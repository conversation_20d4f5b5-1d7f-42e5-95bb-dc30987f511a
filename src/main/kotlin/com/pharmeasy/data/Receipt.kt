package com.pharmeasy.data

import com.pharmeasy.type.AdvancePaymentSource
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.ReceiptStatus
import com.pharmeasy.type.ReceiptType
import org.hibernate.annotations.DynamicUpdate
import org.hibernate.envers.AuditTable
import org.hibernate.envers.Audited
import org.hibernate.envers.NotAudited
import org.hibernate.envers.RelationTargetAuditMode
import java.time.LocalDate
import java.time.LocalDateTime
import javax.persistence.Column
import javax.persistence.Entity
import javax.persistence.EnumType
import javax.persistence.Enumerated
import javax.persistence.FetchType
import javax.persistence.GeneratedValue
import javax.persistence.GenerationType
import javax.persistence.Id
import javax.persistence.JoinColumn
import javax.persistence.OneToOne
import javax.persistence.Table
import javax.persistence.Version

@Entity
@Table(name = "receipt")
@Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
@AuditTable(value = "receipt_history")
@DynamicUpdate
data class Receipt(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long?,
    @Column(name = "receipt_number")
    var receiptNumber: String,
    @Column(name = "created_at")
    var createdAt: LocalDateTime,
    @Column(name = "created_by")
    var createdBy: String,
    @Column(name = "updated_at")
    var updatedAt: LocalDateTime? = LocalDateTime.now(),
    @Column(name = "updated_by")
    var updatedBy: String? = null,
    @Column(name = "payment_transaction_id")
    var paymentTransactionId: String?,
    @Column(name = "remarks")
    var remarks: String? = null,
    @Column(name = "amount")
    var amount: Double? = null,
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    var status: ReceiptStatus = ReceiptStatus.GENERATED,
    @Version
    @Column(name = "version", columnDefinition = "integer DEFAULT 0", nullable = false)
    var version: Int = 0,
    @Column(name = "iteration", columnDefinition = "integer DEFAULT 0", nullable = false)
    var iteration: Int = 0,
    @Column(name = "advance_amount")
    var advanceAmount: Double?,
    @Enumerated(EnumType.STRING)
    @Column(name = "source")
    var source: AdvancePaymentSource = AdvancePaymentSource.SYSTEM,
    @Column(name = "advance_id")
    var advanceId: Long? = null,
    var tenant: String,
    var partnerId: Long,
    var partnerDetailId: Long,
    var unUtilizedAmount: Double,
    var salesmanId: Long? = null,
    var salesmanName: String? = null,
    @Enumerated(EnumType.STRING)
    var paymentType: PaymentType? = null,
    var txReferenceNumber: String? = null,
    var transactionDate: LocalDate? = null,
    var bankName: String? = null,
    var retailerTxnDate: LocalDate? = null,
    var retailerName: String? = null,
    @Enumerated(EnumType.STRING)
    var receiptType: ReceiptType? = null,
    @OneToOne(targetEntity = DraftReceipt::class, fetch = FetchType.LAZY)
    @JoinColumn(name = "draft_receipt_id")
    @NotAudited
    var draftReceipt: DraftReceipt? = null
) {
    constructor(draftReceipt: DraftReceipt, user: String) : this(
        null,
        draftReceipt.receiptNumber,
        LocalDateTime.now(),
        user,
        updatedBy = user,
        paymentTransactionId = draftReceipt.paymentTransactionId,
        remarks = draftReceipt.remarks,
        amount = draftReceipt.amount,
        status = draftReceipt.status,
        advanceAmount = null,
        source = draftReceipt.source,
        tenant = draftReceipt.tenant!!,
        partnerId = draftReceipt.partnerId!!,
        partnerDetailId = draftReceipt.partnerDetailId!!,
        unUtilizedAmount = draftReceipt.unUtilizedAmount!!,
        salesmanId = draftReceipt.salesmanId,
        salesmanName = draftReceipt.salesmanName,
        paymentType = draftReceipt.paymentType,
        txReferenceNumber = draftReceipt.txReferenceNumber,
        transactionDate = draftReceipt.transactionDate,
        bankName = draftReceipt.bankName,
        retailerTxnDate = draftReceipt.retailerTxnDate,
        retailerName = draftReceipt.retailerName,
        receiptType = draftReceipt.receiptType,
        draftReceipt = draftReceipt
    )

    constructor(
        id: Long?, receiptNumber: String, createdAt: LocalDateTime, createdBy: String,
        updatedAt: LocalDateTime? = LocalDateTime.now(), updatedBy: String? = null,
        paymentTransactionId: String?, remarks: String? = null, amount: Double? = null,
        status: ReceiptStatus, advanceAmount: Double = 0.0, iteration: Int, source: AdvancePaymentSource
    ) : this(
        id, receiptNumber, createdAt, createdBy, updatedAt, updatedBy, paymentTransactionId, remarks, amount, status,
        iteration = iteration, advanceAmount = advanceAmount, source = source, tenant = "", partnerId = 0L,
        partnerDetailId = 0L, unUtilizedAmount = 0.0
    )
}
