package com.pharmeasy.data

import com.pharmeasy.type.AdvancePaymentSource
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.ReceiptStatus
import com.pharmeasy.type.ReceiptType
import java.time.LocalDate
import java.time.LocalDateTime

data class ReceiptDto(
    var id: Long? = null,
    var createdBy: String? = null,
    var createdOn: LocalDateTime? = null,
    var updatedBy: String? = null,
    var updatedOn: LocalDateTime? = null,
    var receiptNumber: String? = null,
    var paymentTransactionId: String? = null,
    var remarks: String? = null,
    var amount: Double = 0.0,
    var status: ReceiptStatus = ReceiptStatus.DRAFT,
    var source: AdvancePaymentSource = AdvancePaymentSource.SYSTEM,
    var tenant: String = "",
    var partnerId: Long = 0L,
    var partnerDetailId: Long = 0L,
    var unUtilizedAmount: Double = 0.0,
    var salesmanId: Long? = null,
    var salesmanName: String? = null,
    var paymentType: PaymentType? = null,
    var txReferenceNumber: String? = null,
    var transactionDate: LocalDate? = null,
    var bankName: String? = null,
    var retailerTxnDate: LocalDate? = null,
    var retailerName: String? = null,
    var isBankDeposit: Boolean? = false,
    var bankDepositSlipNo: String? = null,
    var receiptType: ReceiptType? = null,
    var draftReceiptId: Long? = null,
    var approvedBy: String? = null,
    var approvedAt: LocalDateTime? = null,
    var reversedBy: String? = null,
    var settlementCount: Long? = null,
    var settleables: MutableList<DraftReceiptEntityMappingDto> = mutableListOf(),
)
