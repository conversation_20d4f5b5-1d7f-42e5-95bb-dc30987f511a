package com.pharmeasy.data

import com.pharmeasy.type.SettleableType
import javax.persistence.Entity
import javax.persistence.EnumType
import javax.persistence.Enumerated
import javax.persistence.FetchType
import javax.persistence.JoinColumn
import javax.persistence.ManyToOne

@Entity
class DraftReceiptEntityMapping(
    @ManyToOne(targetEntity = DraftReceipt::class, fetch = FetchType.LAZY)
    @JoinColumn(name = "draft_receipt_id", referencedColumnName = "id")
    var receipt: DraftReceipt,
    @Enumerated(EnumType.STRING)
    var entityType: SettleableType,
    var entityId: Long,
    var entityTxAmount: Double,
    var active: Boolean = true
) : BaseEntity() {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as DraftReceiptEntityMapping

        return id == other.id
    }

    override fun hashCode(): Int {
        var result = entityId.hashCode()
        result = 31 * result + entityTxAmount.hashCode()
        result = 31 * result + active.hashCode()
        result = 31 * result + entityType.hashCode()
        return result
    }

}
