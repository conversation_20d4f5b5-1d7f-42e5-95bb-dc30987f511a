package com.pharmeasy.data;

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.pharmeasy.type.ChequeHandleType
import org.hibernate.envers.AuditTable
import org.hibernate.envers.Audited
import org.hibernate.envers.RelationTargetAuditMode
import org.springframework.format.annotation.DateTimeFormat
import java.math.BigDecimal
import java.time.LocalDateTime
import javax.persistence.*

@JsonIgnoreProperties(ignoreUnknown = true)
@Entity
@Audited
@AuditTable(value = "bk_cheque_handle_audit_log")
@Table(name = "bk_cheque_handle")
data class ChequeHandle(
        @Column(name="assign_To")
        var assignTo: String?,

        @Column(name = "cheque_num")
        var chequeNum: String?,

        @Column(name = "settlement_id")
        var settlementId: Long?,

        @Column(name = "advance_payment_id")
        var advancePaymentId: Long?,

        @Column(name = "tenant")
        var tenant: String,

        @Enumerated(EnumType.STRING)
        @Column(name = "status")
        var status: ChequeHandleType?,

        @Column(name = "reason")
        var reason : String? = null,

        @Column(name = "in_approve")
        var inApprove: Boolean,

        @Column(name = "charge")
        var charge: Boolean? = false,

        @Column(name = "charge_amt")
        var chargeAmt: Double? = 0.0,

        @Column(name = "settled")
        var settled: Boolean? = false,

        @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
        @OneToOne(fetch = FetchType.LAZY)
        @JoinColumn(name = "charge_id", nullable = true)
        @JsonIgnore
        var chargeInv: Charges? = null,

        @Column(name="cheque_bounce_date")
        var bounceDate: LocalDateTime? = null,
        val receiptId: Long? = null,

        @Version
        @Column(name = "version", columnDefinition = "integer DEFAULT 0", nullable = false)
        var version: Int = 0,

        @Column(name = "after_clearance_bounce")
        var afterClearanceBounce: Boolean = false


): BaseEntity()
