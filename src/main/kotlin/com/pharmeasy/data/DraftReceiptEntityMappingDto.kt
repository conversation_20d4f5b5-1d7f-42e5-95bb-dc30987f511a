package com.pharmeasy.data

import com.pharmeasy.type.SettleableType
import java.time.LocalDateTime

/**
 * DTO for {@link com.pharmeasy.data.DraftReceiptEntityMapping}
 */
data class DraftReceiptEntityMappingDto(
    var id: Long? = null,
    var entityType: SettleableType,
    var entityId: Long,
    var entityTxAmount: Double,
    var active: Boolean = true,
    val openAmount: Double? = null,
    var createdBy: String? = null,
    var createdOn: LocalDateTime? = null,
    var updatedBy: String? = null,
    var updatedOn: LocalDateTime? = null,
)
