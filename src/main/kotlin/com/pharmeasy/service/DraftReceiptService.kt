package com.pharmeasy.service

import com.pharmeasy.data.DraftReceipt
import com.pharmeasy.data.DraftReceiptEntityMappingDto
import com.pharmeasy.data.ReceiptDto
import com.pharmeasy.model.InvoiceStatus
import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.repo.DraftReceiptRepository
import com.pharmeasy.dto.ReceiptListFilter
import com.pharmeasy.dto.ReceiptStatusUpdateDto
import com.pharmeasy.exception.RequestException
import com.pharmeasy.type.AdvancePaymentSource
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.time.LocalDateTime
import javax.transaction.Transactional
import com.pharmeasy.mapper.ReceiptMapper
import com.pharmeasy.repo.DraftReceiptEntityMappingRepository
import com.pharmeasy.type.ReceiptStatus
import org.springframework.data.domain.Slice
import org.springframework.data.repository.findByIdOrNull

@Service
class DraftReceiptService(
    private val settleableProcessorFactory: SettleableProcessorFactory,
    private val draftReceiptRepo: DraftReceiptRepository,
    private val mapper: ReceiptMapper,
    private val draftReceiptEntityMappingRepo: DraftReceiptEntityMappingRepository
) {
    val source: AdvancePaymentSource = AdvancePaymentSource.RIO_PAY

    companion object {
        private val log = LoggerFactory.getLogger(DraftReceiptProcessingStrategyImpl::class.java)
    }

    @Transactional
    fun saveDraftReceipt(draft: DraftReceipt): DraftReceipt {
        return draftReceiptRepo.save(draft)
    }

    fun getReceiptByPaymentTransactionId(txId: String): DraftReceipt? {
        val receipt = draftReceiptRepo.findByPaymentTransactionId(txId)
        return receipt
    }

    fun findDuplicatesByPaymentTransactionId(
        paymentTransactionId: String,
        partnerDetailId: Long,
        tenant: String
    ): List<DraftReceipt> {
        return draftReceiptRepo.findDuplicatesByPaymentTransactionId(
            paymentTransactionId,
            partnerDetailId,
            tenant
        )
    }

    fun findCashTotalForPartner(
        partnerDetailId: Long,
        startDate: LocalDateTime,
        endDate: LocalDateTime
    ): Double {
        return draftReceiptRepo.findCashTotalForPartner(partnerDetailId, startDate, endDate)
    }

    fun findDuplicateCheque(
        chequeNo: String,
        chequeDate: LocalDate,
        partnerDetailId: Long,
        tenant: String,
        bankName: String
    ): List<DraftReceipt> {
        return draftReceiptRepo.findDuplicateCheque(
            chequeNo,
            chequeDate,
            partnerDetailId,
            tenant,
            bankName
        )
    }

    fun findDuplicateNeftPayments(
        neftId: String,
        partnerDetailId: Long,
        tenant: String,
        neftDate: LocalDate
    ): List<DraftReceipt> {
        return draftReceiptRepo.findDuplicateNeftPayments(
            neftId,
            partnerDetailId,
            tenant,
            neftDate
        )
    }

    fun save(receipt: DraftReceipt): DraftReceipt {
        return draftReceiptRepo.save(receipt)
    }

    /**
     * Saves the draft receipt and its entity mappings.
     * If the settlement amount is higher than the outstanding amount, it will be adjusted accordingly.
     */
    @Transactional
    fun updateSettleableMappings(
        draftReceipt: DraftReceipt,
        payment: PaymentInfo,
        partnerInfo: PartnerInfo
    ) {
        payment.settleables.forEach {
            val settleableType = it.type.settleableType
            val settleableProcessor = settleableProcessorFactory.getSettleableProcessor(settleableType)
            val settleableDetails = settleableProcessor.getSettleableDetails(it.number, partnerInfo.tenant)
            requireNotNull(settleableDetails)

            val isPaidOff = settleableDetails.pendingAmount <= 0.0 || settleableDetails.status in listOf(
                InvoiceStatus.PAID,
                InvoiceStatus.WRITE_OFF
            )

            // If the invoice is paid off, no need to settle. Handling softly instead of throwing an error
            if(isPaidOff){
                return
            }

            // Settle the amount up to the outstanding amount
            val amountToBeSettled = (it.amount).coerceAtMost(settleableDetails.pendingAmount)
            draftReceipt.addSettleableMapping(settleableDetails, amountToBeSettled)
        }
    }

    fun getDraftReceiptsByFilter(receiptListFilter: ReceiptListFilter): Slice<ReceiptDto> {
        val dtos = draftReceiptRepo.getByReceiptFilter(receiptListFilter).map {
            mapper.toDto(it)
        }

        val draftIds = dtos.filter { it.status == ReceiptStatus.DRAFT }.map { it.id!! }.toList()
        val settlementCounts =
            draftReceiptEntityMappingRepo.countByReceiptIds(draftIds).associate { it[0] as Long to it[1] as Long }
        dtos.onEach { it.settlementCount = settlementCounts[it.id] ?: 0 }
        return dtos
    }

    fun updateStatus(draftReceiptId: Long, receiptStatusUpdateDto: ReceiptStatusUpdateDto): DraftReceipt {
        val draftReceipt = draftReceiptRepo.findByIdOrNull(draftReceiptId)
            ?: throw RequestException("Draft receipt not found for id $draftReceiptId")
        if(draftReceipt.status != receiptStatusUpdateDto.status) {
            draftReceipt.status = receiptStatusUpdateDto.status
            draftReceipt.remarks = receiptStatusUpdateDto.remarks
            draftReceiptRepo.save(draftReceipt)
        }
        return draftReceipt
    }

    fun getById(id: Long): DraftReceipt? {
        return draftReceiptRepo.findByIdOrNull(id)
    }

    @Transactional
    fun updateDraftReceipt(
        draftReceipt: DraftReceipt,
        settleableMappings: List<DraftReceiptEntityMappingDto>
    ) {
        if(settleableMappings.isNotEmpty()){
            draftReceipt.updateSettleableMapping(settleableMappings)
        }
        saveDraftReceipt(draftReceipt)
    }
}
