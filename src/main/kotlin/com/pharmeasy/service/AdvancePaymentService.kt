package com.pharmeasy.service

import com.pharmeasy.data.*
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.model.advancepayment.*
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.repo.advancepayment.AdvancePayLinksDataRepo
import com.pharmeasy.repo.advancepayment.AdvancePaymentRepo
import com.pharmeasy.repo.advancepayment.AdvanceSettlementMappingRepo
import com.pharmeasy.specification.AdvancePaymentSpecification
import com.pharmeasy.type.*
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVPrinter
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.dao.PessimisticLockingFailureException
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.io.ByteArrayOutputStream
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

@Service
class AdvancePaymentService(@Value("\${app.url}") val url: String) {

    companion object {
        private val log = LoggerFactory.getLogger(AdvancePaymentService::class.java)
    }

    @Autowired
    private lateinit var advancePaymentRepo : AdvancePaymentRepo

    @Autowired
    private lateinit var advanceSettlementRepo: AdvanceSettlementMappingRepo

    @Autowired
    private lateinit var supplierProxy: SupplierProxy

    @Autowired
    private lateinit var adjustmentService: AdjustmentService

    @Autowired
    private lateinit var documentMasterService: DocumentMasterService

   @Autowired
    private lateinit var vendorFileService: VendorFileService

    @Autowired
    private lateinit var s3FileUtilityService : S3FileUtilityService

    @Autowired
    private lateinit var advancePayLinksDataRepo: AdvancePayLinksDataRepo

    @Autowired
    private lateinit var checkerService: CheckerService

    @Autowired
    private lateinit var companyService: CompanyService

    @Autowired
    private lateinit var chequeHandleService: ChequeHandlingService

    @Autowired
    private lateinit var receiptService: ReceiptService

    @Autowired
    private lateinit var partnerService: PartnerService

    @Autowired
    private lateinit var settlementService: SettlementService

    @Autowired
    private lateinit var vaultCheckerService: CheckerService


    @Transactional
    fun createAdvancePayment(advancePaymentDto: AdvancePaymentDto, userId: String, ds: String? = null): AdvancePayment{
        val companyMappingObj = companyService.getCompanyTenantMappingObject(ds?:advancePaymentDto.tenant)
                ?: throw RequestException("no tenant mapping found for ${advancePaymentDto.tenant}")

        val advancePayRefList : MutableList<AdvancePayRefMapping> = mutableListOf()

        advancePaymentDto.refDocuments.forEach{ paymentRefItem ->
            val advancePayRef = AdvancePayRefMapping(0, LocalDateTime.now(), LocalDateTime.now(),paymentRefItem.referenceNumber,
                    paymentRefItem.referenceDate,null)
            advancePayRefList.add(advancePayRef)
        }
        val advancePayment = AdvancePayment(
                id = 0,
                createdOn = LocalDateTime.now(),
                updatedOn = LocalDateTime.now(),
                createdBy = userId,
                createdByName = advancePaymentDto.createdByName,
                updatedBy = userId,
                userEmail = advancePaymentDto.userEmail,
                assignedTo = null,
                assignedToId = null,
                approvalDate = null,
                vendorName = advancePaymentDto.partnerName,
                documentId = null,
                typeOfAdvance = advancePaymentDto.typeOfAdvance,
                type = advancePaymentDto.type,
                status = Status.PENDING_APPROVAL,
                amount = advancePaymentDto.amount,
                amountPending = advancePaymentDto.amount,
                partnerId = advancePaymentDto.partnerId,
                partnerDetailId = advancePaymentDto.partnerDetailId,
                tenant = companyMappingObj.tenant,
                companyId = companyMappingObj.companyId,
                client = advancePaymentDto.client,
                remarks = advancePaymentDto.remarks,
                refDocuments = advancePayRefList,
                source = advancePaymentDto.source
        )

        advancePayment.refDocuments.forEach { refDocument ->
            refDocument.advancePayment = advancePayment
        }

        val checkers = checkerService.findChecker(advancePaymentDto.tenant)

        if (checkers.isNotEmpty()) {
            val checker = checkers[0]
            advancePayment.assignedTo = checker?.userName
            advancePayment.assignedToId = checker?.userId

        } else {
            throw RequestException("No checker found for this company !")
        }
        val advancePay = advancePaymentRepo.save(advancePayment)
        return advancePay
    }


    fun getAllAdvancePayment(page: Int?, size: Int?, documentId: String?, referenceNumber: String?, partnerId: Long?,
                             from: LocalDate?, to: LocalDate?, transactionFrom: LocalDate?, transactionTo: LocalDate?,
                             status: MutableList<String?>?, assignedTo: String?, id: Long?, type: AdvanceType?, tenant: String, ds: String? = null, partnerDetailId:Long?): PaginationDto {


        val sortBy = if (transactionFrom != null && transactionTo != null) "approvalDate" else "updatedOn"
        val pagination = PageRequest.of(page ?: 0, size ?: 10, Sort.Direction.DESC, sortBy)

        val statusList: MutableList<Status?> = mutableListOf()

        if (!status.isNullOrEmpty()) {

            status.forEach {
                if (it != null) statusList.add(Status.valueOf(it.uppercase()))
            }
        }
        val companyMappingObj: CompanyTenantMapping? = companyService.getCompanyTenantMappingObject(ds?:tenant)
                ?: throw RequestException("No tenant mapping found for $tenant ")
        val res = advancePaymentRepo.findAll(AdvancePaymentSpecification(documentId,referenceNumber,partnerId,from,to,transactionFrom,transactionTo,
                statusList,assignedTo,companyMappingObj?.companyId,id,type,null, partnerDetailId), pagination)
        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), res.content)
    }

    fun getAllVendorAdvancePayment(page: Int?, size: Int?, partnerIds: List<Long>?, tenant: String, ds: String? = null,client:List<InvoiceType>, partnerDetailId: Long?): PaginationDto {
        val pagination = PageRequest.of(page ?: 0, size ?: 10)

        val tenants = companyService.findTenants(ds?:tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")

        val type = PartnerType.CUSTOMER
        var res = if(partnerDetailId != null){
            advancePaymentRepo.getVendorAdvancePaymentWithPartnerDetailId(tenants,partnerDetailId,client,type,pagination)
        }else if (partnerIds.isNullOrEmpty()) {
            advancePaymentRepo.getVendorAdvancePayment(tenants,client,type,pagination)
        }else{
            advancePaymentRepo.getVendorAdvancePaymentWithPartnerIds(tenants,partnerIds,client,type,pagination)
        }

        res.forEach { it ->
            if (it.vendorId != null) {
                val supplierList = supplierProxy.supplier(listOf(it.vendorId))
                val supplier = if (supplierList.isNotEmpty()) supplierList[0] else null
                if (supplier != null) it.vendorName = supplier.partnerName!!
            }
        }
        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), res)
    }

    fun getAggregatedAdvPayment(partnerIds: List<Long>?, tenant: String,client:List<InvoiceType>,partnerDetailId:Long?): AggregatedAdvancePaymentDto {
        val tenants = companyService.findTenants(tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")
        val res = if (partnerIds.isNullOrEmpty() && partnerDetailId == null) {
            advancePaymentRepo.getAggregatedAdvPayment(tenants,client)
        } else if(partnerDetailId != null){
            advancePaymentRepo.getAggregatedAdvPaymentWithPartnerDetailId(tenants,partnerDetailId,client)
        }else {
            advancePaymentRepo.getAggregatedAdvPaymentWithPartnerIds(tenants,partnerIds,client)
        }
        return res
    }

    fun getVendorAdvancePayment(page: Int?, size: Int?, partnerId: Long, status: NoteStatus?, documentId: String?, refDocumentNum: String?,
                                transactionFrom: LocalDate?, transactionTo: LocalDate?, type: AdvanceType?, tenant: String, ds: String? = null): PaginationDto {

        val tenants = companyService.findTenants(ds?:tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")

        val sortBy = if (transactionFrom != null && transactionTo != null) "approvalDate" else "updatedOn"
        val pagination = PageRequest.of(page ?: 0, size ?: 10, Sort.Direction.DESC, sortBy)

        var res: Page<AdvancePayment>
        var flag = false
        val customerType = PartnerType.CUSTOMER
        if (status != null){
            if(status.name == NoteStatus.REALIZED.name){
                flag = true
            }
            res = advancePaymentRepo.getAdvancePayPendingOrRealized(partnerId,documentId,refDocumentNum,
                    transactionFrom,transactionTo,flag,type,tenants, customerType,pagination)
        }else{
            res = advancePaymentRepo.getAdvancePaySupplierTotal(partnerId,documentId,refDocumentNum,
                                                                        transactionFrom,transactionTo,type,tenants, customerType,pagination)
        }

        res.content.forEach { advancePayment ->
            if(advancePayment.approvalDate!=null){
                val noOfDaysBetween = ChronoUnit.DAYS.between(advancePayment.approvalDate, LocalDate.now())
                advancePayment.overDue = noOfDaysBetween
            }
        }
        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), res.content)
    }

    @Transactional
    fun checkerAdvancePayment(advancePaymentId: Long, updateAdvancePayment: UpdateAdvancePaymentDto, userId: String, ds: String? = null): Result {
        val checkers = checkerService.findCheckers(updateAdvancePayment.tenant, null, ds)
        val advancePayment = advancePaymentRepo.findByIdOrNull(advancePaymentId)
            ?: throw RequestException("id $advancePaymentId is not present in our DB!")

        if (userId == advancePayment.createdBy) {
            throw RequestException("Cannot approve the request as created by you")
        }
        if (advancePayment.status == Status.APPROVED || advancePayment.status == Status.REJECTED) {
            return Result(200, "SUCCESS")
        }
        if (checkers.isNotEmpty()) {
            val checker = checkerService.compareChecker(checkers, userId)
            if (userId != checker?.userId) throw RequestException("Users not having checker rights, cannot update cheque status. Please request Checker!")
            if (advancePayment.assignedToId != userId) throw RequestException("You are not assigned to change status of this entry!!")

            try {
                if (updateAdvancePayment.change)
                    updateAdvPaymentStatus(userId, advancePayment, updateAdvancePayment, checkers, ds)
                else {
                    advancePayment.status = Status.REJECTED
                    advancePayment.updatedOn = LocalDateTime.now()
                    if (!updateAdvancePayment.remarks.isNullOrEmpty()) advancePayment.remarks = updateAdvancePayment.remarks
                    advancePaymentRepo.save(advancePayment)
                }
            } catch (e: PessimisticLockingFailureException) {
                throw RequestException("The record was already processed: ${e.message}")
            }
        } else {
            throw RequestException("No checker found for this company!")
        }
        return Result(200, "SUCCESS")
    }

    private fun updateAdvPaymentStatus(userId: String, advancePayment: AdvancePayment, updateAdvancePayment: UpdateAdvancePaymentDto,
                                       checkers: MutableList<CheckerDetails?>,ds: String? = null) {

        if (checkers.isNotEmpty()) {
            val checker = checkerService.compareChecker(checkers, userId)
            if (checker == null) throw RequestException("Users not having checker rights, cannot update cheque status. Please request Checker!")
            val userFlag = updateAdvancePayment.isRioTransaction?:false
            val adjustmentObj = AdjustmentEntry(
                0,
                LocalDateTime.now(),
                LocalDateTime.now(),
                if(userFlag){"SYSTEM"}else {userId},
                if(userFlag){"SYSTEM"}else {userId},
                if(userFlag){"SYSTEM"}else { checker.userName },
                if(userFlag){"SYSTEM"}else {checker.userId},
                if(userFlag){"SYSTEM"}else {userId},
                LocalDate.now(),
                advancePayment.vendorName,
                if (advancePayment.type == PartnerType.VENDOR) LedgerEntryType.DEBIT else LedgerEntryType.CREDIT,
                null,
                DocumentType.ADVANCE_PAYMENT,
                advancePayment.type,
                Status.APPROVED,
                updateAdvancePayment.paymentType!!.name,
                updateAdvancePayment.amount,
                updateAdvancePayment.amount,
                BigDecimal.ZERO,
                advancePayment.partnerId,
                advancePayment.partnerDetailId,
                advancePayment.tenant,
                checker.companyId,
                "",
                advancePayment.client
            )

            val tenants = companyService.findTenants(ds ?: updateAdvancePayment.tenant)
            val company = companyService.getCompanyByTenant(tenants[0] ?: updateAdvancePayment.tenant)
                ?: throw RequestException("Company mapping not found for ${updateAdvancePayment.tenant}")

            val documentId = documentMasterService.getDocumentNumber(
                adjustmentObj.updatedBy!!,
                company.companyCode,
                DocumentType.ADVANCE_PAYMENT
            )
            adjustmentObj.documentId = documentId
            val referenceNumber =
                if (advancePayment.refDocuments.isEmpty()) "" else advancePayment.refDocuments[0].referenceNumber
            if (updateAdvancePayment.paymentType != PaymentType.CHEQUE && advancePayment.source != AdvancePaymentSource.RIO_COLLECTIONS && advancePayment.source != AdvancePaymentSource.RIO_PAY) {
                adjustmentService.addAdjustmentToLedger(adjustmentObj, referenceNumber, null)
                adjustmentService.updateVendorBalance(adjustmentObj)
            }

            advancePayment.status =
                if (updateAdvancePayment.paymentType == PaymentType.CHEQUE && advancePayment.source != AdvancePaymentSource.RIO_COLLECTIONS) Status.PENDING_CHEQUE_CLEARANCE else Status.APPROVED
            advancePayment.amount = updateAdvancePayment.amount
            advancePayment.amountPending = updateAdvancePayment.amount
            advancePayment.paymentType = updateAdvancePayment.paymentType
            advancePayment.paymentReference = updateAdvancePayment.paymentReference
            advancePayment.paymentDate = updateAdvancePayment.paymentDate
            advancePayment.chequeDate = updateAdvancePayment.paymentDate
            advancePayment.documentId = documentId
            advancePayment.bankName = updateAdvancePayment.bankName
            advancePayment.approvalDate = LocalDate.now()
            advancePayment.updatedBy = if(userFlag){"SYSTEM"}else {userId}
            advancePayment.assignedTo = if(userFlag){"SYSTEM"}else {advancePayment.assignedTo}
            advancePayment.assignedToId = if(userFlag){"SYSTEM"}else {advancePayment.assignedToId}
            advancePayment.updatedOn = LocalDateTime.now()
            advancePaymentRepo.save(advancePayment)
            val save = advancePaymentRepo.save(advancePayment)

            if (updateAdvancePayment.paymentType == PaymentType.CHEQUE && advancePayment.source != AdvancePaymentSource.RIO_COLLECTIONS) {
                val chequeObj = CreateChequeHandleDto(
                    save.tenant,
                    save.paymentReference ?: "",
                    null,
                    save.id,
                    save.paymentDate
                )
                if (chequeHandleService.validateNewEntry(
                        chequeObj.chequeNumber,
                        advancePayment.partnerDetailId!!,
                        advancePayment.paymentDate!!,
                        advancePayment.tenant
                    )
                ) {
                    chequeHandleService.addChequeHandleEntry(userId, chequeObj)
                }

            }
        }
    }

    fun getAdvancePayUrl(tenant: String, createdBy: String, ds: String? = null) : CreateResultData {
        val tenants = companyService.findTenants(tenant)

        if (tenants.isEmpty()) return CreateResultData(200, "No Data", null)
        val list = advancePaymentRepo.getAdvancePaymentDataForUrl(tenants)
        if (list.isNullOrEmpty())
            throw RequestException("Data is not available")

        var listVendorId: List<Long?>? = null

        list.forEach { vendAdvPayDto ->

            if (vendAdvPayDto != null) {
                if (vendAdvPayDto.vendorId != null)
                    listVendorId = listOf(vendAdvPayDto.vendorId)

                val vendor = supplierProxy.supplier(listVendorId)
                vendor.forEach { v ->
                    if (v != null) {
                        vendAdvPayDto.vendorName = v.partnerName
                    }
                }
            }
        }
        val prefix = "AdvancePayList-"

        try {
            val file = vendorFileService.saveReport(tenant, VendorDataLinks(null, LocalDateTime.now(), null, "IN_PROGRESS", VendorDataEnum.ADVANCE_PAYMENT, null, createdBy,tenant,false))
            writeAdvancePayData(file.id!!, list, tenant, prefix)

        } catch (e: Exception) {
            e.printStackTrace()
        }

        return CreateResultData(200, "Success", null)
    }

    @Async
    fun writeAdvancePayData(id: Long, advPayList: MutableList<VendorAdvancePaymentDto?>, tenant: String, prefix: String) {
        val csvPrinter: CSVPrinter
        val bytes = ByteArrayOutputStream()

        csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT
                    .withHeader("Vendor Id", "Vendor Name", "Total No. Of Advances", "No. Of Realized Advances",
                                        "No. Of Pending/Partial Advances", "Pending/Partial Advances AMT"))
        advPayList.forEach { it ->
            if(it!=null)
                csvPrinter.printRecord(it.vendorId, it.vendorName, it.totalAdvancePayment,
                        it.totalRealizedAdvancePayment, it.totalPendingAdvancePayment,
                        it.totalPendingAdvancePayAmount?.setScale(2, RoundingMode.HALF_EVEN))
        }
        csvPrinter.flush()
        val uploaded = s3FileUtilityService.uploadFile(bytes.toByteArray(), "CSV", prefix)
        vendorFileService.update(id, uploaded ?: "Failed")
    }

    fun advancePayDownload(tenant: String, createdBy: String, ds: String? = null): CreateResultData {
        val file = advancePayLinksDataRepo.getAdvancePayLink(createdBy,tenant,false)
        if (file.isNotEmpty()) {
            return CreateResultData(200, "Success", file[0]!!.link)
        }
        return CreateResultData(200, "Success", null)
    }

    fun getAdvancePyDetailUrl(partnerId: Long, tenant: String, createdBy: String, documentId: String?, refDocumentNum: String?,
                              transactionFrom: LocalDate?, transactionTo: LocalDate?, type: AdvanceType?, consumed: Boolean?, ds: String? = null, partnerDetailId: Long?) : CreateResultData{
        val tenants = companyService.findTenants(tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")

        val statuses : MutableList<Status?> = mutableListOf()
        statuses.add(Status.APPROVED)

        val list = advancePaymentRepo.findAll( AdvancePaymentSpecification( documentId,refDocumentNum,partnerId,null, null,
                                                                            transactionFrom,transactionTo,statuses,null,null,null,type,consumed, partnerDetailId) )
        if (list.isEmpty())
            throw RequestException("Data is not available")

        list.forEach { advancePayment ->
            if(advancePayment.approvalDate!=null){
                val noOfDaysBetween = ChronoUnit.DAYS.between(advancePayment.approvalDate, LocalDate.now())
                advancePayment.overDue = noOfDaysBetween
                log.debug("noOfDaysBetween{}", noOfDaysBetween)
            }
        }

        val prefix = "AdvancePaymentDetails-"
        try {
            val file = vendorFileService.saveReport(tenant, VendorDataLinks(null, LocalDateTime.now(), null, "IN_PROGRESS", VendorDataEnum.ADVANCE_PAY_DETAIL, null, createdBy,tenant, false))

            writeAdvancePayDetailData(file.id!!, list, tenant, prefix)

        } catch (e: Exception) {
            e.printStackTrace()
        }
        return CreateResultData(200, "Success", null)

    }

    @Async
    fun writeAdvancePayDetailData(id: Long, list: List<AdvancePayment?>, tenant: String, prefix: String) {
        val csvPrinter: CSVPrinter
        val bytes = ByteArrayOutputStream()

        csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT
                .withHeader("Advance Pay Doc. No.", "Transaction Date", "Type", "Reference Doc. No.", "Total Amount", "Open Amount", "Overdue No. Of Days", "Status"))

        list.forEach { it ->
            if(it!=null){
                val status = if (it.consumed == true) NoteStatus.REALIZED else NoteStatus.PENDING
                csvPrinter.printRecord(it.documentId, it.approvalDate, it.typeOfAdvance, it.refDocuments[0].referenceNumber,
                        it.amount,it.amountPending,it.overDue, status)
            }
        }
        csvPrinter.flush()
        val uploaded = s3FileUtilityService.uploadFile(bytes.toByteArray(), "CSV", prefix)
        vendorFileService.update(id, uploaded ?: "Failed")
    }

    fun advancePayDetailDownload(tenant: String, createdBy: String, ds: String? = null): CreateResultData {
        val file = advancePayLinksDataRepo.getAdvancePayDetailLink(tenant,createdBy,false)
        if (file.isNotEmpty()) {
            return CreateResultData(200, "Success", file[0]!!.link)
        }
        return CreateResultData(200, "Success", null)
    }

    @Transactional
    fun updateAdvancePaymentChecker(id: Long, checkerId: Long, tenant: String, userId: String, ds: String? = null): Result {
        val checker = checkerService.findCheckers(tenant,checkerId,ds)

        if(checker.isEmpty())
            throw RequestException("No active checker found with id  $checkerId , for logged in company!")

        if(checker.size>1)
            throw RequestException("More than 1 checker found $checkerId , for logged in company!")

        val advancePayObj = advancePaymentRepo.get(id) ?: throw RequestException("No advance Pay entry found with id $id !")

        if(advancePayObj.status != Status.PENDING_APPROVAL) throw RequestException("Advance Payment with id $id is not in PENDING_APPROVAL state!!")

        val companyId = companyService.getCompanyTenantMappingObject(ds?:tenant)?.companyId
            ?: throw RequestException("Company not found for the given tenant")

        val checkerUserIdList = vaultCheckerService.getCheckerByCompanyId(companyId).mapNotNull { it?.userId }.toMutableList()

        if(userId != advancePayObj.createdBy && userId !in checkerUserIdList){
            throw RequestException("not a valid user cannot update")
        }

        advancePayObj.assignedToId = checker[0]?.userId
        advancePayObj.assignedTo = checker[0]?.userName
        advancePayObj.updatedBy = userId

        advancePaymentRepo.save(advancePayObj)

        return success
    }

    @Transactional
    fun cancelAdvancePayment(userId: String, advanceId:Long):Result{
        val advanceObj = advancePaymentRepo.get(advanceId)?:
        throw RequestException("Advance Payment id : $advanceId not found!")
        val checkers = checkerService.findCheckers(advanceObj.tenant,null,null)
        if (checkers.isNotEmpty()) {
            checkerService.compareChecker(checkers, userId)?: throw RequestException(
                "Users not having checker rights," +
                        "cannot update status. Please request Checker!"
            )
        }

        if(advanceObj.consumed == true || advanceObj.amountPending != advanceObj.amount)
            throw RequestException("Cancellation failed. Advance payment used in settlement! ")
        if(advanceObj.status == Status.CANCELLED)
            throw RequestException("Advance payment already in CANCELLED state! ")

        if (advanceObj.status == Status.APPROVED){
            val companyCode = companyService.getCompanyCodeByTenant(advanceObj.tenant)?:
            throw RequestException("company code not found!")
            createReversalLedgerEntry(advanceObj,userId,companyCode)
        }
        advanceObj.status = Status.CANCELLED
        advanceObj.updatedBy = userId
        advanceObj.updatedOn = LocalDateTime.now()
        advancePaymentRepo.save(advanceObj)
        return  success
    }
    fun createReversalLedgerEntry(advancePayment: AdvancePayment, userId:String, companyCode:String){

        val adjustmentObj = AdjustmentEntry(0, LocalDateTime.now(), LocalDateTime.now(), userId, userId, userId,userId, userId, LocalDate.now(), advancePayment.vendorName, if (advancePayment.type == PartnerType.CUSTOMER) LedgerEntryType.DEBIT else LedgerEntryType.CREDIT, null, DocumentType.PAYMENT_REVERSAL, advancePayment.type, Status.APPROVED,
            "advance payment reversal", advancePayment.amount, BigDecimal.ZERO, BigDecimal.ZERO, advancePayment.partnerId, advancePayment.partnerDetailId, advancePayment.tenant, advancePayment.companyId, "", advancePayment.client)
        adjustmentObj.documentId = documentMasterService.getDocumentNumber(adjustmentObj.updatedBy!!, companyCode, DocumentType.PAYMENT_REVERSAL)
        adjustmentService.addAdjustmentToLedger(adjustmentObj, advancePayment.documentId)
        adjustmentService.updateVendorBalance(adjustmentObj)

    }

    @Transactional
    fun changeAdvancePaymentStatus(advancePaymentId: Long,status:Status):AdvancePayment{
        val advancePayment = advancePaymentRepo.findByIdOrNull(advancePaymentId)?:
        throw RequestException("id $advancePaymentId is not present in our DB!")
        if(status == Status.APPROVED){
            val company = companyService.getCompanyByTenant(advancePayment.tenant)
                ?: throw RequestException("Company mapping not found for ${advancePayment.tenant}")
            val adjustmentObj = AdjustmentEntry(0, LocalDateTime.now(), LocalDateTime.now(), advancePayment.createdBy, advancePayment.createdBy, advancePayment.assignedTo, advancePayment.assignedToId, advancePayment.createdBy,
                LocalDate.now(), advancePayment.vendorName, if(advancePayment.type == PartnerType.VENDOR)LedgerEntryType.DEBIT else LedgerEntryType.CREDIT, advancePayment.documentId, DocumentType.ADVANCE_PAYMENT, advancePayment.type,
                Status.APPROVED, advancePayment.paymentType!!.name, advancePayment.amount,advancePayment.amount, BigDecimal.ZERO, advancePayment.partnerId, advancePayment.partnerDetailId, advancePayment.tenant, company.id, "", advancePayment.client)
            val referenceNumber = if (advancePayment.refDocuments.isEmpty()) "" else advancePayment.refDocuments[0].referenceNumber
            adjustmentService.addAdjustmentToLedger(adjustmentObj, referenceNumber, null)
            adjustmentService.updateVendorBalance(adjustmentObj)
        }
        advancePayment.status = status
        return advancePaymentRepo.save(advancePayment)
    }

    @Transactional
    fun autoCreateAndApproveAdvancePayment(advanceAmount: Double, bkInvoice: BkInvoice, settlement: Settlement, userName: String, userEmail: String, chequeDate: LocalDate?, ds: String? = null): Long{
        val advancePaymentDto = AdvancePaymentDto(
            amount = advanceAmount.toBigDecimal(),
            partnerId = bkInvoice.partnerId!!,
            partnerName = bkInvoice.supplierName!!,
            partnerDetailId = bkInvoice.partnerDetailId,
            tenant = bkInvoice.tenant!!,
            type = bkInvoice.type,
            client = bkInvoice.client,
            typeOfAdvance = AdvanceType.INVOICE,
            remarks = "CREATED FROM RIO COLLECTIONS",
            createdByName = userName,
            userEmail = userEmail,
            refDocuments = listOf(PaymentRefItem(settlement.paymentReference, settlement.paymentDate!!)),
            source = AdvancePaymentSource.RIO_COLLECTIONS
        )

        val advancePayment = createAdvancePayment(advancePaymentDto, "SYSTEM", null)
        val updateAdvancePaymentDto = UpdateAdvancePaymentDto(
            amount = advancePayment.amount,
            paymentReference = settlement.paymentReference,
            paymentType = settlement.paymentType,
            paymentDate = settlement.paymentDate,
            chequeDate = chequeDate,
            bankName = settlement.bankName,
            remarks = "APPROVED VIA RIO COLLECTIONS",
            tenant = settlement.tenant,
            change = true,
            source = AdvancePaymentSource.RIO_COLLECTIONS
        )
        checkerAdvancePayment(advancePayment.id!!, updateAdvancePaymentDto, advancePayment.assignedToId!!, ds)

        return advancePayment.id!!
    }

    @Transactional
    fun checkAndUpdateLedgerForAdvancePayment(user: String, referenceNumber: String) {

        val advancePayment = getAdvancePaymentByDocumentNumber(referenceNumber)
        val receipt = receiptService.getReceiptByAdvancePaymentId(advancePayment.id!!)
        val existingLedgerAmount = partnerService.getExistingLedgerAmount(
            advancePayment.partnerDetailId!!,
            advancePayment.type,
            advancePayment.tenant,
            advancePayment.documentId!!,
            receipt?.receiptNumber
        )
        if (isLedgerAlreadyExist(existingLedgerAmount, advancePayment.amount ?: BigDecimal.ZERO, receipt?.amount)) {
            throw RequestException("Ledger already exist for transaction $referenceNumber")
        }
        val ledgerDto = VendorLedgerDto(
            transactionDate = LocalDate.now(),
            vendorId = advancePayment.partnerId,
            vendorName = advancePayment.vendorName ?: "",
            ledgerEntryType = LedgerEntryType.CREDIT,
            documentType = DocumentType.ADVANCE_PAYMENT,
            documentNumber = advancePayment.documentId!!,
            referenceNumber = advancePayment.documentId,
            externalReferenceNumber = receipt?.receiptNumber,
            particulars = "MISSED ADVANCE PAYMENT LEDGER UPDATE AGAINST RECEIPT NUMBER ${receipt?.receiptNumber}",
            debitAmount = BigDecimal.ZERO,
            creditAmount = advancePayment.amount,
            partnerDetailId = advancePayment.partnerDetailId,
            partnerId = advancePayment.partnerId,
            tenant = advancePayment.tenant,
            type = advancePayment.type,
            client = advancePayment.client,
            remark = advancePayment.remarks
        )
        partnerService.addVendorLedgerEntry(user, ledgerDto)
    }

    private fun isLedgerAlreadyExist(
        existingLedgerAmount: Double,
        advancePaymentAmount: BigDecimal,
        receiptAmount: Double?
    ): Boolean {
        val existingAmount = BigDecimal(existingLedgerAmount)
        val isAmountMatching = existingAmount.compareTo(advancePaymentAmount) == 0
        val isReceiptMatching = receiptAmount?.let { existingAmount.compareTo(BigDecimal(it)) == 0 } ?: false

        return (existingLedgerAmount > 0.0) && (isAmountMatching xor isReceiptMatching)
    }

    fun getAdvanceOutstanding(pdi: Long, tenant: String): BigDecimal {

        return advancePaymentRepo.getAdvancePaymentOutstanding(tenant, pdi) ?: BigDecimal.ZERO
    }

    fun reverseAdvancePaymentSettlements(advancePayment: AdvancePayment,userId:String) {
        if (advancePayment.amount != advancePayment.amountPending) {
            var settlementList = advanceSettlementRepo.getAllSettlementsByAdvancePaymentId(advancePayment.id!!)
            if (settlementList.isNotEmpty()) {
                settlementList.forEach {
                    settlementService.reverseSettlementAndInvoice(userId, it)
                }

            }
        }
    }
    fun getAdvancePaymentById(id: Long): AdvancePayment{
        return advancePaymentRepo.findByIdOrNull(id) ?: throw RequestException("Advance Payment not found for id: $id")
    }

    fun getAdvancePaymentByDocumentNumber(documentNumber: String): AdvancePayment{
        return  advancePaymentRepo.getByDocumentId(documentNumber) ?: throw RequestException("Advance Payment not found for documentNumber: $documentNumber")
    }
}
