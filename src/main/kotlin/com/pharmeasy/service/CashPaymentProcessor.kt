package com.pharmeasy.service

import com.pharmeasy.data.DraftReceipt
import com.pharmeasy.dto.ReceiptUpdateDto
import com.pharmeasy.service.abstracts.PaymentProcessor
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.time.LocalDate

@Service
class CashPaymentProcessor(
    private val draftReceiptService: DraftReceiptService,
    @Value("\${app.maxCashLimit}") private val cashThreshold: Double
) : PaymentProcessor(draftReceiptService) {
    /**
     * Validates the payment details for cash transactions.
     * This method can be overridden to provide specific validation logic for cash payments.
     * Check for cash payments crossing the two Lakhs (200000) threshold in a day
     *
     */
    override fun validatePaymentForApproval(draftReceipt: DraftReceipt) {
        require(draftReceipt.transactionDate == null) { "Transaction Date is not supported for Cash Payment"}
        require(draftReceipt.txReferenceNumber == null) { "Transaction Reference Number is not supported for Cash Payment"}

        val startDate = LocalDate.now().atStartOfDay()
        val endDate = LocalDate.now().plusDays(1).atStartOfDay()
        val totalCash =
            draftReceiptService.findCashTotalForPartner(draftReceipt.partnerDetailId, startDate, endDate)
        require(totalCash + draftReceipt.amount <= cashThreshold) {
            "Cash payment cannot exceed $cashThreshold in a day"
        }
    }

    override fun validatePaymentUpdate(receiptUpdateDto: ReceiptUpdateDto) {
        with(receiptUpdateDto) {
            require(bankName.isNullOrBlank()) { "Bank name is not allowed for CASH payments" }
            require(txReferenceNumber.isNullOrBlank()) { "Transaction reference number is not allowed for CASH payments" }
            require(transactionDate == null) { "Transaction date is not allowed for CASH payments" }
        }
        super.validatePaymentUpdate(receiptUpdateDto)
    }


}
