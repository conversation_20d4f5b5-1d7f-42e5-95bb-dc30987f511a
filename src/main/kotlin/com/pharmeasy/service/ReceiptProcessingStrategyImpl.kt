package com.pharmeasy.service

import com.pharmeasy.data.DraftReceipt
import com.pharmeasy.data.DraftReceiptEntityMapping
import com.pharmeasy.data.Receipt
import com.pharmeasy.model.ops.SettlementGroupNumber
import com.pharmeasy.service.abstracts.BaseReceiptProcessingStrategy
import com.pharmeasy.service.strategy.PaymentProcessorStrategyFactory
import com.pharmeasy.type.AdvancePaymentSource
import com.pharmeasy.type.ReceiptStatus
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service

@Service
class ReceiptProcessingStrategyImpl(
    private val companyService: CompanyService,
    private val documentService: DocumentMasterService,
    private val paymentProcessorStrategyFactory: PaymentProcessorStrategyFactory,
    private val settleableProcessorFactory: SettleableProcessorFactory,
    @Lazy private val receiptService: ReceiptServiceV2
) : BaseReceiptProcessingStrategy(
        companyService,
        documentService,
        paymentProcessorStrategyFactory,
        settleableProcessorFactory
    ) {
    override val source: AdvancePaymentSource = AdvancePaymentSource.RIO_COLLECTIONS

    fun createReceipt(draftReceipt: DraftReceipt, settlementGroupNumber: SettlementGroupNumber): Receipt {
        validateReceiptForApproval(draftReceipt)

        val existingReceipt = receiptService.getReceiptByDraftId(draftReceipt.id!!)
        val receipt =
            if (existingReceipt == null) {
                val receipt = Receipt(draftReceipt = draftReceipt, "SYSTEM") // User gets updated for WEB flows
                receiptService.saveReceipt(receipt)
            } else {
                existingReceipt
            }
        addSettlements(receipt, draftReceipt.settleableMappings, settlementGroupNumber)
        val paymentProcessor = paymentProcessorStrategyFactory.getInstance(receipt.paymentType!!)
        paymentProcessor.savePayment(receipt)
        return receipt
    }

    fun addSettlements(
        receipt: Receipt,
        settlementMapping: List<DraftReceiptEntityMapping>,
        settlementGroupNumber: SettlementGroupNumber
    ) {
        settlementMapping.forEach {
            receiptService.addSettlement(receipt, it, settlementGroupNumber)
        }
    }
}
