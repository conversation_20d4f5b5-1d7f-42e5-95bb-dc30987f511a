package com.pharmeasy.service

import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.type.PaymentType

sealed class ReceiptStrategy {
    object DraftReceipt : ReceiptStrategy()

    object PreApprovedReceipt : ReceiptStrategy()

    companion object {
        private const val RIO_DELIVERY = "RIO_DELIVERY"
        private const val SALESMAN_DELIVERY_BOY = "SALESMAN_DELIVERY_BOY"
        private const val DIGITAL_RECEIPT = "DIGITAL_RECEIPT"
        private const val DIGITAL_PAYMENT = "DIGITAL_PAYMENT"
        private const val WALLET = "WALLET"
        private const val CREDIT = "CREDIT"

        fun getStrategy(payment: PaymentInfo): ReceiptStrategy {
            return when {
                isDigitalPaymentType(payment) || isCashWithPreApproval(payment) -> PreApprovedReceipt
                else -> DraftReceipt
            }
        }


        private fun isDigitalPaymentType(payment: PaymentInfo): Boolean {
            val paymentModeUpper = payment.paymentMode?.uppercase()
            return paymentModeUpper == DIGITAL_PAYMENT ||
                paymentModeUpper == WALLET ||
                paymentModeUpper == CREDIT
        }

        private fun isCashWithPreApproval(payment: PaymentInfo): Boolean {
            val  initiatedByUpper = payment.initiatedBy?.uppercase()
            val paymentModeUpper = payment.paymentMode?.uppercase()

            val isCashTransaction = payment.paymentType == PaymentType.CASH
            val isPreApprovedSource = (
                initiatedByUpper == RIO_DELIVERY ||
                    initiatedByUpper == SALESMAN_DELIVERY_BOY
                )
            val isDigitalReceiptMode = paymentModeUpper == DIGITAL_RECEIPT
            val isNotBankDeposit = payment.bankDetails?.isBankDeposit != true

            return isCashTransaction && isPreApprovedSource && isNotBankDeposit && isDigitalReceiptMode
        }
    }
}
