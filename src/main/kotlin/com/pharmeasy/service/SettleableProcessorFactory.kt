package com.pharmeasy.service

import com.pharmeasy.service.abstracts.BaseSettleableProcessor
import com.pharmeasy.type.SettleableType
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service

@Service
class SettleableProcessorFactory() {

    @Autowired
    @Lazy
    private lateinit var invoiceSettleableProcessor: InvoiceSettleableProcessor

    @Autowired
    @Lazy
    private lateinit var debitNoteSettleableProcessor: DebitNoteSettleableProcessor

    fun getSettleableProcessor(type: SettleableType): BaseSettleableProcessor {
        return when (type) {
            SettleableType.INVOICE -> invoiceSettleableProcessor
            SettleableType.DEBIT_NOTE -> debitNoteSettleableProcessor
        }
    }
}
