package com.pharmeasy.service

import com.pharmeasy.data.*
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.ReceiptReversalDto
import com.pharmeasy.model.VendorLedgerDto
import com.pharmeasy.repo.*
import com.pharmeasy.repo.advancepayment.AdvancePaymentRepo
import com.pharmeasy.repo.advancepayment.AdvanceSettlementMappingRepo
import com.pharmeasy.type.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDate
import java.time.LocalDateTime.now

@Service
class ReceiptService {

    companion object {
        private val log = LoggerFactory.getLogger(ReceiptService::class.java)
    }

    @Autowired
    private lateinit var receiptRepo: ReceiptRepo

    @Autowired
    private lateinit var receiptSettlementRepo: ReceiptSettlementRepo

    @Autowired
    private lateinit var documentService: DocumentMasterService

    @Autowired
    private lateinit var companyRepo: CompanyRepo

    @Autowired
    private lateinit var settlementRepo: SettlementRepo

    @Autowired
    private lateinit var advancePaymentRepo: AdvancePaymentRepo

    @Autowired
    private lateinit var invoiceSettlementRepo: InvoiceSettlementRepo

    @Autowired
    private lateinit var advancePaymentService: AdvancePaymentService

    @Autowired
    private lateinit var partnerService: PartnerService

    @Autowired
    private lateinit var advanceSettlementMappingRepo: AdvanceSettlementMappingRepo

    @Transactional
    fun createSettlementReceipt(settlement: Settlement) {
        // Advance Payment is deprecated, so no need to create receipt for it
        // Credit Note is no longer being mapped to receipt, so no need to create receipt for it
        when (settlement.paymentType) {
            in listOf(PaymentType.CREDITNOTE, PaymentType.ADVANCE_PAYMENT) -> {}
            else -> createReceipt(settlement)
        }
    }

    @Transactional
    fun handleReceiptReversal(receiptReversalDto: ReceiptReversalDto) {
        val receipt = receiptSettlementRepo.getReceiptBySettlementId(receiptReversalDto.previousSettlementId)
        if (receipt != null) {
            val receiptId = receipt.id.receiptId
            val receiptData = receiptRepo.getOne(receiptId)
            receiptData.updatedAt = now()
            receiptData.updatedBy = receiptReversalDto.user
            receiptData.iteration = 0
            receiptData.status = getReceiptStatusBySettlementId(receiptReversalDto.settlementId)
            receiptData.remarks = getReceiptRemarkByStatus(receiptData.status)
            receiptRepo.save(receiptData)
        }

        if (receiptReversalDto.settlementId != null) {
            val settlement = settlementRepo.getOne(receiptReversalDto.settlementId!!)
            createReceipt(settlement)
        }
    }

    fun checkExistingReceipt(settlement: Settlement): Receipt? {
        val paymentReference = settlement.paymentReference
        val paymentDate = settlement.paymentDate
        val paymentMode = settlement.paymentType
        val partnerDetailId = settlement.partnerDetailId
        val bankName = settlement.bankName
        val receiptsIds = receiptRepo.getReceiptsByPaymentReferenceAndPaymentDateAndPaymentModeAndPartnerDetailIdAndBankName(paymentReference, paymentDate, paymentMode, partnerDetailId, bankName)
        return if(receiptsIds.isNullOrEmpty()){
            null
        }else{
            //todo: check receipt number is same for all the receipts
            receiptsIds[0]
        }
    }

    private fun mapReceiptWithCreditNoteSettlement(settlement: Settlement) {
        val creditNotes = settlement.creditNotes
        for (creditNote in creditNotes) {
            when (creditNote.noteType) {
                NoteTypes.ADVANCE_PAYMENT -> {
                    val receiptId = getReceiptIdByAdvanceCreditNote(creditNote)
                    if (receiptId == null) {
                        createReceipt(settlement)
                    } else {
                        mapReceiptSettlement(receiptId, settlement.id, settlement.createdBy!!)
                    }
                }
                else -> createReceipt(settlement)
            }
        }
    }

    private fun createReceipt(settlement: Settlement) {
        val existingReceipt = checkExistingReceipt(settlement)
        if (existingReceipt != null) {
            updateExistingReceipt(existingReceipt, settlement)
            mapReceiptSettlement(existingReceipt.id!!, settlement.id, settlement.createdBy!!)
        } else {
            val tenant = settlement.tenant
            val user = settlement.createdBy!!
            val amount = settlement.paidAmount
            val paymentTransactionId = settlement.paymentReference

            val company = companyRepo.getCompanyByTenant(tenant)
                ?: throw RequestException("Company mapping not found for $tenant")

            val receiptNumber = documentService.generateDocumentNumber(company.companyCode, DocumentType.RECEIPT_ENTRY)

            val status = ReceiptStatus.GENERATED
            val remarks = getReceiptRemarkByStatus(status)

            val receipt = receiptRepo.save(
                Receipt(
                    null, receiptNumber, now(), user, now(), user,
                    paymentTransactionId, remarks, amount, status, iteration =  1, advanceAmount = 0.0,
                    source = AdvancePaymentSource.SYSTEM
                )
            )
            mapReceiptSettlement(receipt.id!!, settlement.id, user)
        }
    }

    private fun mapReceiptSettlement(receiptId: Long, settlementId: Long, user: String) {
        var receipt = receiptSettlementRepo.getReceiptByReceiptIdAndSettlementId(receiptId, settlementId)
        if(receipt == null) {
            val receiptSettlementId = ReceiptSettlementId(receiptId, settlementId)
            receiptSettlementRepo.save(ReceiptSettlement(receiptSettlementId, now(), user))
        }
    }

    private fun getReceiptIdByAdvanceCreditNote(creditNote: CreditNote): Long? {
        val receipts = receiptSettlementRepo.getReceiptSettlementsByCreditNote(creditNote.creditNoteNumber)
        return receipts?.firstOrNull()?.id?.receiptId
    }

    private fun getReceiptStatusBySettlementId(settlementId: Long?): ReceiptStatus {
        return when (settlementId) {
            null -> ReceiptStatus.CANCELLED
            else -> ReceiptStatus.REVERSED
        }
    }

    private fun getReceiptRemarkByStatus(status: ReceiptStatus): String {
        return when (status) {
            ReceiptStatus.CANCELLED -> "Receipt Cancelled"
            ReceiptStatus.REVERSED -> "Receipt Reversed"
            else -> "Receipt Created"
        }
    }

    private fun updateExistingReceipt(receipt: Receipt, settlement: Settlement) {
        receipt.updatedBy = settlement.createdBy
        receipt.iteration += 1
        receipt.amount = receipt.amount?.plus(settlement.paidAmount)
        receiptRepo.save(receipt)
    }

    @Transactional
    fun updateAdvancePaymentAmountInReceipt(settlement: Settlement, advanceAmount: Double, source: AdvancePaymentSource, advanceId: Long?){
        val existingReceipt = checkExistingReceipt(settlement) ?: throw RequestException("Receipt not found for settlement id: ${settlement.id}")
        existingReceipt?.updatedBy = settlement.createdBy
        existingReceipt?.iteration = existingReceipt?.iteration?.plus(1)!!
        existingReceipt.amount = existingReceipt.amount?.plus(advanceAmount)
        existingReceipt.advanceAmount = advanceAmount
        existingReceipt.source = source
        existingReceipt.advanceId = advanceId
        val receipt = receiptRepo.save(existingReceipt)
        val ledgerAmount = settlement.paidAmount+advanceAmount
        if(settlement.paymentType != PaymentType.CHEQUE){
            createLedgerFromReceipt(receipt,settlement,ledgerAmount)
        }
    }

    fun checkAdvancePaymentSource(settlement: Settlement){
        val advancePaymentIds = advanceSettlementMappingRepo.getSettlementAdvPayBySettlementId(settlement.id)
        advancePaymentIds.forEach{
            val receipt = getReceiptByAdvancePaymentId(it.id.advancePaymentId)
            if(receipt == null) {
                createReceipt(settlement)
            }else {
                mapReceiptSettlement(receipt.id!!, settlement.id, settlement.createdBy!!)
            }
        }
    }

    @Transactional
    fun createAdvancePaymentFromReceipt(receipt:Receipt, settlement: Settlement, userName: String, userEmail: String, ds:String? = null){
        if (settlement.paymentType == PaymentType.CHEQUE) {
            val bkInvoice = invoiceSettlementRepo.getInvoicesBySettlementId(settlement.id)
            val advanceId = advancePaymentService.autoCreateAndApproveAdvancePayment(
                receipt.advanceAmount!!,
                bkInvoice[0],
                settlement,
                userName,
                userEmail,
                settlement.chequeDate,
                ds
            )
            receipt.advanceId = advanceId
            receiptRepo.save(receipt)
        }
    }

    @Transactional
    fun createLedgerFromReceipt(receipt: Receipt,settlement: Settlement,creditAmount: Double = settlement.paidAmount){
        val particulars = "Paid by "+settlement.paymentType.name
        val vendorLedger = VendorLedgerDto(
            transactionDate = LocalDate.now(),
            vendorId = settlement.partnerId!!,
            vendorName = settlement.supplierName!!,
            ledgerEntryType = LedgerEntryType.CREDIT,
            documentType = DocumentType.CX_RECEIPT,
            documentNumber = settlement.settlementNumber!!,
            referenceNumber = settlement.paymentReference,
            externalReferenceNumber = receipt.receiptNumber,
            particulars = particulars,
            debitAmount = BigDecimal.ZERO,
            creditAmount = creditAmount.toBigDecimal(),
            partnerDetailId = settlement.partnerDetailId,
            partnerId = settlement.partnerId,
            tenant = settlement.tenant,
            type = settlement.type,
            client = InvoiceType.RIO
        )
        try{
            partnerService.addVendorLedgerEntry(receipt.updatedBy?:"SYSTEM", vendorLedger)
        }catch (e:Exception){
            log.error("Error while creating ledger entry for receipt ${receipt.receiptNumber} for settlement ${settlement.settlementNumber}", e)
            throw Exception("Error while creating ledger entry for receipt ${receipt.receiptNumber} for settlement ${settlement.settlementNumber}")
        }
    }

    fun addLedgerForReceipt(settlement: Settlement){
        val receipt = checkExistingReceipt(settlement)?: throw RequestException("Receipt not found for settlement")
        createLedgerFromReceipt(receipt,settlement, receipt.amount!!)
    }

    fun mapAdvanceIdToReceipt(settlement: Settlement, advance: AdvancePayment, source: AdvancePaymentSource){
        val existingReceipt = checkExistingReceipt(settlement)?: throw RequestException("Receipt not found for settlement")
        existingReceipt.source = source
        existingReceipt.advanceId = advance.id
        existingReceipt.advanceAmount = advance.amount?.toDouble()
        existingReceipt.amount = existingReceipt.amount?.plus(advance.amount!!.toDouble())
        existingReceipt.updatedBy = settlement.createdBy
        existingReceipt.updatedAt = now()
        val receipt = receiptRepo.save(existingReceipt)
        createLedgerFromReceipt(receipt,settlement, receipt.amount!!)
    }

    fun getAllSettlementsFromReceipt(settlementNumber: String): List<Settlement>{
        val settlement = settlementRepo.getSettlementBySettlementNumber(settlementNumber)
        val receipt = receiptRepo.getReceiptBySettlementId(settlement!!.id)
        return receiptSettlementRepo.getSettlementByReceiptId(receipt?.get(0)?.id!!)
    }

    fun getReceiptByAdvancePaymentId(advancePaymentId: Long): Receipt? {
        return receiptRepo.getReceiptByAdvanceId(advancePaymentId)
    }

    fun updateReceiptDtos(draftReceiptDtos: List<ReceiptDto>) {
        val draftIds = draftReceiptDtos.map { it.id!! }
        val receipts = receiptRepo.findByDraftReceiptIdIn(draftIds).associateBy { it.id }
        val settlementCounts = receiptSettlementRepo.countByReceiptIds(draftIds).associate {
            (it[0] as BigInteger).toLong() to (it[1] as BigInteger).toLong()
        }
       draftReceiptDtos.onEach {
           val receipt = receipts[it.id]
           if (receipt != null) {
               it.updatedOn = receipt.updatedAt
               it.updatedBy = receipt.updatedBy
               it.settlementCount = settlementCounts[it.id] ?: 0
               it.unUtilizedAmount = receipt.unUtilizedAmount
               it.approvedBy = receipt.createdBy
               it.approvedAt = receipt.createdAt
               it.settlementCount = settlementCounts[it.id] ?: 0
               if(it.status == ReceiptStatus.REVERSED) {
                   it.reversedBy = receipt.updatedBy
               }
           }
       }
    }
}
