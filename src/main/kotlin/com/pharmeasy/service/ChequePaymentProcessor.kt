package com.pharmeasy.service

import com.pharmeasy.data.DraftReceipt
import com.pharmeasy.data.Receipt
import com.pharmeasy.dto.ReceiptUpdateDto
import com.pharmeasy.model.CreateChequeHandleDto
import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.service.abstracts.PaymentProcessor
import org.springframework.stereotype.Service
import java.time.LocalDate

@Service
class ChequePaymentProcessor(
    private val draftReceiptService: DraftReceiptService,
    private val chequeHandlingService: ChequeHandlingService
) : PaymentProcessor(draftReceiptService) {

    companion object {
        private const val CHEQUE_EXPIRY_DAYS = 90L
    }

    override fun validatePayment(payment: PaymentInfo, partnerInfo: PartnerInfo): String? {
        super.validatePayment(payment, partnerInfo)?.let { return it }
        var remark =
            if (payment.bankDetails?.chequeNo.isNullOrBlank()) {
                "Cheque number is required for cheque payments"
            } else {
                null
            }

        if (remark != null) return remark
        if (payment.bankDetails?.chequeDate == null || payment.bankDetails.bankName.isNullOrBlank()) {
            return null
        }
        require(payment.bankDetails.chequeDate.plusDays(CHEQUE_EXPIRY_DAYS).isAfter(LocalDate.now())) {
            "Cheque date should be within 90 days of today"
        }
        remark = isDuplicate(
            partnerInfo.partnerDetailId,
            partnerInfo.tenant,
            payment.bankDetails.chequeDate,
            payment.bankDetails.bankName,
            payment.bankDetails.chequeNo!!
        )
        return remark
    }

    override fun validatePaymentForApproval(draftReceipt: DraftReceipt) {
        require(draftReceipt.bankName.isNullOrBlank()) { "Bank name is required for cheque payments" }
        require(draftReceipt.transactionDate != null) { "Cheque date is required for cheque payments" }
        require(draftReceipt.txReferenceNumber.isNullOrBlank()) { "Cheque number is required for cheque payments" }
        require(draftReceipt.transactionDate!!.plusDays(CHEQUE_EXPIRY_DAYS).isBefore(LocalDate.now())) {
            "Cheque date should not be older than 90 days"
        }

        require(
            isDuplicate(
                draftReceipt.partnerDetailId,
                draftReceipt.tenant,
                draftReceipt.transactionDate!!,
                draftReceipt.bankName!!,
                draftReceipt.txReferenceNumber!!
            ) == null
        ) {
            "Cheque number ${draftReceipt.txReferenceNumber} already exists for the given date" +
                " in receipt: ${draftReceipt.receiptNumber}"
        }
    }

    override fun validatePaymentUpdate(receiptUpdateDto: ReceiptUpdateDto) {
        with(receiptUpdateDto) {
            require(!bankName.isNullOrBlank()) { "Bank name is required for cheque payments" }
            require(!txReferenceNumber.isNullOrBlank()) { "Transaction reference number is required for cheque payments" }
            require(transactionDate != null) { "Transaction date is required for cheque payments" }
            require(receiptUpdateDto.transactionDate!!.plusDays(CHEQUE_EXPIRY_DAYS).isBefore(LocalDate.now())) {
                "Cheque date should not be older than 90 days"
            }
        }
        super.validatePaymentUpdate(receiptUpdateDto)
    }

    private fun isDuplicate(
        partnerDetailId: Long,
        tenant: String,
        chequeDate: LocalDate,
        bankName: String,
        chequeNo: String
    ): String? {
        var remark: String? = null
        val chequeDate = chequeDate
        val duplicateCheque =
            draftReceiptService
                .findDuplicateCheque(
                    chequeNo,
                    chequeDate,
                    partnerDetailId,
                    tenant,
                    bankName
                )
        if (duplicateCheque.isNotEmpty()) {
            remark = "Cheque number $chequeNo already exists for the given date" +
                " in receipt: ${duplicateCheque[0].receiptNumber}"
        }
        return remark
    }

    override fun savePayment(receipt: Receipt) {
        chequeHandlingService.addChequeHandleEntry(
            receipt.updatedBy!!,
            CreateChequeHandleDto(
                receipt.tenant,
                receipt.txReferenceNumber!!,
                null,
                null,
                receipt.transactionDate!!,
                receipt.id!!
            )
        )
    }
}
