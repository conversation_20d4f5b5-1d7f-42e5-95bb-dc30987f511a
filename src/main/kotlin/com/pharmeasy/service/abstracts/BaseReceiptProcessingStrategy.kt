package com.pharmeasy.service.abstracts

import com.pharmeasy.data.DraftReceipt
import com.pharmeasy.data.DraftReceiptEntityMappingDto
import com.pharmeasy.dto.MisMatchedDetailsDto
import com.pharmeasy.dto.ReceiptUpdateDto
import com.pharmeasy.dto.SettleableDetails
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.service.CompanyService
import com.pharmeasy.service.DocumentMasterService
import com.pharmeasy.service.SettleableProcessorFactory
import com.pharmeasy.service.strategy.PaymentProcessorStrategyFactory
import com.pharmeasy.type.AdvancePaymentSource
import com.pharmeasy.type.DocumentType
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.ReceiptStatus
import com.pharmeasy.type.SettleableType
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
abstract class BaseReceiptProcessingStrategy(
    private val companyService: CompanyService,
    private val documentService: DocumentMasterService,
    private val paymentProcessorStrategyFactory: PaymentProcessorStrategyFactory,
    private val settleableProcessorFactory: SettleableProcessorFactory
) {
    val source: AdvancePaymentSource = AdvancePaymentSource.SYSTEM
    private val log = LoggerFactory.getLogger(javaClass)

    companion object {
        @JvmStatic
        protected val DEFAULT_RECEIPT_REMARK = "Receipt Created from RIO Payment"
    }

    protected fun validateReceiptRequest(payment: PaymentInfo, partnerInfo: PartnerInfo): String? {
        log.info("Validating receipt details for payment: ${payment.customerTransactionId}")
        require(payment.customerTransactionId.isNotBlank()) { "Payment transaction ID is required" }
        require(payment.transactionAmount > 0) { "Transaction amount must be positive" }

        var remark: String? = null
        payment.settleables.forEach { settleable ->
            val settleableType = settleable.type.settleableType
            val settleableProcessor = settleableProcessorFactory.getSettleableProcessor(settleableType)

            // TODO: Should this be a failure or reject with remark?
            requireNotNull(settleableProcessor.getSettleableDetails(settleable.number, partnerInfo.tenant)) {
                "Invalid $settleableType number: ${settleable.number} for tenant ${partnerInfo.tenant}"
            }
        }
        val paymentProcessor = paymentProcessorStrategyFactory.getInstance(payment.paymentType)
        paymentProcessor.validatePayment(payment, partnerInfo)?.let {
            remark = it
        }

        return remark
    }

    protected fun validateReceiptForApproval(draft: DraftReceipt) {
        require(draft.status == ReceiptStatus.APPROVED) { "Receipt status must be APPROVED" }
        require(draft.amount == draft.unUtilizedAmount + draft.settleableMappings.sumOf { it.entityTxAmount }) {
            "Receipt amount must be equal to the sum of the unUtilized amount and settleable mappings"
        }

        val paymentProcessor = paymentProcessorStrategyFactory.getInstance(draft.paymentType!!)
        paymentProcessor.validatePaymentForApproval(draft)
    }

    protected fun generateReceiptNumber(tenant: String): String {
        val company = companyService.getCompanyByTenant(tenant)
            ?: throw RequestException("Company not found for tenant $tenant")
        val receiptNumber = documentService.generateDocumentNumber(company.companyCode, DocumentType.RECEIPT_ENTRY)
        return receiptNumber
    }

    protected fun validateReceiptUpdateDto(receiptUpdateDto: ReceiptUpdateDto): List<MisMatchedDetailsDto> {
        with(receiptUpdateDto) {
            require(paymentType !in listOf(PaymentType.CASH, PaymentType.NEFT, PaymentType.CHEQUE)) {
                "Invalid Payment Type"
            }
            val paymentProcessor = paymentProcessorStrategyFactory.getInstance(paymentType)
            paymentProcessor.validatePaymentUpdate(receiptUpdateDto)
            var totalTxAmt = 0.0
            val activeSettleableMappings = mutableListOf<DraftReceiptEntityMappingDto>()
            settleableMappings?.onEach {
                if (it.active) {
                    totalTxAmt += it.entityTxAmount
                    activeSettleableMappings.add(it)
                }
            }
            require(totalTxAmt <= amount) { "Total transaction amount must be greater or equal to the sum of the invoice/DN amounts" }

            val misMatchedDetails = misMatchedDetails(activeSettleableMappings)
            return misMatchedDetails
        }
    }

    private fun misMatchedDetails(activeSettleableMappings: MutableList<DraftReceiptEntityMappingDto>): MutableList<MisMatchedDetailsDto> {
        val response = mutableListOf<MisMatchedDetailsDto>()

        val (invoices, debitNotes) = activeSettleableMappings.partition { it.entityType == SettleableType.INVOICE }
        val settleables = mutableListOf<SettleableDetails>()
        if (invoices.isNotEmpty()) {
            val settleableProcessor = settleableProcessorFactory.getSettleableProcessor(SettleableType.INVOICE)
            val invoiceDtos = settleableProcessor.getSettleableDetails(invoices.map { it.entityId })
            settleables.addAll(invoiceDtos)
        }

        if (debitNotes.isNotEmpty()) {
            val settleableProcessor = settleableProcessorFactory.getSettleableProcessor(SettleableType.DEBIT_NOTE)
            val debitNoteDtos = settleableProcessor.getSettleableDetails(debitNotes.map { it.entityId })
            settleables.addAll(debitNoteDtos)
        }
        val settleableDetails = settleables.associateBy { it.id }
        activeSettleableMappings.onEach {
            val settleableDetails = settleableDetails[it.entityId]
            if (settleableDetails == null) {
                throw RequestException("Could not get settleable details for ${it.entityType} id: ${it.entityId}")
            }
            require(settleableDetails.pendingAmount >= it.entityTxAmount) {
                "Amount cannot be greater than pending amount for ${it.entityType} id: ${it.entityId}"
            }
            if(settleableDetails.pendingAmount != it.openAmount) {
                response.add(MisMatchedDetailsDto(it.entityType, it.entityId, settleableDetails.pendingAmount))
            }
        }
        return response
    }

}

