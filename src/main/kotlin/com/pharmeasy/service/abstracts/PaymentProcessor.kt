package com.pharmeasy.service.abstracts

import com.pharmeasy.data.DraftReceipt
import com.pharmeasy.data.Receipt
import com.pharmeasy.dto.ReceiptUpdateDto
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.model.ops.RIOPaymentDTO
import com.pharmeasy.service.DraftReceiptService

abstract class PaymentProcessor(
    private val draftReceiptService: DraftReceiptService
) {
    /**
     * Validates the payment details.
     * This method can be overridden to provide specific validation logic for different payment types.
     * Default implementation checks for the presence of a transaction ID
     * and uniqueness of the payment at referenceNumber level
     *
     * @param payment The payment details to validate.
     * @throws RequestException if the payment details are invalid.
     */
    open fun validatePayment(payment: PaymentInfo, partnerInfo: PartnerInfo): String? {
        require(payment.customerTransactionId.isNotBlank()) {
            "Transaction ID is required"
        }

        return checkDuplicates(payment, partnerInfo)
    }

    open fun validatePaymentForApproval(draftReceipt: DraftReceipt) {}

    private fun checkDuplicates(payment: PaymentInfo, partnerInfo: PartnerInfo): String? {
        val duplicatePayments =
            draftReceiptService.findDuplicatesByPaymentTransactionId(
                payment.customerTransactionId,
                partnerInfo.partnerDetailId,
                partnerInfo.tenant
            )

        val remark =
            if (duplicatePayments.isNotEmpty()) {
                "Transaction ID ${payment.customerTransactionId} already exists for the given partner" +
                    " in receipt: ${duplicatePayments[0].receiptNumber}"
            } else {
                null
            }
        return remark
    }

    open fun savePayment(receipt: Receipt) {
        return
    }

    open fun validatePaymentUpdate(receiptUpdateDto: ReceiptUpdateDto) {
        require(receiptUpdateDto.amount > 0) { "Amount must be greater than zero" }
    }
}
