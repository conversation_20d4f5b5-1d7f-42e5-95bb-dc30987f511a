package com.pharmeasy.service


import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.pharmeasy.data.*
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.proxy.RetailIoProxy
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.proxy.WarehouseProxy
import com.pharmeasy.repo.*
import com.pharmeasy.repo.read.*
import com.pharmeasy.specification.BkInvoiceSpecification
import com.pharmeasy.specification.InvoiceMultiGetSpecification
import com.pharmeasy.specification.SupplierInvoiceSpecification
import com.pharmeasy.stream.BlockedVendorSettlementPusher
import com.pharmeasy.stream.InvoiceSettlementUpdateHandler
import com.pharmeasy.stream.SettlementInvoiceListGateway
import com.pharmeasy.type.*
import com.pharmeasy.type.DocumentType
import com.pharmeasy.type.InvoiceType
import com.pharmeasy.type.LedgerEntryType
import com.pharmeasy.type.PartnerType
import com.pharmeasy.util.DateUtils.getISTDateTime
import com.pharmeasy.util.DateUtils.getUTCDateTime
import com.pharmeasy.util.UUIDUtil
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVPrinter
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.io.ByteArrayOutputStream
import java.math.BigDecimal
import java.math.BigDecimal.ONE
import java.math.BigDecimal.ZERO
import java.math.RoundingMode
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalDateTime.now
import java.time.temporal.ChronoUnit
import kotlin.math.max

@Service
class InvoiceService(@Value("\${app.partner.retailer}") val retailer : Int,
                     @Value("\${app.partner.retailerAlpha}") val retailerAlpha : Int,
                     @Value("\${app.partner.retailerInClinic}") val retailerInClinic : Int,
                     @Value("\${app.partner.retailerFOFO}") val retailerFOFO : Int,
                     @Value("\${app.partner.retailerCOCO}") val retailerCOCO : Int,
                     @Value("\${app.partner.retailerHospitals}") val retailerHospitals : Int,
                     @Value("\${app.partner.hospitals}") val hospitals : Int,
                     @Value("\${app.partner.retailerPL}") val retailerPL : Int,
                     @Value("\${app.partner.cnf}") val cnf : Int,
                     @Value("\${app.partner.distributor}") val distributor : Int,
                     @Value("\${app.partner.holding}") val holding : Int,
                     @Value("\${app.partner.manufacturer}") val manufacturer : Int,
                     @Value("\${app.partner.retailerMarketPlace}") val retailerMarketPlace : Int,
                     @Value("\${app.retail-io.version}")val retailIoVersion: String,
                     @Value("\${app.retail-io.source}")val retailIoSource: String,
                     @Value("\${app.retail-io.key}")val retailIoKey: String) {

    companion object {
        private val log = LoggerFactory.getLogger(InvoiceService::class.java)
        public val INVALID_PROC_TYPE_MSG = "Only invoices of purchase type JIT and Procurement is allowed"
    }

    @Autowired
    private lateinit var retailIoProxy: RetailIoProxy

    @Autowired
    private lateinit var bkInvoiceRepo: BkInvoiceRepo

    @Autowired
    private lateinit var bkInvoiceReadRepo: BkInvoiceReadRepo

    @Autowired
    private lateinit var bkItemRepo: BkItemRepo

    @Autowired
    private lateinit var supplierProxy: SupplierProxy

    @Autowired
    private lateinit var partnerService: PartnerService

    @Autowired
    private lateinit var invoiceSettlementRepo: InvoiceSettlementRepo

    @Autowired
    private lateinit var companyTenantMappingRepo: CompanyTenantMappingRepo

    @Autowired
    private lateinit var vendorFileService: VendorFileService

    @Autowired
    private lateinit var s3FileUtilityService: S3FileUtilityService

    @Autowired
    private lateinit var invoiceDataLinkRepo: InvoiceDataLinksRepo

    @Autowired
    private lateinit var warehouseProxy: WarehouseProxy

    @Autowired
    private lateinit var adjustmentService: AdjustmentService

    @Autowired
    private lateinit var gatePassService : GatePassService

    @Autowired
    private lateinit var companyService: CompanyService

    @Autowired
    private lateinit var debitNoteService: DebitNoteService

    @Autowired
    private lateinit var settlementService: SettlementService

    @Autowired
    private lateinit var creditNoteReservationRepo: CreditNoteReservationRepo

    @Autowired
    private lateinit var creditNoteRepo: CreditNoteRepo

    @Autowired
    private lateinit var redisUtilityService: RedisUtilityService

    @Autowired
    private lateinit var settlementRepo: SettlementRepo

    @Autowired
    private lateinit var invoiceDebitNoteReadRepo: InvoiceDebitNoteReadRepo

    @Autowired
    private lateinit var invoiceSettlementReadRepo: InvoiceSettlementReadRepo

    @Autowired
    private lateinit var debitNoteReadRepo: DebitNoteReadRepo

    @Autowired
    private lateinit var settlementReadRepo: SettlementReadRepo

    @Autowired
    private lateinit var blockedVendorSettlementPusher: BlockedVendorSettlementPusher

    @Autowired
    private lateinit var slipService: SlipService

    @Autowired
    private lateinit var invoiceSettlementUpdateHandler: InvoiceSettlementUpdateHandler

    @Autowired
    private lateinit var settlementInvoiceListGateway: SettlementInvoiceListGateway

    fun getInvoices(page: Int?, size: Int?, supplierId: Long?, invoiceId: String?, invoiceNum: String?, status: InvoiceStatus?,
                    from: LocalDate?, to: LocalDate?, tenant: String?, customerType: Boolean, ds: String? = null): PageableBkInvoice {
        log.debug("Inside getInvoices - $page : $size : $supplierId : $invoiceId : $invoiceNum : $status : $from : $to : $tenant")
        val pagination = PageRequest.of(page ?: 0, size ?: 10, Sort.Direction.ASC, "dueDate")
        var custType: PartnerType = PartnerType.VENDOR
        if (customerType) custType = PartnerType.CUSTOMER
        //val res = bkInvoiceRepo.getInvoices(invoiceId, invoiceNum, supplierId, status, fromDate, toDate, tenant, page)
        var companyTenantMapping = companyService.getCompanyTenantMappingObject(ds?:tenant!!)?: throw RequestException("No tenant Mapping found")

        var tenants = companyService.findTenants(ds?:tenant?:"")
        if (tenants.isNullOrEmpty()) return PageableBkInvoice()

        val res = bkInvoiceRepo.findAll(BkInvoiceSpecification(invoiceId, invoiceNum, null, mutableListOf(status), from, to, tenants, supplierId, custType), pagination)

        var finalres = mutableListOf<BkInvoiceWithSettlementId>()
        res.content.forEach { bkInvoice ->

            var supplierList = supplierProxy.supplier(listOf(bkInvoice.partnerId))
            var supplier = if (supplierList.isNotEmpty()) supplierList.get(0) else null
            if (supplier != null) bkInvoice.supplierName = supplier.partnerName!!
            finalres.add(
                    BkInvoiceWithSettlementId(
                            id = bkInvoice.id,
                            createdOn = getISTDateTime(bkInvoice.createdOn),
                            updatedOn = getISTDateTime(bkInvoice.updatedOn),
                            invoiceId = bkInvoice.invoiceId,
                            invoiceNum = bkInvoice.invoiceNum,
                            supplierId = bkInvoice.supplierId,
                            supplierName = bkInvoice.supplierName,
                            amount = bkInvoice.amount,
                            paidAmount = bkInvoice.paidAmount,
                            dueAmount = bkInvoice.amount - bkInvoice.paidAmount,
                            dueDate = bkInvoice.dueDate,
                            status = bkInvoice.status,
                            //     debitNotes = bkInvoice.debitNotes,
                            settlementId = null,
                            items = bkInvoice.items,
                            settledOn = getISTDateTime(bkInvoice.settledOn),
                            tenant = bkInvoice.tenant ?: "",
                            source = companyTenantMapping.tenantName))
        }

        return PageableBkInvoice(res.totalElements, res.totalPages, finalres)
    }

    fun getSupplierInvoices(page: Int?, size: Int?, partnerId: Long, invoiceNum: String?, status: InvoiceStatus?,
                            createdOnFrom: LocalDate?, createdOnTo: LocalDate?, dueDateFrom: LocalDate?, dueDateTo: LocalDate?,
                            settlementNumber: String?, source: String?, tenant: String, overdue: Boolean, customerType: Boolean, invoiceDateFrom: LocalDate?, invoiceDateTo: LocalDate?, ds: String? = null,distributorId: Long?): PaginationDto {

        log.debug("Inside getSupplierInvoices")
        var custType: PartnerType = PartnerType.VENDOR
        if (customerType) custType = PartnerType.CUSTOMER


        var sortBy = Sort.by(Sort.Direction.ASC, "createdOn").and(Sort.by(Sort.Direction.DESC, "amount"))
        val pageRequest = PageRequest.of(page ?: 0, size ?: 10, sortBy)

        var companyMapping = if(distributorId != null) companyService.getTenantByPDI(distributorId) else companyService.getCompanyTenantMappingObject(ds?:tenant)
        var tenants = companyService.findTenants(companyMapping!!.tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")
        var companyTenantMapping = companyService.getCompanyTenantMappingObject(ds?:tenant!!)?: throw RequestException("No tenant Mapping found")



        log.debug("sources : tenants are - $source : $tenants")

        if (!source.isNullOrEmpty() && source !in tenants) throw RequestException("Selected source is not part of the requester's company")

        var res: Page<BkInvoice>

        var statuses: MutableList<InvoiceStatus>? = null

        if (status != null) {
            statuses = mutableListOf()
            statuses.add(status)
            if (status == InvoiceStatus.PENDING) {
                statuses.add(InvoiceStatus.PARTIAL_PAID)
            }
        }

        if (settlementNumber == null) {
            res = bkInvoiceRepo.findAll(SupplierInvoiceSpecification(partnerId, invoiceNum, statuses, createdOnFrom, createdOnTo,
                    dueDateFrom, dueDateTo, source, tenants, overdue, custType,invoiceDateFrom,invoiceDateTo), pageRequest)


        } else {
            if (statuses == null) statuses = mutableListOf(InvoiceStatus.PAID, InvoiceStatus.PARTIAL_PAID, InvoiceStatus.PENDING, InvoiceStatus.DELETED)
            res = invoiceSettlementRepo.getPageableInvoicesForSettlement(settlementNumber, partnerId, statuses, pageRequest)
        }
        val supplierList = supplierProxy.supplier(listOf(partnerId))
        val supplier = if(supplierList.isNotEmpty()) supplierList[0] else null
        res.content.forEach { inv ->
            inv.tenant = companyTenantMapping.tenantName
            inv.createdOn = getISTDateTime(inv.createdOn)
            inv.updatedOn = getISTDateTime(inv.updatedOn)
            inv.settledOn = getISTDateTime(inv.settledOn)
            if (supplier != null) inv.supplierName = supplier.partnerName!!
            log.debug("vendor name: ${inv.supplierName} : ${supplier?.partnerName}")
        }

        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), res.content)
    }

    @Transactional
    fun createCustomerInvoice(customerInvoiceDto: StoreInvoice?,customerDTO:BookkeeperCustomerInvoiceDto): BkInvoice? {
        log.debug("Inside createCustomerInvoice $customerInvoiceDto")

             var invoice = bkInvoiceRepo.getByInvoiceId(customerDTO.externalOrderId, customerInvoiceDto!!.id, mutableListOf(customerDTO.tenant))

        if(invoice != null) return null

        if(customerInvoiceDto != null && (customerInvoiceDto.saleOrder!!.client == "PE" || customerInvoiceDto.saleOrder!!.client == "ML")) {
            log.debug("Received Transit Order for Client ${customerInvoiceDto.saleOrder!!.client} : Ignore!")
            return null
        }else if(customerInvoiceDto != null && customerInvoiceDto.saleOrder!!.client == "RIO") {

            var tenantPartnerInfo = supplierProxy.supplierByTenant(customerDTO.tenant)
            if(tenantPartnerInfo.isNullOrEmpty()) throw RequestException("Partner info not found for Tenant ${customerDTO.tenant} ")
            var customerInvoice = createCustomerRIO(customerInvoiceDto, customerDTO,tenantPartnerInfo[0]!!.id!!)
            // customer CN settlement if reserved
            var dbCustomerInvoice = bkInvoiceRepo.getDSCustomerInvoice(customerInvoiceDto.externalOrderId, customerInvoice.tenant!!)
            if(dbCustomerInvoice != null){
                settleInvoiceWithBlockedCNs(dbCustomerInvoice, null, dbCustomerInvoice.tenant!!)
            }
            return dbCustomerInvoice

        }else{
            throw RequestException("createCustomerInvoice failed : ${customerDTO.externalOrderId} not found !")
        }
    }

//    private fun createCustomerPE(consolidatedStoreInvoiceDto: ConsolidatedStoreInvoiceDto, customerDTO: InvoiceNotificationDTO): BkInvoice {
//        val invoice = customerInvoiceDto
//        val status = if (invoice.saleOrder!!.totalAmount!!.equals(0)) {
//            InvoiceStatus.PAID
//        } else {
//            InvoiceStatus.PENDING
//        }
//
//        val pair = createInvoiceObjPE(customerDTO.user, invoice, status,customerDTO.tenant)
//        var supplierList = pair.first
//        val bkInvoice = pair.second
//
//
//        return saveBkInvoice(customerDTO.user, bkInvoice, supplierList.get(0)!!.partnerId!!)
//    }

    fun createInvoiceObjPE(user: String, invoice: StoreInvoice, status: InvoiceStatus, tenant:String): Pair<List<Supplier?>, BkInvoice> {
        log.info("Creating Invoice for PE/ML/CS client with tenant: $tenant and invoice id: ${invoice.saleOrder.externalOrderId}")
        var warehouse: Warehouse? = warehouseProxy.getByTenant(tenant)
                ?: throw RequestException("Warehouse ${tenant} not found! ")

        var supplierList = supplierProxy.supplier(null, invoice.saleOrder!!.partnerDetailId!!.toLong())
        var supplier = if (supplierList.isNotEmpty()) supplierList.get(0) else throw RequestException("Could not get Supplier List for partnerId ${invoice.saleOrder!!.partnerDetailId} for tenant " +
                "${tenant} ,while invoice creation.")

        var allPartnerDetail = supplier?.partnerDetailList?.associateBy { it!!.id }
        var partnerDetail = allPartnerDetail?.get(invoice.saleOrder!!.partnerDetailId!!.toLong())
        val itemList: MutableList<BkItem> = mutableListOf()
        var items = invoice.saleOrder?.pickedItems?.groupBy { Pair(it.batchNumber, it.ucode) }

        items!!.forEach { item ->

            var igst = if (partnerDetail?.state?.toLowerCase() != warehouse?.state?.toLowerCase()) item.value[0].igst ?: ZERO else ZERO
            var sgst = if (partnerDetail?.state?.toLowerCase() != warehouse?.state?.toLowerCase()) ZERO else item.value[0].sgst ?: ZERO
            var cgst = if (partnerDetail?.state?.toLowerCase() != warehouse?.state?.toLowerCase()) ZERO else item.value[0].cgst ?: ZERO
            var discountPercentage = if (invoice.saleOrder!!.client == "PE" || invoice.saleOrder!!.client == "ML" || invoice.saleOrder!!.client == "CS") {
                if (invoice.saleOrder?.includeCustomerDiscountInStoreInvoice!!) {
                    invoice.saleOrder?.discountPercentage!! + invoice.saleOrder?.storeDiscountPercentage!! + item.value[0].discountPercentage!!
                } else invoice.saleOrder?.storeDiscountPercentage
            } else {
                item.value[0].discountPercentage!!
            }

            var abtMrp = (item.value[0].mrp.divide((ONE + (maxOf(igst, (sgst + cgst)).divide(BigDecimal(100), 5, RoundingMode.CEILING))), 5, RoundingMode.CEILING))
            log.debug("abtMpr : ${abtMrp}")
            var taxableValue = (abtMrp - (abtMrp * (discountPercentage!!.divide(BigDecimal(100), 5, RoundingMode.CEILING)))!!).multiply(item.value.size.toBigDecimal())
            log.debug("taxableValue : $taxableValue")
            var netGstAmt = ((maxOf(igst, (sgst + cgst))).divide(BigDecimal(100), 5, RoundingMode.CEILING)).multiply(taxableValue)
            var invoiceAmt = (taxableValue + netGstAmt)
            var discountAmt = (discountPercentage!!.divide(BigDecimal(100), 5, RoundingMode.CEILING)).multiply(abtMrp)

            val bkItem = BkItem(0, now(), now(), item.value[0].id!!, item.value[0].ucode!!, item.value[0].batchNumber!!, item.value[0].mrp.toDouble(),
                    item.value.size, ZERO, ZERO, discountPercentage, discountAmt, cgst, sgst, igst, item.value[0].hsnCode, invoiceAmt, netGstAmt, abtMrp, item.value[0].name, taxableValue, invoice.id, null, item.value[0].expiryDate)

            itemList.add(bkItem)
        }

        var creditPeriod = supplier!!.retailerCommercial!!.creditPeriod!!

        val bkInvoice = BkInvoice(0, invoice.createdOn, invoice.createdOn, user, user,
                invoice.externalOrderId, invoice.id, invoice.saleOrder!!.partnerDetailId,
                supplier?.partnerName, invoice.payableAmount!!.toDouble(), 0.0, invoice.createdOn!!.plusDays(creditPeriod!!).toLocalDate(), status, itemList, null,
                null, supplierList.get(0)!!.partnerId
                ?: 0, invoice.saleOrder!!.partnerDetailId, tenant, PartnerType.CUSTOMER, InvoiceType.valueOf(invoice.saleOrder!!.client
                ?: "PE"))

        log.info("BkInvoice PE/ML/CS created ${bkInvoice.invoiceId}")

        bkInvoice.items.forEach { bkItem ->
            bkItem.bkInvoice = bkInvoice
        }
        return Pair(supplierList, bkInvoice)
    }

    private fun createCustomerRIO(customerInvoiceDto: StoreInvoice, customerDTO: BookkeeperCustomerInvoiceDto,sourcePartnerId: Long): BkInvoice {
        val invoice = customerInvoiceDto
        val status = if (invoice.retailerInvoiceData!!.totalAmount!!.equals(0)) {
            InvoiceStatus.PAID
        } else {
            InvoiceStatus.PENDING
        }

        val pair = createInvoiceObjRIO(invoice, customerDTO.tenant, status,customerDTO.user,sourcePartnerId)
        var supplierList = pair.first
        val bkInvoice = pair.second


        return saveBkInvoice(customerDTO.user, bkInvoice, supplierList.get(0)!!.partnerId!!)
    }

    fun createInvoiceObjRIO(invoice: StoreInvoice, tenant: String, status: InvoiceStatus, user:String, sourcePartnerDetailId : Long): Pair<List<Supplier?>, BkInvoice> {
        var supplierList = supplierProxy.supplier(null, invoice.saleOrder!!.partnerDetailId!!.toLong())
        var supplier = if (supplierList.isNotEmpty()) supplierList.get(0) else throw RequestException("Could not get Supplier List for partnerId ${invoice.saleOrder!!.partnerDetailId} for tenant " +
                "${tenant} ,while invoice creation.")

        var allPartnerDetail = supplier?.partnerDetailList?.associateBy { it!!.id }

        var partnerDetail = allPartnerDetail?.get(invoice.saleOrder!!.partnerDetailId!!.toLong())

        val itemList: MutableList<BkItem> = mutableListOf()
        var supplierSourceList = supplierProxy.supplier(null, sourcePartnerDetailId)
        var supplierSource = if (supplierSourceList.isNotEmpty()) supplierSourceList.get(0) else throw RequestException("Could not get Source Supplier List for partnerId ${sourcePartnerDetailId} of tenant " +
                "${tenant} ,while invoice creation.")

        var allSourcePartnerDetail = supplierSource?.partnerDetailList?.associateBy { it!!.id }

        var partnerDetailSource = allSourcePartnerDetail?.get(sourcePartnerDetailId)
        invoice.retailerInvoiceData!!.lineItems!!.forEach { item ->

            var igst = if (partnerDetail?.state?.toLowerCase() != partnerDetailSource?.state?.toLowerCase()) item.gst ?: ZERO else ZERO
            var sgst = if (partnerDetail?.state?.toLowerCase() == partnerDetailSource?.state?.toLowerCase()) item.gst!!.divide(BigDecimal(2), 5, RoundingMode.CEILING) else ZERO
            var cgst = if (partnerDetail?.state?.toLowerCase() == partnerDetailSource?.state?.toLowerCase()) item.gst!!.divide(BigDecimal(2), 5, RoundingMode.CEILING) else ZERO

            var saleUnitPriceCheck = item.saleUnitPrice
            var saleUnitPriceTypeCheck = item.saleUnitPriceType

            log.debug("createInvoiceObjRIO saleUnitPriceCheck: $saleUnitPriceCheck")
            log.debug("createInvoiceObjRIO saleUnitPriceTypeCheck: $saleUnitPriceTypeCheck")


            val bkItem = BkItem(0, now(), now(), invoice.retailerInvoiceData!!.saleOrderId!!, item.ucode!!, item.batch!!, item.mrp!!.toDouble(),
                    item.quantityBilled
                            ?: 0, ZERO, ZERO, item.discountPercentage, item.discount, cgst, sgst, igst, item.hsnCode, item.netAmount,
                    item.gstAmount, ZERO, item.name, item.taxableValue, invoice.id, null, item.expiryDate, item.schemePercent,
                    item.quantityFree, item.ptr, item.schemeType?.name, item.gst, item.quantityOrdered, item.quantityScheme, item.salesValue,null,null,item.effectivePerItemCost,item.ucodeSchemePercent , item.saleUnitPrice, item.saleUnitPriceType)

            itemList.add(bkItem)
        }

        var creditPeriod = supplier!!.retailerCommercial?.creditPeriod?:0L

        val bkInvoice = BkInvoice(0, invoice.createdOn, invoice.createdOn, user,user,
                invoice!!.retailerInvoiceData!!.externalOrderId, invoice.id, invoice.saleOrder!!.partnerDetailId,
                supplier?.partnerName, invoice.retailerInvoiceData!!.totalAmount!!.toDouble(), 0.0, invoice.createdOn!!.plusDays(creditPeriod).toLocalDate(), status, itemList, null,
                null, supplierList.get(0)!!.partnerId
                ?: 0, invoice.saleOrder!!.partnerDetailId, tenant, PartnerType.CUSTOMER, InvoiceType.valueOf(invoice.saleOrder!!.client
                ?: "RIO"),null,null,null, (invoice.retailerInvoiceData?.tcsAmount?.toDouble())?:0.0,(invoice.retailerInvoiceData?.tcs?.toDouble())?:0.0)

        log.debug("BkInvoice RIO created $bkInvoice")

        bkInvoice.items.forEach { bkItem ->
            bkItem.bkInvoice = bkInvoice
        }
        return Pair(supplierList, bkInvoice)
    }

    @Transactional
    fun createBookkeeperInvoice(bookkeeperInvoiceDto: BookkeeperInvoiceDto): BkInvoice? {
        val invoice = bookkeeperInvoiceDto.invoice

        var oldInvoice = bkInvoiceRepo.checkDuplicateInvoiceId(bookkeeperInvoiceDto.invoice.id.toString(),bookkeeperInvoiceDto.tenant)

        if(oldInvoice != null){
            log.debug("Duplicate Vendor invoice  :${bookkeeperInvoiceDto.invoice.id} , tenant : ${bookkeeperInvoiceDto.tenant}")
            return null
        }
//        StockTransferReturn ICSReturn
        if (bookkeeperInvoiceDto.invoice.purchaseType !in arrayOf("JIT", "PROCUREMENT","StockTransferReturn","ICSReturn")) {
            log.debug(INVALID_PROC_TYPE_MSG)
            return null
        }
        val status = if (invoice.totalAmount.equals(0)) {
            InvoiceStatus.PAID
        } else {
            InvoiceStatus.PENDING
        }

        var partnerId = supplierProxy.getMasterId(invoice.partnerDetailId)
        var supplierList = supplierProxy.supplier(listOf(partnerId))
        var supplier = if (supplierList.isNotEmpty()) supplierList.get(0) else throw RequestException("Could not get Supplier List for partnerId $partnerId for tenant " +
                "${bookkeeperInvoiceDto.tenant} ,while invoice creation.")

        val itemList: MutableList<BkItem> = mutableListOf()
        invoice.items.forEach { item ->
            val igst = item.igst
            val sgst = item.sgst
            val cgst = item.cgst
            val itemDiscountPercentage = item.discount
            var abtMrp = item.abettedMrp
            log.debug("abtMpr : ${abtMrp}")
//            var taxableValue = item.purchaseRate?.times(item.totalQuantity)
            var taxableValue = (item.purchaseRate?.times(item.totalQuantity))?.minus((item.itemDiscountAmount!! + item.schemeDiscount!!))
            log.debug("taxableValue : $taxableValue")
            var netGstAmt = if(igst == 0.0) ((sgst as Double + cgst as Double)* taxableValue!!)/100 else (igst* taxableValue!!)/100
            var invoiceAmt = (taxableValue.plus(netGstAmt))

            val bkItem = BkItem(0, now(), now(), item.id, item.code, item.batch,item.mrp, item.totalQuantity,BigDecimal(item.effectivePurchaseRate!!), BigDecimal(item.effectivePurchaseRate!!), itemDiscountPercentage?.toBigDecimal()
            ,item.itemDiscountAmount?.toBigDecimal(),cgst.toBigDecimal(),sgst.toBigDecimal(),igst.toBigDecimal(),item.hsn,invoiceAmt.toBigDecimal().setScale(5, RoundingMode.CEILING),netGstAmt.toBigDecimal(), abtMrp?.toBigDecimal(), item.name, taxableValue.toBigDecimal(), bookkeeperInvoiceDto.invoice.invoiceNo, null
                    ,item.expiryDate, item.schemeDiscountPercentage?.toBigDecimal(), item.schemeQuantity, null,null,null, item.quantity,item.schemeQuantity?.toBigDecimal(), null, item.purchaseRate?.toBigDecimal(), item.schemeDiscount?.toBigDecimal())

            itemList.add(bkItem)
        }

        var dueDate : LocalDate? = invoice.updatedOn!!.toLocalDate()
        if(invoice.purchaseType == "JIT")
            if(supplier?.distributorCommercial?.jitCreditPeriod != null) {
                dueDate = invoice.updatedOn!!.plusDays(supplier?.distributorCommercial?.jitCreditPeriod!!).toLocalDate()
            }
        else if(invoice.purchaseType == "PROCUREMENT") {
                if (supplier?.distributorCommercial?.rpCreditPeriod != null) {
                    dueDate = invoice.updatedOn!!.plusDays(supplier?.distributorCommercial?.rpCreditPeriod!!).toLocalDate()
                }
            }
        val bkInvoice = BkInvoice(0, invoice.updatedOn, invoice.updatedOn, bookkeeperInvoiceDto.user, bookkeeperInvoiceDto.user,
                invoice.id.toString(), invoice.invoiceNo, partnerId, supplier?.partnerName, invoice.totalAmount, 0.0,
                dueDate, status, itemList, null,
                invoice.purchaseType, partnerId
                ?: 0, invoice.partnerDetailId, bookkeeperInvoiceDto.tenant, PartnerType.VENDOR, InvoiceType.VENDOR,null,null,null,invoice.tcsAmount?:0.0,invoice.tcs?:0.0)


        bkInvoice.items.forEach { bkItem ->
            bkItem.bkInvoice = bkInvoice
        }
        if(bookkeeperInvoiceDto.invoice.purchaseType in arrayOf("StockTransferReturn","ICSReturn")){

            log.debug("creating DN for ST and ICS type return")
            debitNoteService.createStIcsDN(bkInvoice,
                    if(bookkeeperInvoiceDto.invoice.purchaseType=="StockTransferReturn")
                        NoteTypes.ST_RETURN
            else
                        NoteTypes.ICS_RETURN
            )
            return bkInvoice
        }

        var savedInvoice = saveBkInvoice(bookkeeperInvoiceDto.user, bkInvoice, partnerId!!)

        try{
            gatePassService.createGatePassInvoiceMapping(bookkeeperInvoiceDto, savedInvoice)
        }catch (e : Exception){
            log.error("Something went wrong while gate pass invoice mapping ",e)
        }
        return savedInvoice
     }

    fun saveBkInvoice(user: String, bkInvoice: BkInvoice, partnerId: Long): BkInvoice {
        log.debug("Saving BkInvoice: ${bkInvoice.invoiceId} : ${bkInvoice.invoiceNum} : $partnerId")
        val inv = bkInvoiceRepo.save(bkInvoice)

    //    log.debug("inside saveBkInvoice $bkInvoice")
        val vl = VendorLedgerDto(
                transactionDate = inv.updatedOn!!.toLocalDate(),
                vendorId = partnerId ?: inv.supplierId!!,
                vendorName = inv.supplierName!!,
                ledgerEntryType = if(bkInvoice.type == PartnerType.CUSTOMER) LedgerEntryType.DEBIT else LedgerEntryType.CREDIT,
                documentType = if(inv.type == PartnerType.VENDOR) DocumentType.PURCHASE_INVOICE else DocumentType.SALE_INVOICE,
                documentNumber = inv.invoiceNum!!,
                referenceNumber =inv.invoiceId,
                externalReferenceNumber = "",
                particulars = if(inv.type == PartnerType.VENDOR) "Purchase Invoice" else "SALE INVOICE",
                debitAmount = if(bkInvoice.type == PartnerType.CUSTOMER) inv.amount.toBigDecimal() else ZERO,
                creditAmount = if(bkInvoice.type == PartnerType.CUSTOMER) ZERO else inv.amount.toBigDecimal() ,
                tenant = bkInvoice.tenant!!,
                partnerId = partnerId,
                partnerDetailId = inv.partnerDetailId,
                type = inv.type,
                client = inv.client,
                transactionalTimestamp = inv.updatedOn
        )
        var companyMappingObj = companyTenantMappingRepo.getByTenant(vl.tenant)!!

        // updating ledger balance if entry is created by retry event api..
        var adjustmentObjForInvoice = AdjustmentEntry(null,null,null,user,null,null,null,user,
                vl.transactionDate,vl.vendorName,vl.ledgerEntryType,
                vl.documentNumber,vl.documentType,vl.type,Status.APPROVED,vl.particulars,
                vl.debitAmount,vl.creditAmount, ZERO,vl.partnerId!!,vl.partnerDetailId,
                vl.tenant,companyMappingObj?.companyId,null,vl.client,null)
        adjustmentService.updateVendorBalance(adjustmentObjForInvoice)
        adjustmentService.addAdjustmentToLedger(adjustmentObjForInvoice,vl.referenceNumber)
        adjustmentService.updateVendorLedgerData(adjustmentObjForInvoice)
//        if(inv.client.name == "RIO") settlementService.autoSettlementAdvancePayment(inv)
        return inv
    }

    fun getBkInvoicesForSupplier(supplierId: Long?, InvoiceNum: String?, status: InvoiceStatus?, tenant: String, customerType: Boolean, ds: String? = null): List<BkInvoiceIdNum> {
        log.debug("Inside getBkInvoicesForSupplier")
        var custType: PartnerType = PartnerType.VENDOR
        if (customerType) custType = PartnerType.CUSTOMER
        var tenants = companyService.findTenants(ds?:tenant!!)
        return bkInvoiceRepo.getBkInvoicesForSupplier(supplierId, InvoiceNum, status, custType, tenants)
    }

    fun getPendingInvoiceAmount(supplierId: Long?, tenant: String, customerType: Boolean, ds: String? = null): Double {
        log.debug("Inside getPendingInvoiceAmount")
        var custType: PartnerType = PartnerType.VENDOR
        if (customerType) custType = PartnerType.CUSTOMER
        var tenants = companyService.findTenants(ds?:tenant!!)
        if (tenants.isNullOrEmpty()) return 0.0
        val amount = bkInvoiceRepo.getPendingInvoiceAmount(supplierId, custType, tenants)
        var res = 0.0
        if (amount != null) res = amount

        return res
    }

    fun getEPRDetails(ucode: String?, invoiceId: Int?): List<AccountItemDTO> = bkInvoiceRepo.getEPRDetails(ucode, invoiceId)

    @Transactional
    fun setTrueEpr(ucode: String?, invoiceId: Long, trueEpr: BigDecimal): Int = bkItemRepo.setTrueEpr(ucode, invoiceId, trueEpr)

    fun getBkInvoiceById(id: Long): BkInvoice? = bkInvoiceRepo.get(id)

    fun getInvoicesBySettlementId(id: Long): List<BkInvoice> {
        log.debug("Getting invoices for settlement id: $id")

        //  return bkInvoiceRepo.getBySettlementId(id)
        return invoiceSettlementRepo.getInvoicesForSettlement(id)
    }

    fun getInvoiceSettlementData(invoiceId: Long, settlementId: Long): InvoiceSettlement {
        log.debug("Fetching invoice settlement data for $invoiceId : $settlementId")

        return invoiceSettlementRepo.findById(InvoiceSettlementId(invoiceId, settlementId)).orElse(null)
    }

    fun getVendorInvoicesData(vendorIds: List<Long>?, partnerId: Long?, partnerDetailId: Long?, tenant: String, page: Int?, size: Int?, customerType: Boolean, ds: String? = null, startAt: String?, endAt: String?, onClick:Boolean?, firmType: List<Int>?, client: List<InvoiceType>): PaginationDto {
        var custType: PartnerType = PartnerType.VENDOR
        if (customerType) custType = PartnerType.CUSTOMER
        val today: LocalDateTime = if (endAt != null) {getUTCDateTime(LocalDate.parse(endAt).atTime(23, 59, 59))} else {getUTCDateTime(LocalDate.now().atTime(23, 59, 59))}
        val from: LocalDateTime = if (startAt != null) {getUTCDateTime(LocalDate.parse(startAt).atTime(0, 0, 0))} else {getUTCDateTime(today.minusYears(10))}
        var tenants = companyService.findTenants(ds?:tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")
        val pagination = PageRequest.of(page ?: 0, size ?: 10)
        var statuses = if(onClick == true){
            mutableListOf(InvoiceStatus.PENDING,InvoiceStatus.PARTIAL_PAID)
        }else{
            mutableListOf(InvoiceStatus.PENDING,InvoiceStatus.PARTIAL_PAID,InvoiceStatus.PAID)
        }
        var res: Page<VendorInvoiceDto>
        var vendorIdFiltered: List<Long>?
        var getAllFlag: Boolean = false


        if(partnerId != null) {
            vendorIdFiltered = if(vendorIds != null) {
                if(vendorIds.contains(partnerId)) listOf<Long>(partnerId) else listOf<Long>()
            } else {
                listOf<Long>(partnerId)
            }
        }else {
            if(!vendorIds.isNullOrEmpty()){
                vendorIdFiltered = vendorIds
            } else {
                vendorIdFiltered = listOf<Long>()
                getAllFlag = true
            }
        }

        res = if (vendorIdFiltered.isNullOrEmpty()) {
            if(getAllFlag) {
                bkInvoiceRepo.getVendorInvoiceData(from, today, custType, partnerDetailId, tenants, statuses, client, pagination)
            } else {
                Page.empty<VendorInvoiceDto>()
            }
        } else {
            bkInvoiceRepo.getVendorInvoiceDataWithVendorIds(vendorIdFiltered, from, today, custType, partnerDetailId, tenants, statuses, client, pagination)
        }

        var supplierIdList = res.map { it.vendorId }.toMutableList()
        var supplierMap = supplierProxy.supplier(supplierIdList).associateBy { it?.partnerId }
        var vendorMap = mutableMapOf<Long,String>()
        if(customerType && !supplierIdList.isNullOrEmpty()){

            var vendorClient  = bkInvoiceRepo.getVendorClient(from, today, custType, tenants,supplierIdList)
            vendorClient.forEach {
               var listOfClient = vendorMap.get(it.vendorId)
                if(listOfClient== null){
                    vendorMap[it.vendorId!!] = it.client?:""
                }else{
                    vendorMap[it.vendorId!!] = listOfClient+"/"+it.client
                }

            }
        }

        res.forEach { vendInvoiceDto ->

            if (vendInvoiceDto.vendorId != null) {
                val supplier = supplierMap.get(vendInvoiceDto.vendorId)
                if (supplier != null) vendInvoiceDto.vendorName = supplier.partnerName!!

                if(customerType){
                    vendInvoiceDto.client  = vendorMap.get(vendInvoiceDto.vendorId?:0L)
                }
            }
        }

        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), res)
    }

    fun getAggregatedInvoiceData(partnerIds: List<Long>?, partnerId: Long?,partnerDetailId: Long?, tenant: String, customerType: Boolean, ds: String? = null, startAt: String?, endAt: String?,firmType:List<Int>?): AggregatedInvoiceDataDto {
        log.debug("Inside getAggregatedInvoiceData $partnerIds : $tenant")
        var custType: PartnerType = PartnerType.VENDOR
        if (customerType) custType = PartnerType.CUSTOMER
        val today: LocalDateTime = if (endAt != null) {getUTCDateTime(LocalDate.parse(endAt).atTime(23, 59, 59))} else {getUTCDateTime(LocalDate.now().atTime(23, 59, 59))}
        val from: LocalDateTime = if (startAt != null) {getUTCDateTime(LocalDate.parse(startAt).atTime(0, 0, 0))} else {getUTCDateTime(today.minusYears(10))}
        val mapper = jacksonObjectMapper()
        val invoiceAggregatedDataCacheKey = "bookkeeper_invoice_aggregated_data_${tenant}_${custType}_${ds}"
        if(partnerIds.isNullOrEmpty() && startAt.isNullOrEmpty() && endAt.isNullOrEmpty() && partnerId == null && firmType.isNullOrEmpty()){
            // using cache only for summary page - not for other filter lookups
            var cacheResponseString = redisUtilityService.getfromRedis(invoiceAggregatedDataCacheKey)
            if(!cacheResponseString.isNullOrEmpty()){
                var cacheResponseDTO: AggregatedInvoiceDataDto = mapper.readValue(cacheResponseString)
                return cacheResponseDTO
            }
        }
        var firmIds =  getAllFirms()
        var firm : MutableList<Int>? = mutableListOf()
        if(firmType==null) {
            firmIds!!.forEach { it -> firm!!.add(it!!.id) }
        }else{
            firmType.forEach{it->
                firm!!.add(it)
            }

        }


        var tenants = companyService.findTenants(ds?:tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")

        log.debug("$from : $today : $tenant : $tenants : $partnerIds")

        var res: AggregatedInvoiceDataDto
        var partnerIdFiltered: List<Long>?
        var getAllFlag: Boolean = false

        if(partnerId != null) {
            if(partnerIds != null) {
                partnerIdFiltered  = if(partnerIds.contains(partnerId)) listOf<Long>(partnerId) else listOf<Long>()
            } else {
                partnerIdFiltered = listOf<Long>(partnerId)
            }
        } else if(firmType != null){
            var pIds = supplierProxy.getPartnersByFirmId(firm!!.toList())
            var pId : MutableList<Long> = mutableListOf()
            pIds!!.forEach{
                    it->pId.add(it?.partnerId!!.toLong())
            }
            partnerIdFiltered = pId
        }else {
            if(!partnerIds.isNullOrEmpty()){
                partnerIdFiltered = partnerIds
            } else {
                partnerIdFiltered = listOf<Long>()
                getAllFlag = true
            }
        }

        try {
            if (partnerIdFiltered.isNullOrEmpty()) {
                if(getAllFlag) {
                    res = bkInvoiceRepo.getAggregatedInvoiceData(from, today, custType, partnerDetailId, tenants)
                    if(startAt.isNullOrEmpty() && endAt.isNullOrEmpty()) {
                        redisUtilityService.setToRedis(invoiceAggregatedDataCacheKey, mapper.writeValueAsString(res), null)
                        log.debug("Cache miss for invoice aggregated data $invoiceAggregatedDataCacheKey. Cache set at ${now()}")
                    }
                } else {
                    res = AggregatedInvoiceDataDto()
                }
            } else {
                res = bkInvoiceRepo.getAggregatedInvoiceDataWithPartnerIds(partnerIdFiltered, partnerDetailId, from, today, custType, tenants)
            }
        } catch (e: Exception) {
            res = AggregatedInvoiceDataDto(0L, ZERO, 0L, ZERO)
        }

        return res
    }

    fun getAllUpdatedVenderList(tenant: String, customerType: Boolean, ds: String? = null): List<SupplierListDTO?> {

        var tenants = companyService.findTenants(ds?:tenant)

        if (tenants.isNullOrEmpty()) return mutableListOf()
        var custType: PartnerType = PartnerType.VENDOR
        if (customerType) custType = PartnerType.CUSTOMER
        val mapper = jacksonObjectMapper()
        val cacheKey = "bookkeeper_supplier_data_${custType}_${tenant}_${ds}"
        var cacheResponseString = redisUtilityService.getfromRedis(cacheKey)
        if(!cacheResponseString.isNullOrEmpty()){
            var cacheResponseDTO: List<SupplierListDTO> = mapper.readValue(cacheResponseString)
            return cacheResponseDTO
        }

        try {

            var listOfPartnerIdData = bkInvoiceRepo.getAllSupplier(tenants, custType).associateBy { it!!.partnerId }
            var listOfPartnerDetailId = bkInvoiceRepo.getAllPartnerDetailId(tenants,custType)

            if (listOfPartnerDetailId.isNullOrEmpty())
                return mutableListOf()
            else {
                var partnerResult = supplierProxy.allTotalSupplier(true, listOfPartnerDetailId)
                partnerResult.forEach {
                    if (listOfPartnerIdData.containsKey(it!!.partnerId)) {
                        it!!.client = listOfPartnerIdData.get(it!!.partnerId)!!.client
                    }
                }
                redisUtilityService.setToRedis(cacheKey, mapper.writeValueAsString(partnerResult), null)
                log.debug("Cache miss for vendor list data $cacheKey. Cache set at ${now()}")
                return partnerResult
            }
        } catch (e: Exception) {
            log.debug("error in get all vendor list : ${e} ")
            return mutableListOf()
        }
    }


    fun getAllSupplierWithoutInvoice(customerType: Boolean,ds:String?,tenant:String, partnerName: String?, partnerDetailId: Long?): MutableList<PartnerDetailDto> {

        log.debug("inside getAllSupplierWithoutInvoice customerType $customerType")

        if(ds!=null && !customerType){
            var tenantPartnerInfo = supplierProxy.supplierByTenant(tenant)
            return if(tenantPartnerInfo.isEmpty())
                mutableListOf()
            else
                mutableListOf(PartnerDetailDto(tenantPartnerInfo[0]?.id?.toInt(),tenantPartnerInfo[0]?.partnerId?.toInt(),tenantPartnerInfo[0]?.name,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,InvoiceType.VENDOR))
        }

        var firmTypeIds = mutableListOf<Int>()
        var firmTypeVendorIds = mutableListOf<Int>()

        try{
            var firmsTypes = supplierProxy.getFirmTypes(null,null,null)
            var firmsTypesMap = firmsTypes?.associateBy { it!!.id }

            log.debug("response from supplier firmsTypes $firmsTypes")

            firmsTypes?.forEach {

                if(ds != null && it!!.id == retailerMarketPlace){
                    firmTypeIds.add(it.id)
                }
                if (it!!.id == retailer || it!!.id == retailerAlpha ||it!!.id == retailerInClinic ||it!!.id == retailerFOFO ||it!!.id == retailerCOCO ||it!!.id == retailerHospitals ||it!!.id == hospitals ||it!!.id == retailerPL  ){

                        firmTypeIds.add(it.id)
                }
                else if(it!!.id == cnf || it!!.id == distributor || it!!.id == holding || it!!.id == manufacturer){
                    firmTypeVendorIds.add(it.id)
                }
            }
            var partnerListdata = mutableListOf<PartnerDetailDto>()
            log.debug("firmTypeIds--> $firmTypeIds")
            log.debug("firmTypeVendorIds--> $firmTypeVendorIds")
            when(customerType){
                true -> {
                    partnerListdata = if(!partnerName.isNullOrEmpty()){
                        supplierProxy.getPartnerDetails(partnerName,null,null,null,null) as MutableList<PartnerDetailDto>
                    }else if(partnerDetailId != null){
                        supplierProxy.getPartnerDetails(null,null,null,null,partnerDetailId.toInt()) as MutableList<PartnerDetailDto>
                    }else{
                        supplierProxy.getPartnerDetails(partnerName,firmTypeIds,null,null,null) as MutableList<PartnerDetailDto>
                    }
                }

                false ->{
                    partnerListdata = if(!partnerName.isNullOrEmpty()){
                        supplierProxy.getPartnerDetails(partnerName,null,null,null,null) as MutableList<PartnerDetailDto>
                    }else if(partnerDetailId != null){
                        supplierProxy.getPartnerDetails(null,null,null,null,partnerDetailId.toInt()) as MutableList<PartnerDetailDto>
                    }else{
                        supplierProxy.getPartnerDetails(partnerName,firmTypeVendorIds,null,null,null) as MutableList<PartnerDetailDto>
                    }
                }
            }
            log.debug("partnerListdata--> $partnerListdata")

            partnerListdata.forEach{
                it.firmTypes?.forEach { firm ->
                    var firmDTO = firmsTypesMap!!.get(firm)
                    if (firmDTO!!.id == retailer || firmDTO!!.id == retailerMarketPlace ||it!!.id == retailerInClinic ||it!!.id == retailerFOFO ||it!!.id == retailerCOCO ||it!!.id == retailerHospitals ||it!!.id == hospitals ||it!!.id == retailerPL){
                        it.client = InvoiceType.RIO
                    }
                    else if (firmDTO!!.id == retailerAlpha ){
                        it.client = InvoiceType.PE
                    }else if(firmDTO!!.id == cnf || firmDTO!!.id == distributor || firmDTO!!.id == holding || firmDTO!!.id == manufacturer){
                        it.client = InvoiceType.VENDOR
                    }
                }
            }
            log.debug("partnerListdata $partnerListdata")
            return  partnerListdata
        }catch (e: Exception){
            log.debug("error in get all supplier list : ${e} ")
            return mutableListOf()
        }
    }

    // invoice url
    fun getInvoiceURL(tenant: String, createdBy: String?, customerType: Boolean, ds: String? = null, startAt: String?, endAt: String?,firmType:List<Int>?,client: List<InvoiceType>): CreateResultData {
        log.debug("Inside getInvoiceURL")
        var tenants = companyService.findTenants(ds?:tenant)
        var custType: PartnerType = PartnerType.VENDOR
        if (customerType) custType = PartnerType.CUSTOMER
        if (tenants.isNullOrEmpty()) return CreateResultData(200, "No Data", null)

        val toDateTime: LocalDateTime? = if (endAt != null) {getUTCDateTime(LocalDate.parse(endAt).atTime(23, 59, 59))} else {null}
        val fromDateTime: LocalDateTime? = if (startAt != null) {getUTCDateTime(LocalDate.parse(startAt).atTime(0, 0, 0))} else {null}
        log.debug("Inside getInvoiceURL getting filter dates $fromDateTime $toDateTime")

        var list = if(fromDateTime !=null && toDateTime != null && client==null){
            bkInvoiceRepo.getVendorInvoiceDataWithDateForUrl(custType, tenants, fromDateTime, toDateTime)
        }else if(fromDateTime !=null && toDateTime != null && client != null){
            bkInvoiceReadRepo.getVendorInvoiceDataWithDateandPartnerForUrl(custType, tenants, fromDateTime, toDateTime,client)
        }else if(fromDateTime ==null && toDateTime == null && client != null){
            bkInvoiceReadRepo.getVendorInvoiceDataWithPartnerForUrl(custType, tenants, client)
        }
        else{
            log.debug("Not Going into getVendorInvoiceDataWithDateForUrl $fromDateTime $toDateTime")
            bkInvoiceRepo.getVendorInvoiceDataForUrl(custType, tenants)
        }

        if (list.isNullOrEmpty())
            throw RequestException("Data is not available")

        var companyMappingObj = companyService.getCompanyTenantMappingObject(ds?:tenant) ?: throw RequestException("no tenant mapping found for $tenant")
        var listVendorId: List<Long?>? = null
        log.debug("$companyMappingObj")
        list.forEach { vendInvoiceDto ->

        if (vendInvoiceDto != null) {
            if (vendInvoiceDto.vendorId != null)
            //    vendInvoiceDto.vendorName = partnerService.getSupplierName(vendInvoiceDto.vendorId!!, companyMappingObj.companyId!!, custType)!!
                listVendorId = listOf(vendInvoiceDto.vendorId)

            var vendor = supplierProxy.supplier(listVendorId)
            vendor.forEach { v ->
                if (v != null) {
                    vendInvoiceDto.vendorName = v.partnerName
                }
            }
        }
    }


    var prefix = "InvoiceList-"


        try {
            var file = vendorFileService.saveReport(companyMappingObj.tenant, VendorDataLinks(null, now(), null, "IN_PROGRESS", VendorDataEnum.INVOICE, null, createdBy,companyMappingObj.tenant,customerType))
            writeInvoiceData(file.id!!, list, companyMappingObj.tenant, prefix,customerType)

        } catch (e: Exception) {
            e.printStackTrace()
        }
        return CreateResultData(200, "Success", null)

    }

    @Async
    fun writeInvoiceData(id: Long, successList: MutableList<VendorInvoiceDto?>, tenant: String, prefix: String?, customerType:Boolean) {
        log.debug("Inside writeInvoiceData: writting data into csv")
        log.debug("VendorInvoiceDTo successList : $successList")

        val csvPrinter: CSVPrinter
        var bytes = ByteArrayOutputStream()
        if(customerType) {
            csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT
                    .withHeader("Customer Id", "Customer Name", AccountConstants.CUSTOMER_TYPE, "Total No. Of Invoices", "No. Of Paid Invoices", "No. Of Pending/Partial Invoices", "Pending/Partial Invoices AMT", "No. Of Overdue Invoices", "Overdue Invoice AMT"))
        }else{
            csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT
                    .withHeader("Vendor Id", "Vendor Name", "Total No. Of Invoices", "No. Of Paid Invoices", "No. Of Pending/Partial Invoices", "Pending/Partial Invoices AMT", "No. Of Overdue Invoices", "Overdue Invoice AMT"))
        }
        successList!!.forEach {
            if (it != null) {
                if(customerType){
                    csvPrinter.printRecord(it.vendorId, it.vendorName, it.client, it.totalInvoices, it.totalPaidInvoices, it.totalPendingInvoices, it.totalPendingInvoiceAmount.setScale(2, RoundingMode.HALF_EVEN), it.totalOverdueInvoices, it.totalOverdueInvoicesAmount.setScale(2, RoundingMode.HALF_EVEN))
                }else
                    csvPrinter.printRecord(it.vendorId, it.vendorName, it.totalInvoices, it.totalPaidInvoices, it.totalPendingInvoices, it.totalPendingInvoiceAmount.setScale(2, RoundingMode.HALF_EVEN), it.totalOverdueInvoices, it.totalOverdueInvoicesAmount.setScale(2, RoundingMode.HALF_EVEN))
            }
        }

        csvPrinter.flush()
        val uploaded = s3FileUtilityService.uploadFile(bytes.toByteArray(), "CSV", prefix)
        vendorFileService.update(id, uploaded ?: "Failed")
    }

    fun getInvoiceDownload(createdBy: String, tenant: String, customerType: Boolean?, ds: String? = null): CreateResultData {

        var tenants = if(ds!=null) companyService.findTenants(ds?:tenant)[0] else tenant
        var file=invoiceDataLinkRepo.getInvoiceLink(createdBy,tenants,customerType)
        if (file.isNotEmpty()) {
            return CreateResultData(200, "Success", file[0]!!.link)
        }


        return CreateResultData(200, "Success", null)
    }

// invoice detail url
    fun getInvoiceDetailURL(partnerId: Long?, tenant: String, createdBy: String?, createdOnFrom: LocalDate?, createdOnTo: LocalDate?, status: InvoiceStatus?, overdue: Boolean, dueDateFrom: LocalDate?, dueDateTo: LocalDate?, customerType: Boolean, ds: String? = null): CreateResultData {
        log.debug("Inside getInvoiceDetailURL")
        var custType: PartnerType = PartnerType.VENDOR
        if (customerType) custType = PartnerType.CUSTOMER
        var tenants = companyService.findTenants(ds?:tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")
        var statuses: MutableList<InvoiceStatus>? = null
        log.debug("***** $status ****")

        if (status != null) {
        statuses = mutableListOf()
        statuses.add(status)
        if (status == InvoiceStatus.PENDING) {
            statuses.add(InvoiceStatus.PARTIAL_PAID)
        }
        log.debug("&&&&&&&  $statuses  &&&&&&")
                            }
        log.debug("createdOnFrom: $createdOnFrom createdOnTo: $createdOnTo")
        val list = bkInvoiceRepo.findAll(SupplierInvoiceSpecification(partnerId, null, statuses, createdOnFrom,
                createdOnTo, dueDateFrom, dueDateTo, null, tenants, overdue, custType, null, null))

        if (list.isNullOrEmpty())
            throw RequestException("Data is not available")

        var prefix = "InvoiceDetails-"


        try {
            var file = vendorFileService.saveReport(tenants[0]!!, VendorDataLinks(null, now(), null, "IN_PROGRESS", VendorDataEnum.INVOICE_DETAIL, null, createdBy,tenants[0]!!,customerType))

            writeInvoiceDetailData(file.id!!, list, tenants[0]!!, prefix, customerType)

        } catch (e: Exception) {
            e.printStackTrace()
        }
        return CreateResultData(200, "Success", null)

    }

    @Async
    fun writeInvoiceDetailData(id: Long, successList: MutableList<BkInvoice?>, tenant: String, prefix: String?, customerType:Boolean) {
        log.debug("Inside writeInvoiceDetailData: writting data into csv")
        val csvPrinter: CSVPrinter
        var bytes = ByteArrayOutputStream()
        if(customerType){
            csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT
                    .withHeader("Invoice No.", "Invoice Date", "Due Date", "Source", "Order Id", "Invoice Amount", "Overdue(No. Of Days)", "Payment Date", "Settlement Number", "Status"))
        }else {
            csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT
                    .withHeader("Invoice No.", "Invoice Live Date", "Vendor Invoice Date", "Due Date", "Source", "Purchase Type", "Invoice Amount", "Overdue(No. Of Days)", "Payment Date", "Settlement Number", "Status"))
        }

        successList!!.forEach {
              var companyTenant = companyTenantMappingRepo.getAllTenantByCompany(tenant).associateBy { it?.tenant }

            if (it?.invoiceId != null) {

                val settleNum = getSettlementsForInvoice(it.id)

                var settlementNumber = StringBuffer()
                settleNum.forEach { sn ->
                    settlementNumber.append(sn.settlementNumber + ",")
                }

                var overdueDays  = if(it.dueDate == null || it.dueDate!! > LocalDate.now()) null else ChronoUnit.DAYS.between(it.dueDate!!, LocalDate.now())

                if (customerType) {
                    csvPrinter.printRecord(it.invoiceNum, it.createdOn?.toLocalDate(), it.dueDate, companyTenant[it.tenant]?.tenantName, it.invoiceId ,it.amount, overdueDays, it.settledOn?.toLocalDate(), settlementNumber, it.status)
                } else
                    csvPrinter.printRecord(it.invoiceNum, it.createdOn?.toLocalDate(), it.gatePassInvoice?.invoiceDate, it.dueDate, companyTenant[it.tenant]?.tenantName, it.purchaseType, it.amount, overdueDays, it.settledOn?.toLocalDate(), settlementNumber, it.status)
            }
        }

        csvPrinter.flush()
        val uploaded = s3FileUtilityService.uploadFile(bytes.toByteArray(), "CSV", prefix)
        vendorFileService.update(id, uploaded ?: "Failed")
    }

    fun getInvoiceDetailDownload(createdBy: String, tenant: String, customerType: Boolean, ds: String? = null): CreateResultData {

        var tenants = companyService.findTenants(ds?:tenant)
        var file=invoiceDataLinkRepo.getInvoiceDetailLink(tenants[0]!!,createdBy, customerType)

        if (file.isNotEmpty()) {
            return CreateResultData(200, "Success", file[0]!!.link)
        }

        return CreateResultData(200, "Success", null)
    }

    fun getSettlementsForInvoice(id: Long): MutableList<InvoiceSettlementWithCNDto> {
        log.debug("Inside getSettlementsForInvoice : $id")
        //var listfin = mutableListOf<String>()
        var finalres = mutableListOf<InvoiceSettlementWithCNDto>()
        log.debug("After List Declaration")
        var res = invoiceSettlementRepo.getSettlementInfoForInvoice(id)
        log.debug("Invoice settlement repo result : $res")
        res.forEach { it ->
            log.debug("It Value : $it")
            var settlement = settlementRepo.getSettlementBySettlementNumber(it.settlementNumber)
            log.debug("Before Settlements : $settlement")
            if (settlement != null) {
                log.debug("Inside Settlements : $settlement")
                var creditNotesList = mutableListOf<String>()
                if (it.paymentType == PaymentType.CREDITNOTE)    // Add code here
                {
                    log.debug("Inside If Block")
                    var settlementID = settlement.id
                    log.debug("Inside If Block Code 5AM settlement Id : $settlementID")
                    creditNotesList = settlementRepo.getCreditNoteListBySettlementID(settlementID)
                    log.debug("Credit Note List : $creditNotesList")
                }
                finalres.add(
                    InvoiceSettlementWithCNDto(
                        settlementNumber = it.settlementNumber,
                        settlementDate = it.settlementDate,
                        paymentDate = it.paymentDate,
                        amount = it.amount,
                        chequeDate = it.chequeDate,
                        bankName = it.bankName,
                        isBounced = it.isBounced,
                        paymentType = it.paymentType,
                        paidAmount = it.paidAmount,
                        external_reference_id = it.external_reference_id,
                        creditNoteNumbers = creditNotesList
                    )
                )
                log.debug(" Credit Note Numbers : $finalres")
            }
        }
        return finalres
        //below is the original code of this method
        //return invoiceSettlementRepo.getSettlementInfoForInvoice(id)
    }

    @Transactional
    fun createDarkStoreInvoice(storeOrderInvoice: StoreOrderInvoice, darkStoreInvoiceDto: DSInvoiceDto) {

        var ds = companyService.getTenantByPDI(darkStoreInvoiceDto.partnerDetailId?:throw RequestException("WMS need to pass new parameter partner detail id in payload in order for ds invoice creation"))
        var dsTenant = companyService.findTenants(ds.tenant?:darkStoreInvoiceDto.tenant)
        if(dsTenant.isEmpty()) throw RequestException("DS mapping not found for wh $darkStoreInvoiceDto.tenant")
        log.debug("ds tenants : $dsTenant")

        var whSaleInvVault = bkInvoiceRepo.getByInvoiceIdForDS(darkStoreInvoiceDto.externalOrderId,darkStoreInvoiceDto.tenant!!)?: throw RequestException("Sale invoice not found ")


        var storeInvoice = createStoreInvoice(storeOrderInvoice, darkStoreInvoiceDto.user)

        var invoice = bkInvoiceRepo.getByInvoiceId(storeInvoice.retailerInvoiceData!!.externalOrderId!!, storeInvoice!!.id!!, mutableListOf(dsTenant[0]!!))

        if(invoice != null) return

        createCustomerRIO(storeInvoice, BookkeeperCustomerInvoiceDto(dsTenant[0]!!,darkStoreInvoiceDto.user,darkStoreInvoiceDto.externalOrderId),whSaleInvVault.partnerDetailId!!)

        // create purchase invoice for ds from sale invoice of warehouse..


        var copywhSaleInvoice = whSaleInvVault.copy()

        copywhSaleInvoice.id = 0
        copywhSaleInvoice.items = copywhSaleInvoice.items.map { it.copy() } as MutableList<BkItem>
        copywhSaleInvoice.items.forEach {

            it.id = 0
            it.bkInvoice = copywhSaleInvoice
            it.purchaseRate = it.ptr
        }

        var tenantPartnerInfo = supplierProxy.supplierByTenant(darkStoreInvoiceDto.tenant)
        if(tenantPartnerInfo.isNullOrEmpty()) throw RequestException("Partner info not found for Tenant ${darkStoreInvoiceDto.tenant} ")
        copywhSaleInvoice.type = PartnerType.VENDOR
        copywhSaleInvoice.client = InvoiceType.VENDOR
        copywhSaleInvoice.invoiceId = "DS-${copywhSaleInvoice.invoiceId}"
        copywhSaleInvoice.purchaseType = "PROCUREMENT"
        copywhSaleInvoice.supplierId = tenantPartnerInfo.get(0)!!.partnerId
        copywhSaleInvoice.partnerId = tenantPartnerInfo.get(0)!!.partnerId
        copywhSaleInvoice.partnerDetailId = tenantPartnerInfo.get(0)!!.id
        copywhSaleInvoice.supplierName = tenantPartnerInfo.get(0)!!.name
        copywhSaleInvoice.tenant = dsTenant[0]
        copywhSaleInvoice.dueDate = whSaleInvVault.dueDate
        copywhSaleInvoice.createdOn = storeOrderInvoice.createdOn?: now()
        copywhSaleInvoice.updatedOn = storeOrderInvoice.createdOn?: now()
        saveBkInvoice(darkStoreInvoiceDto.user,copywhSaleInvoice,tenantPartnerInfo.get(0)!!.partnerId!!)

        // customer ds CN settlement if reserved
        var dsCustomerInvoice = bkInvoiceRepo.getDSCustomerInvoice(darkStoreInvoiceDto.externalOrderId, dsTenant[0]!!)

        if(dsCustomerInvoice != null){
            settleInvoiceWithBlockedCNs(dsCustomerInvoice, ds.tenant, darkStoreInvoiceDto.tenant)
        }

    }

    fun createStoreInvoice(storeOrderInvoice: StoreOrderInvoice, user: String): StoreInvoice {
        return StoreInvoice(storeOrderInvoice.id!!, SaleOrder(storeOrderInvoice.storeOrder!!.externalOrderId, storeOrderInvoice.storeOrder!!.externalOrderId, null, null, null, null, null, null,
                null, null, storeOrderInvoice.storeOrder!!.destinationPartnerName, null, null, null, null, null, null, null, null,
                null, null, null, null, null, null, null, null, false,
                null, null, null, null, null, false, null, null, null,
                null, storeOrderInvoice.storeOrder!!.destinationPartnerId, null, null), null, storeOrderInvoice.externalOrderId!!,
                null, storeOrderInvoice.invoiceAmount, user, storeOrderInvoice.createdOn
                ?: now(), null, null, null, storeOrderInvoice.retailerInvoiceData)
    }

    @Transactional
    fun settleInvoiceWithBlockedCNs(invoice: BkInvoice, ds: String? = null, whTenant: String) {
        var orderType = if(ds != null) {OrderType.B2B2B} else {OrderType.B2B}
        var blockedCNRDtos = creditNoteReservationRepo.findByExternalOrderIdAndStatuses(invoice.invoiceId!!, orderType, mutableListOf(CreditNoteReservationStatus.RESERVED))
        if (blockedCNRDtos.isNullOrEmpty()) {return}
        var blockedCNs: MutableList<CreditNote> = mutableListOf()
        var totalCNamount = ZERO
        blockedCNRDtos.forEach { cnrDto ->
            var cnCopy = creditNoteRepo.get(cnrDto.creditNoteId)!!.copy()
            cnCopy.amountUsed = cnrDto.amountUsed
            cnCopy.deductRemainingAmount = false
            blockedCNs.add(cnCopy)
            totalCNamount += cnrDto.amountUsed
        }
        var invoiceCpy = invoice.copy()
        val distributorTenant = if(invoice.distributorPdi!! == 0L){
            invoice.tenant
        }else{
            var distributorDetails = getTenantCodeAndCustomerType(invoice.distributorPdi!!)
            distributorDetails.tenant
        }
        invoiceCpy.paidAmount = invoiceCpy.paidAmount.plus(totalCNamount.toDouble())
        var settlement = Settlement(
            0,
            null,
            null,
            null,
            invoice.partnerId!!,
            invoice.supplierName,
            totalCNamount.toDouble(),
            totalCNamount.toDouble(),
            "Settlement with reserved CNs",
            null,
            mutableListOf(invoiceCpy),
            blockedCNs,
            PaymentType.CREDITNOTE,
            "",
            LocalDate.now(),
            invoice.partnerId,
            null,
            PartnerType.CUSTOMER,
            distributorTenant
                ?: "",
            null,
            null,
            null,
            false,
            reversed = false,
            advancePayment = mutableListOf(),
            chargeInvoice = mutableListOf(),
            charge = false,
            receipt = null,
            paymentSource = AdvancePaymentSource.SYSTEM,
            retailerDebitNotes = mutableListOf(),
            uuid = UUIDUtil.generateUuid()
        )
        settlementService.save("SYSTEM", settlement, ds)

        var cnr = creditNoteReservationRepo.findByExternalOrderId(invoice.invoiceId!!)!!
        cnr.completedOn = now()
        cnr.status = CreditNoteReservationStatus.COMPLETED
        creditNoteReservationRepo.save(cnr)
    }

    @Transactional
    fun createConsoleInvoice(consolidatedStoreInvoiceDto: ConsolidatedStoreInvoiceDto?, customerDTO: InvoiceNotificationDTO) {
        log.debug("Inside createCustomerInvoice $consolidatedStoreInvoiceDto")
        var customerInvoiceDto=consolidatedStoreInvoiceDto

        var invoice = bkInvoiceRepo.getByInvoiceId(customerInvoiceDto!!.id.toString(),customerInvoiceDto!!.invoiceId!!, mutableListOf(customerDTO.tenant))

        if(invoice != null) return

        if(customerInvoiceDto != null && customerDTO!!.client != "RIO" && customerDTO!!.client != null ) {
            var invoiceObj = createInvoiceObjPEFromConsole(customerDTO.user,customerInvoiceDto, customerDTO.tenant,customerDTO.client!!)
            saveBkInvoice(customerDTO.user, invoiceObj, invoiceObj?.partnerId!!)
        }else{
            log.debug("createCustomerInvoice failed : ${customerDTO.client} not allowed !")
        }
    }


    fun createInvoiceObjPEFromConsole(user: String, invoice: ConsolidatedStoreInvoiceDto, tenant:String,client:String): BkInvoice {

        val status = if (invoice!!.amount!!.equals(0)) {
            InvoiceStatus.PAID
        } else {
            InvoiceStatus.PENDING
        }
        var supplierList = supplierProxy.supplier(null, invoice.partnerDetailId!!.toLong())
        var supplier = if (supplierList.isNotEmpty()) supplierList.get(0) else throw RequestException("Could not get Supplier List for partnerId ${invoice!!.partnerDetailId} for tenant " +
                "${tenant} ,while invoice creation.")

        val itemList: MutableList<BkItem> = mutableListOf()
        var items = invoice?.pickedItems

        items!!.forEach { item ->

//            var taxPercent = if (invoice.interState!!) item.igst ?: ZERO else item.cgst ?: ZERO + (item.sgst ?: ZERO)

            var abtMrp = item.rate
            log.debug("abtMpr : $abtMrp")
            var taxableValue = item.taxableValue
            log.debug("taxableValue : $taxableValue")
            var netGstAmt = item.tax
            var invoiceAmt = (taxableValue?.plus(netGstAmt?: ZERO))
            var discountAmt = item.discountAmount

            var saleUnitPriceCheck = item.saleUnitPrice
            var saleUnitPriceTypeCheck = item.saleUnitPriceType

            log.debug("createInvoiceObjPEFromConsole saleUnitPriceCheck: $saleUnitPriceCheck")
            log.debug("createInvoiceObjPEFromConsole saleUnitPriceTypeCheck: $saleUnitPriceTypeCheck")

            val bkItem = BkItem(0, now(), now(), item.id!!, item.ucode!!, item.batchNumber!!, item.mrp!!.toDouble(),
                item.quantity?:0, ZERO, ZERO, item.discountPercentage, discountAmt, if (!invoice.interState!!) item.cgst else ZERO,if (!invoice.interState!!) item.sgst else ZERO,if (invoice.interState!!) item.igst else ZERO, item.hsnCode, invoiceAmt, netGstAmt, abtMrp, item.name, taxableValue, invoice.invoiceId, null, item.expiryDate, ZERO, 0, ZERO, "", ZERO, 0, ZERO, ZERO, ZERO, ZERO, ZERO, ZERO, item.saleUnitPrice, item.saleUnitPriceType)

            itemList.add(bkItem)
        }

        var creditPeriod = supplier!!.retailerCommercial!!.creditPeriod!!

        val bkInvoice = BkInvoice(0, invoice.createdOn, invoice.createdOn, user, user,invoice.id.toString(),
                invoice.invoiceId, invoice!!.partnerDetailId,
                supplier?.partnerName, invoice.amount!!.toDouble(), 0.0, invoice.createdOn!!.plusDays(creditPeriod!!).toLocalDate(), status, itemList, null,
                null, supplierList.get(0)!!.partnerId
                ?: 0, invoice!!.partnerDetailId, tenant, PartnerType.CUSTOMER, InvoiceType.valueOf(client))

        bkInvoice.items.forEach { bkItem ->
            bkItem.bkInvoice = bkInvoice
        }
        return bkInvoice
    }

    @Transactional
    fun createBookkeeperRioInvoiceData(rioInvoiceDataDto: RioInvoiceDataDto){
        var distributorDetails = getTenantCodeAndCustomerType(rioInvoiceDataDto.distributorId)
        var distributorTenant = distributorDetails.tenant
        var orderType = distributorDetails.fulfilmentType
        var customerType = orderType==OrderType.B2B2B
        var dsCompany: CompanyTenantMapping? = null
        if(customerType) {
            dsCompany = companyService.getTenantByPDI(rioInvoiceDataDto.distributorId)
        }
        var tenant = companyService.getCompanyTenantMappingObject(dsCompany?.tenant?:distributorTenant)?.tenant

        var supplier = supplierProxy.supplier(null, rioInvoiceDataDto.retailerId)

        var partnerId = supplier[0]?.partnerId
        var partnerName = supplier[0]?.partnerName


        var bkInvoiceData = BkInvoice(
            id = 0,
            createdOn = now(),
            createdBy = "SYSTEM",
            invoiceId = rioInvoiceDataDto.orderId ,
            invoiceNum = rioInvoiceDataDto.invoiceNumber ,
            supplierId = rioInvoiceDataDto.retailerId ,
            supplierName = partnerName ,
            amount = rioInvoiceDataDto.invoiceAmount,
            paidAmount = 0.0 ,
            dueDate = rioInvoiceDataDto.dueDate,
            status = InvoiceStatus.PENDING,
            items = mutableListOf(),
            partnerId = partnerId ,
            partnerDetailId = rioInvoiceDataDto.retailerId ,
            tenant = tenant ,
            type = PartnerType.CUSTOMER,
            client = InvoiceType.RIO,
            settledOn = null,
            updatedOn = null,
            updatedBy = "SYSTEM",
            apiVersion = APIVersionType.V2,
            distributorPdi = rioInvoiceDataDto.distributorId,
            baseInvoiceAmount = rioInvoiceDataDto.baseInvoiceAmount!!,
            roundOffAmount = rioInvoiceDataDto.roundOffAmount
        )

        var dbObj = bkInvoiceRepo.save(bkInvoiceData)
        var entryType = LedgerEntryType.DEBIT

        var vendorLedger = VendorLedgerDto(
            transactionDate = dbObj.createdOn?.toLocalDate()!!,
            vendorId = dbObj?.partnerId!!,
            vendorName = dbObj?.supplierName!!,
            ledgerEntryType = entryType,
            documentType = DocumentType.SALE_INVOICE,
            documentNumber = dbObj.invoiceNum!!,
            referenceNumber = dbObj.invoiceId!!,
            externalReferenceNumber = null,
            particulars = "SALE INVOICE",
            debitAmount = dbObj.amount.toBigDecimal(),
            creditAmount = ZERO,
            partnerDetailId = dbObj.partnerDetailId,
            partnerId = dbObj.partnerId,
            tenant = dbObj.tenant!!,
            type = dbObj.type,
            client = dbObj.client,
            transactionalTimestamp = dbObj.createdOn
        )
        try{
            partnerService.addVendorLedgerEntry("SYSTEM", vendorLedger)
        }catch (e:Exception){
            log.error("Error in partner service",e)
        }
        settleInvoiceWithBlockedCNs(dbObj, dsCompany?.tenant,tenant!!)

    }


    fun getAllFirms(): List<FirmDTO?>? {
        var firms = supplierProxy.getFirmTypes(true,null,null)
        return firms
    }

    fun getTenantCodeAndCustomerType(distributorPDI: Long): DistributorTenantOrderTypeDTO {
        val distributorPartnerMapping = supplierProxy.getDistributorPartnerMapping(
            distributorPDI
        )
        val distributorDetails = DistributorTenantOrderTypeDTO(
            tenant = if (distributorPartnerMapping.distributorPdi == distributorPartnerMapping.warehousePdi) {
                distributorPartnerMapping.tenant
            }else{
                companyService.getTenantByPDI(distributorPDI).tenant
            },
            fulfilmentType = if (
                distributorPartnerMapping.distributorPdi == distributorPartnerMapping.warehousePdi
            ) {
                OrderType.B2B
            } else {
                OrderType.B2B2B
            },
            locationSameAsWarehouse =  if (distributorPartnerMapping.distributorPdi == distributorPartnerMapping.warehousePdi){
                null
            }else {
                distributorPartnerMapping.locationSameAsWarehouse
            }
        )
        return distributorDetails
    }

    fun getRioInvoiceItemData(distributorPdi: Long, retailerPdi: Long, invoiceNumber: String): RioInvoiceItemsDto?{
        return retailIoProxy.getRioItemData(retailIoVersion,retailIoSource,retailIoKey,distributorPdi,retailerPdi,invoiceNumber)
    }

    @Transactional(readOnly = true)
    fun getInvoiceJourney(id: Long?): InvoiceJourneyDTO?{
        var invoiceDebitNoteDetails = invoiceDebitNoteReadRepo.getDebitNoteDetailsByInvoiceId(id!!)
        var invoiceSettlementDetails = invoiceSettlementReadRepo.getSettlementDetailsByInvoiceId(id!!)
        var invoiceDetails = bkInvoiceReadRepo.getOne(id)

        var invoiceDebitNoteJourney: MutableList<InvoiceDebitNoteJourneyDTO> = mutableListOf()
        var invoiceSettlementJourney: MutableList<InvoiceSettlementJourneyDTO> = mutableListOf()

        var supplierDetails = supplierProxy.supplier(null, invoiceDetails.partnerDetailId)

        var firmType = supplierDetails[0]?.firmTypes
        var firmTypeName: MutableList<String> = mutableListOf()
        firmType?.forEach {
            firmTypeName.add(it.firmTypeName!!)
        }
        invoiceDebitNoteDetails?.forEach {
            var debitNoteData = debitNoteReadRepo.getOne(it.id.debitNoteId)
            invoiceDebitNoteJourney.add(InvoiceDebitNoteJourneyDTO(
                debitNoteNumber = debitNoteData.debitNoteNumber,
                debitNoteAmount = debitNoteData.amountReceivable,
                debitNoteDate = debitNoteData.createdOn,
                creditNoteNumber = debitNoteData.creditNoteNumber,
                noteType = debitNoteData.noteType!!,
                returnReferenceType = debitNoteData.returnReferenceType
            ))
        }

        invoiceSettlementDetails?.forEach {
            var settlementData = settlementReadRepo.getOne(it.id.settlementId)
            invoiceSettlementJourney.add(InvoiceSettlementJourneyDTO(
                settlementNumber = settlementData.settlementNumber,
                settledAmount = it.paidAmount,
                paymentType = settlementData.paymentType,
                paymentReference = settlementData.paymentReference,
                paymentDate = settlementData.createdOn
            ))
        }

        return InvoiceJourneyDTO(
            invoiceId = invoiceDetails.invoiceId,
            invoiceAmount = invoiceDetails.amount!!,
            invoiceStatus = invoiceDetails.status,
            firmType = firmTypeName,
            invoiceDebitNoteJourney = invoiceDebitNoteJourney,
            invoiceSettlementJourney = invoiceSettlementJourney
        )
    }

    fun getAllInvoices(partnerDetailId: Long, tenant: String, ds: Boolean= false, page: Int?, size: Int?): PaginationDto{
        val pagination = PageRequest.of(page?: 0, size?: 10)
        val tenants = companyService.findTenants(tenant,ds)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")
        var res = bkInvoiceReadRepo.getRioCollectionInvoicesByPDIAndTenant(partnerDetailId, tenant, pagination)
        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), res)
    }

    fun getRecentInvoiceSettledForPartner(partnerDetailId: Long): MutableList<BkInvoice>{
        val pagination = PageRequest.of(0, 1)
        val res = bkInvoiceReadRepo.getRecentInvoiceSettledForPartner(partnerDetailId, pagination)
        return res.content
    }

    fun getCountByInvoiceNumber(invoiceNumber: String, tenant: String): Long?{
        return bkInvoiceReadRepo.getCountByInvoiceNumber(invoiceNumber, tenant)
    }

    fun findCustomerInvoiceByInvoiceNumAndParentTenant(invoiceNumber: String, tenant: String): BkInvoice? {
        return bkInvoiceReadRepo.findCustomerInvoiceByInvoiceNumAndParentTenant(invoiceNumber, tenant)
    }

    fun findCustomerInvoiceByInvoiceNumAndTenant(invoiceNumber: String, tenant: String): BkInvoice? {
        return bkInvoiceRepo.findCustomerInvoiceByInvoiceNumAndTenant(invoiceNumber, tenant)
    }

    fun getRioInvoice(rioOrderId: String, queryTenant: String): BkInvoice{
        return bkInvoiceRepo.getRioInvoice(rioOrderId, queryTenant)!!
    }

    fun findCustomerInvoiceByInvoiceNum(invoiceNumber: String): BkInvoice? {
        return bkInvoiceRepo.findCustomerInvoiceByInvoiceNum(invoiceNumber)
    }

    @Transactional
    fun closeLessThanOneRupeeInvoices(createdOnBuffer: LocalDateTime){
        val bkInvoices = bkInvoiceReadRepo.getLessThanOneRupeeOpenInvoices(createdOnBuffer)
        if(bkInvoices.isNotEmpty()){
            bkInvoices.forEach {
                it.paidAmount = it.amount
                it.status = InvoiceStatus.PAID
                it.updatedOn = now()
                it.updatedBy = "SYSTEM"
                invoiceSettlementUpdateHandler.createSettlementConsumer(it.id, CreationType.INVOICE)
            }
            bkInvoiceRepo.saveAll(bkInvoices)
        }
    }

    fun updateInvoiceAmtForBounceCheque(invoices: List<BkInvoice>, invoiceAmtMap: Map<Long, InvoiceSettlement>) {
        invoices.forEach {

            if (invoiceAmtMap.containsKey(it.id)) {

                var paidAmt = it.paidAmount
                var invoiceSettle = invoiceAmtMap.get(it.id)
                var settlementAmt = invoiceSettle?.amount?.toDouble()!!
                var diff = BigDecimal(paidAmt.minus(settlementAmt)).setScale(7, RoundingMode.HALF_EVEN)

                if ((it.amount == settlementAmt) || diff <= ZERO ) {

                    it.status = InvoiceStatus.PENDING
                    it.paidAmount = 0.0
                    it.settledOn = null
                    it.settlementNumber= null
                    it.settlementId = null
                } else if (it.amount > settlementAmt && (diff > ZERO)) {

                    it.status = InvoiceStatus.PARTIAL_PAID
                    it.paidAmount = diff.toDouble()
                    it.updatedOn = it.updatedOn
                    val invoiceSettleMap = invoiceSettlementRepo.getSettlementsListForInvoice(invoiceSettle.id.settlementId,invoiceSettle.id.invoiceId)
                    it.settlementId = invoiceSettleMap.lastOrNull()?.id
                    it.settlementNumber = invoiceSettleMap.lastOrNull()?.settlementNumber
                    it.settledOn = invoiceSettleMap.lastOrNull()?.createdOn
                }
                invoiceSettlementUpdateHandler.createSettlementConsumer(it.id, CreationType.INVOICE)
            }


        }
        bkInvoiceRepo.saveAll(invoices)
    }

    fun updateInvoices(user: String, settlement: Settlement, list: MutableList<InvoiceSettlement> = mutableListOf()): MutableList<InvoiceSettlement> {
        log.debug("Inside updateInvoices ${settlement.id}")

        val settlementInvoiceList = mutableListOf<InvoiceList>()
        val paidInvoiceIds = mutableListOf<String>()
        val tenants = companyTenantMappingRepo.getAllTenantByTenant(settlement.tenant)

        if (tenants.isEmpty()) throw RequestException("No tenant Found !")

        val changedInvoices = mutableListOf<BkInvoice>()
        val newInvoices = mutableListOf<BkInvoice>()

        newInvoices.addAll(settlement.invoices)
        settlement.invoices.clear()
        settlement.invoices.addAll(newInvoices.filterNotNull())

        val pid = mutableListOf<Long?>()

        settlement.invoices.forEach { settledInvoice ->

            val bki = bkInvoiceRepo.getByInvoiceId(settledInvoice.invoiceId!!, settledInvoice.invoiceNum ?: "", tenants)
            if (bki == null) {
                throw RequestException("invoice id: ${settledInvoice.invoiceId} not found ")
            }

            if (bki.status == InvoiceStatus.PAID || bki.status == InvoiceStatus.DELETED)
                throw RequestException("Trying to change the invoice in a terminal state - ${bki.status}")
            bki.updatedOn = now()


            if (bki.amount < settledInvoice.paidAmount) {
                throw RequestException("Paid amount is greater then invoice amount")
            }
            val amountPaid = settledInvoice.paidAmount - bki.paidAmount

            if (amountPaid < 0) {
                val remainingAmount = bki.amount - bki.paidAmount
                throw RequestException("Remaining amount against invoice number ${bki.invoiceNum} is $remainingAmount. Cannot settle $amountPaid against this transaction.")
            }

            val remainingAmt = bki.amount - bki.paidAmount
            bki.paidAmount = settledInvoice.paidAmount

            // allow lower round off only if the invoice amount is having decimals
            var allowedRoundOffDiff = 0.01
//                skipping the logic to allow within 1 rupee round-off for all payment modes
            if ((settledInvoice.amount - settledInvoice.amount.toInt() != 0.00) && (settledInvoice.amount.toInt() + 1 >= settledInvoice.paidAmount && settledInvoice.paidAmount >= settledInvoice.amount.toInt())) {
                allowedRoundOffDiff = 0.99
            }

            if ((settledInvoice.amount - settledInvoice.paidAmount) > allowedRoundOffDiff) {
                bki.status = InvoiceStatus.PARTIAL_PAID
            } else {
                bki.status = InvoiceStatus.PAID
                pid.add(bki.partnerId)
                paidInvoiceIds.add(bki.invoiceId!!)
            }
            bki.settledOn = LocalDateTime.now()
            bki.settlementId = settlement.id
            bki.settlementNumber = settlement.settlementNumber
            changedInvoices.add(bki)
            list.add(
                InvoiceSettlement(
                    InvoiceSettlementId(bki.id, settlement.id),
                    if (remainingAmt >= amountPaid) BigDecimal(amountPaid) else BigDecimal(remainingAmt),
                    BigDecimal(amountPaid),
                    bki.status
                )
            )
            settlementInvoiceList.add(InvoiceList(bki.invoiceNum ?: "", bki.status, BigDecimal(amountPaid)))
        }

        invoiceSettlementRepo.saveAll(list)
        settlementInvoiceListGateway.sendInvoiceListProducerEvent(SettlementInvoiceListDto(settlementInvoiceList))

        try {
            settlement.invoices.clear()
            settlement.invoices.addAll(bkInvoiceRepo.saveAll(changedInvoices))
            changedInvoices.forEach {
                // Push message in a queue
                // applicationEventPublisher.publishEvent(InvoiceSettlementEventsource(this, bki.id))
                invoiceSettlementUpdateHandler.createSettlementConsumer(it.id, CreationType.INVOICE)
            }
        } catch (e: Exception) {
            log.error("Error while saving the invoices: ${e.message}")
            throw e
        }
        if (pid.isNotEmpty()) {
            blockedVendorSettlementPusher.blockedVendorSettlementSinkProducer(pid)
        }

        try {
            slipService.statusChangeSlip(paidInvoiceIds, SlipStatus.CLOSED, "Invoice closed by settlement")
        } catch (requestException: RequestException) {
            log.error("Failed to close slips due to request error: ${requestException.message}")
        } catch (e: Exception) {
            log.error("Failed to close slips due to an unexpected error: ${e.message}", e)
        }
        return list
    }

    fun findInvoicesByNumberAndTenant(invoiceNumberTenantList: List<Pair<String, String>>): List<BkInvoice> {
        return bkInvoiceRepo.findAll(InvoiceMultiGetSpecification(invoiceNumberTenantList))
    }

    fun updateInvoiceForSettlement(
        bki: BkInvoice,
        invoiceSettlement: DraftReceiptEntityMapping,
        settlement: Settlement
    ): Pair<BkInvoice, InvoiceSettlement> {
        val invoice = settlement.invoices.find { it.id == bki.id }!!
        if (bki.status == InvoiceStatus.PAID || bki.status == InvoiceStatus.DELETED)
            throw RequestException("Trying to change the invoice in a terminal state - ${bki.status}")

        // bki holds current paid. invoice holds the updated paid amount after settlement
        val amountBeingPaid = invoiceSettlement.entityTxAmount
        val outstandingAmount = bki.amount - bki.paidAmount
        bki.paidAmount = invoice.paidAmount

        val roundOffTolerance = calculateAllowableRoundOffDifference(bki)

        if (bki.amount - bki.paidAmount > roundOffTolerance) {
            bki.status = InvoiceStatus.PARTIAL_PAID
        } else {
            bki.status = InvoiceStatus.PAID
        }

        bki.settledOn = now()
        bki.settlementId = settlement.id
        bki.settlementNumber = settlement.settlementNumber
        bkInvoiceRepo.save(bki)

        var invoiceSettlement = InvoiceSettlement(
            InvoiceSettlementId(bki.id, settlement.id),
            BigDecimal(max(amountBeingPaid, outstandingAmount)),
            BigDecimal(amountBeingPaid),
            bki.status
        )
        invoiceSettlement = invoiceSettlementRepo.save(invoiceSettlement)
        return  bki to invoiceSettlement
    }
    /**
     * Calculates round-off diff allowed for invoice payment.
     *
     * @return The maximum allowable difference between invoice amount and paid amount
     */
    private fun calculateAllowableRoundOffDifference(invoice: BkInvoice): Double {
        // Default round-off tolerance is 0.01 (1 paisa)
        val defaultRoundOffTolerance = 0.01

        // Check if invoice amount has decimals and if payment is within 1 rupee round-off
        val hasDecimals = invoice.amount - invoice.amount.toInt() != 0.00
        val isWithinOneUnitRoundOff = invoice.amount.toInt() + 1 >= invoice.paidAmount &&
            invoice.paidAmount >= invoice.amount.toInt()

        // Allow higher tolerance (0.99) only if invoice has decimals and payment is within 1 unit round-off
        return if (hasDecimals && isWithinOneUnitRoundOff) {
            0.99
        } else {
            defaultRoundOffTolerance
        }
    }

    fun findCustomerInvoiceByInvoiceNumAndTenant(invoiceNumber: List<String>, tenant: String): List<BkInvoice> {
        return bkInvoiceRepo.findByInvoiceNumInAndTenant(invoiceNumber, tenant)
    }

    fun findAllById(ids: List<Long>): List<BkInvoice> {
        return bkInvoiceRepo.findAllById(ids)
    }

}
