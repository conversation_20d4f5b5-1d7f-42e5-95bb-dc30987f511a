package com.pharmeasy.service

import com.pharmeasy.data.DraftReceipt
import com.pharmeasy.dto.ReceiptUpdateDto
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.service.abstracts.PaymentProcessor
import com.pharmeasy.util.DateUtils
import org.springframework.stereotype.Service
import java.time.LocalDate

@Service
class NeftPaymentProcessor(
    private val draftReceiptService: DraftReceiptService
) : PaymentProcessor(draftReceiptService) {
    override fun validatePayment(payment: PaymentInfo, partnerInfo: PartnerInfo): String? {
        super.validatePayment(payment, partnerInfo)?.let { return it }
        val remark = validate(
            payment.bankDetails?.neftId,
            DateUtils.getDateFromEpochStamp(payment.transactionDate),
            partnerInfo.partnerDetailId,
            partnerInfo.tenant
        )
        return remark
    }

    override fun validatePaymentForApproval(draftReceipt: DraftReceipt) {
        val remark = validate(
            draftReceipt.txReferenceNumber,
            draftReceipt.transactionDate,
            draftReceipt.partnerDetailId,
            draftReceipt.tenant
        )

        if(remark != null) throw RequestException(remark)
    }

    override fun validatePaymentUpdate(receiptUpdateDto: ReceiptUpdateDto) {
        with(receiptUpdateDto) {
            require(!bankName.isNullOrBlank()) { "Bank name is required for NEFT payments" }
            require(!txReferenceNumber.isNullOrBlank()) { "Transaction reference number is required for NEFT payments" }
            require(transactionDate != null) { "Transaction date is required for NEFT payments" }
            require(transactionDate!!.isBefore(LocalDate.now().plusDays(1))) {
                "Transaction date must not be in the future"
            }
        }
        super.validatePaymentUpdate(receiptUpdateDto)
    }


    private fun validate(neftId: String?, neftDate: LocalDate?, partnerDetailId: Long, tenant: String): String? {
        require(!neftId.isNullOrBlank()) { "NEFT ID is required for NEFT payments" }
        require(neftDate != null) { "NEFT date is required for NEFT payments" }

        val duplicateNeftPayments =
            draftReceiptService.findDuplicateNeftPayments(
                neftId,
                partnerDetailId,
                tenant,
                neftDate
            )
        return if (duplicateNeftPayments.isNotEmpty()) {
            "NEFT ID $neftId already exists for the the partner in receipt: ${duplicateNeftPayments[0].receiptNumber}"
        } else {
            null
        }
    }
}
