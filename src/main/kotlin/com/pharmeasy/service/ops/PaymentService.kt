package com.pharmeasy.service.ops

import com.fasterxml.jackson.databind.ObjectMapper
import com.pharmeasy.data.BkInvoice
import com.pharmeasy.data.RetailerDebitNote
import com.pharmeasy.data.ops.Disbursement
import com.pharmeasy.data.ops.Payment
import com.pharmeasy.data.ops.PaymentTransaction
import com.pharmeasy.exception.ProxyException
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.InvoiceStatus
import com.pharmeasy.model.PaymentTransactionUpdates
import com.pharmeasy.model.PaymentTransactionUpdatesData
import com.pharmeasy.model.Supplier
import com.pharmeasy.model.TradeCreditDto
import com.pharmeasy.model.ops.BankDetails
import com.pharmeasy.model.ops.CreditDetails
import com.pharmeasy.model.ops.LogisticsPaymentUpdateDTO
import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentBatchRequest
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.model.ops.PaymentInfoDTO
import com.pharmeasy.model.ops.PaymentTransactionDTO
import com.pharmeasy.model.ops.RIODisbursementDTO
import com.pharmeasy.model.ops.RIOPaymentDTO
import com.pharmeasy.model.ops.SettleableInfo
import com.pharmeasy.proxy.CoreServiceProxy
import com.pharmeasy.proxy.RioIntegrationProxy
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.repo.ops.DisbursementRepository
import com.pharmeasy.repo.ops.PaymentRepository
import com.pharmeasy.service.AdvancePaymentService
import com.pharmeasy.service.CompanyService
import com.pharmeasy.service.DigitalReceiptsWritebackService
import com.pharmeasy.service.InvoiceService
import com.pharmeasy.service.ReceiptOrchestratorService
import com.pharmeasy.service.ReceiptService
import com.pharmeasy.service.RetailerDebitNoteService
import com.pharmeasy.service.SettlementService
import com.pharmeasy.service.TradeCreditPaymentService
import com.pharmeasy.stream.OPSLogisticsEventPusher
import com.pharmeasy.type.AdvancePaymentSource
import com.pharmeasy.type.CreationType
import com.pharmeasy.type.InvoiceType
import com.pharmeasy.type.OrderType
import com.pharmeasy.type.PaymentTransactionStatus
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.ops.OPSPaymentMode
import com.pharmeasy.type.ops.PaymentPartnerType
import com.pharmeasy.util.DateUtils
import com.pharmeasy.util.EventPublisherUtil
import org.hibernate.exception.ConstraintViolationException
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.*

@Service
class PaymentService(
    private val receiptOrchestratorService: ReceiptOrchestratorService,
    private val invoiceService: InvoiceService,
    private val supplierProxy: SupplierProxy,
    private val eventPublisherUtil: EventPublisherUtil,
    private val tradeCreditPaymentService: TradeCreditPaymentService,
    private val companyService: CompanyService

) {

    companion object {
        private val log = LoggerFactory.getLogger(PaymentService::class.java)
        private const val PAYMENT_TYPE_CASH = "CASH"
        private val INITIATED_BY = mutableListOf("RIO_DELIVERY", "SALESMAN_DELIVERY_BOY")
        private const val TRADE_CREDIT_PAYMENT_MODE = "Credit"
        private const val TRADE_CREDIT_REPAYMENT_CATEGORY = "CREDIT_REPAYMENT"
    }

    @Autowired
    private lateinit var paymentRepository: PaymentRepository

    @Autowired
    private lateinit var paymentExcessService: PaymentExcessService

    @Autowired
    private lateinit var settlementService: SettlementService

    @Autowired
    private lateinit var rioIntegrationProxy: RioIntegrationProxy

    @Autowired
    private lateinit var opsLogisticsEventPusher: OPSLogisticsEventPusher

    @Autowired
    private lateinit var disbursementRepository: DisbursementRepository

    @Autowired
    private lateinit var rioDraftInvoiceService: DigitalReceiptsWritebackService

    @Autowired
    private lateinit var advancePaymentService: AdvancePaymentService

    @Autowired
    private lateinit var coreServiceProxy: CoreServiceProxy

    @Autowired
    private lateinit var receiptService: ReceiptService

    @Autowired
    private lateinit var paymentTransactionService: PaymentTransactionService

    @Autowired
    private lateinit var retailerDebitNoteService: RetailerDebitNoteService


    @Transactional
    fun processRioPaymentV2(rioPaymentDTOList: List<RIOPaymentDTO>) {
        val referenceTxn = rioPaymentDTOList.firstOrNull() ?: return

        validateRequest(rioPaymentDTOList)

        val supplier = supplierProxy.supplier(null, referenceTxn.partnerDetailId).firstOrNull()
            ?: throw RequestException("Partner not found")
        val tenant =
            getTenantByDistributorId(
                referenceTxn.distributorId,
                referenceTxn.partnerDetailId!!
            )

        if (tenant.isNullOrEmpty()) {
            throw RequestException(
                "Tenant not found. distributorId: ${referenceTxn.distributorId!!}" +
                    " partnerDetailId: ${referenceTxn.retailerFrontEndPartyCode!!.toLong()}"
            )
        }

        val (tradeCreditRepayments, otherTransactions) = rioPaymentDTOList.partition { it.category?.uppercase() == TRADE_CREDIT_REPAYMENT_CATEGORY }

        handleTradeCreditRepayments(tradeCreditRepayments, tenant)
        if(otherTransactions.isNotEmpty()){
            val transactionsByTxnId = otherTransactions.groupBy { it.retailerTxnId!! }
            val paymentRequest = constructPaymentRequest(transactionsByTxnId, supplier, referenceTxn, tenant)

            receiptOrchestratorService.processPayment(paymentRequest)
        }

        eventPublisherUtil.sendPaymentTransactionUpdates(
            PaymentTransactionUpdatesData(rioPaymentDTOList.map {
                PaymentTransactionUpdates(
                    it.retailerTxnId!!,
                    PaymentTransactionStatus.PROCESSED
                )
            })
        )
    }

    private fun validateRequest(rioPaymentDTOList: List<RIOPaymentDTO>) {
        rioPaymentDTOList.forEach { payment ->
            requireNotNull(payment.retailerTxnId) { "Transaction Id is required" }
            requireNotNull(payment.partnerDetailId) { "Partner Detail Id is required" }
            requireNotNull(payment.distributorId) { "Distributor ID is required" }
            requireNotNull(payment.retailerTxnDate) { "Retailer transaction date is required" }
            requireNotNull(payment.retailerTotalTxnAmount) { "Retailer transaction amount is required" }
            require(payment.retailerTotalTxnAmount!! >= (payment.retailerTxnAmount ?: 0.0)) {
                "Retailer transaction amount must be less than or equal to the total transaction amount"
            }
            if (!payment.invoiceNumber.isNullOrBlank()) {
                requireNotNull(payment.retailerTxnAmount) { "Transaction amount is required for invoice/DN" }
                require(payment.retailerTxnAmount!! > 0.0) { "Transaction amount must be positive" }
                requireNotNull(payment.type) { "Type is required for invoice/DN" }
            }
        }
        rioPaymentDTOList.groupBy { it.retailerTxnId }.forEach { (_, payments) ->
            val computedTotal = payments.sumOf {
                // Advance amount is used for advance payments, else use retailerTxnAmount
                val amt = if(it.category == "ADVANCE_PAYMENT") it.advanceAmount else it.retailerTxnAmount
                return@sumOf amt ?: 0.0
            }
            require(computedTotal == payments.firstOrNull()?.retailerTotalTxnAmount!!) {
                "Total transaction amount must be equal to the sum of retailerTxnAmount / advanceAmount"
            }
        }
    }

    private fun handleTradeCreditRepayments(
        tradeCreditRepayments: List<RIOPaymentDTO>,
        tenant: String
    ) {
        tradeCreditRepayments.forEach {
            handleTradeCreditRepayment(it, tenant)
        }
    }

    private fun constructPaymentRequest(
        transactions: Map<String, List<RIOPaymentDTO>>,
        supplier: Supplier,
        referenceTxn: RIOPaymentDTO,
        tenant: String
    ): PaymentBatchRequest {
        val paymentInfoList = transactions.map { (_, value) ->
            val rioPaymentInfo = value.first()

            val settleables = value.mapNotNull {
                if (!it.invoiceNumber.isNullOrBlank())
                    SettleableInfo(
                        it.invoiceNumber!!,
                        it.type!!,
                        it.retailerTxnAmount!!,
                        it.category
                    )
                else null
            }
            val paymentInfo =
                PaymentInfo(
                    rioPaymentInfo.retailerTxnId!!,
                    rioPaymentInfo.retailerTxnDate!!,
                    rioPaymentInfo.retailerTotalTxnAmount!!,
                    rioPaymentInfo.paymentType,
                    rioPaymentInfo.paymentMode,
                    rioPaymentInfo.initiatedBy,
                    null,
                    BankDetails(
                        rioPaymentInfo.bankName,
                        rioPaymentInfo.chequeNo,
                        rioPaymentInfo.chequeDate?.let { DateUtils.getDateFromEpochStamp(it) },
                        rioPaymentInfo.neftId,
                        rioPaymentInfo.isBankDeposit,
                        rioPaymentInfo.bankDepositSlipNo
                    ),
                    CreditDetails(
                        rioPaymentInfo.creditTransactionId,
                        rioPaymentInfo.creditDueDate,
                        rioPaymentInfo.creditPartner
                    ),
                    settleables,
                    rioPaymentInfo.salesmanId?.toLong(),
                    rioPaymentInfo.salesmanName
                )
            return@map paymentInfo
        }
        val paymentBatchRequest = PaymentBatchRequest(
            partnerInfo = PartnerInfo(
                supplier.partnerId!!,
                referenceTxn.partnerDetailId!!,
                supplier.partnerName!!,
                tenant,
                referenceTxn.distributorId!!
            ),
            paymentInfoList
        )
        return paymentBatchRequest
    }

    @Deprecated("Use processRioPaymentsV2")
    @Transactional
    fun processRioPaymentEvents(rioPaymentDTOList: List<RIOPaymentDTO>){
        var rioPay = false
        rioPaymentDTOList.forEach {
            log.info("received rio pay event: ${ObjectMapper().writeValueAsString(it)}")
            try {
                if (("Cash".equals(it.retailerTxnType, ignoreCase = true) && (("RIO_DELIVERY".equals(it.initiatedBy, ignoreCase = true) || ("SALESMAN_DELIVERY_BOY".equals(it.initiatedBy, ignoreCase = true))) && it.isBankDeposit != true) && "DIGITAL_RECEIPT".equals(it.paymentMode, ignoreCase = true))
                    || ("DIGITAL_PAYMENT".equals(it.paymentMode, ignoreCase = true) || "WALLET".equals(it.paymentMode, ignoreCase = true) || "CREDIT".equals(it.paymentMode, ignoreCase = true))) {
                    processRioPaymentEvent(it)
                    rioPay = true
                }
                else {
                    rioDraftInvoiceService.createDraftReceipt(it, "SYSTEM")//draft receipt creation
                }
            } catch (e: Exception) {
                log.error("Error processing rio payment for ${ObjectMapper().writeValueAsString(it)}", e)
                throw RequestException("Error processing rio payment for ${ObjectMapper().writeValueAsString(it)}, $e")
            }
        }
        if(rioPay){
            createRioPayAdvanceAmountReceipt(rioPaymentDTOList)
        }
        tradeCreditPaymentService.createTradeCreditCustomerLedger(rioPaymentDTOList)
        eventPublisherUtil.sendPaymentTransactionUpdates(
            PaymentTransactionUpdatesData(rioPaymentDTOList.map {
                PaymentTransactionUpdates(
                    it.retailerTxnId!!,
                    PaymentTransactionStatus.PROCESSED
                )
            })
        )
    }

    @Transactional
    fun processRioPaymentEvent(rioPaymentInfo: RIOPaymentDTO){
        if(rioPaymentInfo.invoiceNumber == null && rioPaymentInfo.advanceAmount != null && rioPaymentInfo.advanceAmount!! > 0){
            //handleAdvancePayment(rioPaymentInfo.advanceAmount!!.toBigDecimal(), rioPaymentInfo.retailerTxnId!!, rioPaymentInfo.retailerFrontEndPartyCode!!.toLong(), rioPaymentInfo.distributorId, null)
            log.info("SKIPPING ADVANCE PAYMENT FROM BEING PROCESSED")
        }else{
            val tenant = getTenantByDistributorId(rioPaymentInfo.distributorId, rioPaymentInfo.retailerFrontEndPartyCode!!.toLong())
            if (tenant.isNullOrEmpty()){
                throw RequestException("Tenant not found. distributorId: ${rioPaymentInfo.distributorId!!.toLong()} partnerDetailId: ${rioPaymentInfo.retailerFrontEndPartyCode!!.toLong()}")
            }

            if(rioPaymentInfo.category.equals(TRADE_CREDIT_REPAYMENT_CATEGORY, ignoreCase = true)){
                handleTradeCreditRepayment(rioPaymentInfo, tenant)
                return
            }
            when(rioPaymentInfo.type){
                CreationType.DEBIT_NOTE -> processRioPaymentEventForDebitNote(rioPaymentInfo, tenant)//DN Autosettlement
                else -> processRioPaymentEventForInvoice(rioPaymentInfo, tenant) //Invoice autosettlement
            }
        }
    }

    @Transactional
    fun processRioPaymentEventForInvoice(rioPaymentInfo: RIOPaymentDTO, tenant: String){
            val bkInvoice: BkInvoice?
            val invoiceCount = invoiceService.getCountByInvoiceNumber(rioPaymentInfo.invoiceNumber!!, tenant)
            if(invoiceCount!! > 1L){
                bkInvoice = invoiceService.findCustomerInvoiceByInvoiceNumAndParentTenant(rioPaymentInfo.invoiceNumber!!, tenant)
                if(bkInvoice==null){
                    log.error("Duplicate Invoice number ${rioPaymentInfo.invoiceNumber} is present")
                    throw RequestException("Duplicate invoice found without parent tenant mapping")
                }
            }else {
                bkInvoice = invoiceService.findCustomerInvoiceByInvoiceNumAndTenant(rioPaymentInfo.invoiceNumber!!, tenant)
            }
            if(bkInvoice == null) {
                log.error("Invoice number ${rioPaymentInfo.invoiceNumber} not found")
                throw RequestException("Bk Invoice not found for ${rioPaymentInfo.invoiceNumber}")
            }
            val isTradeCredit = rioPaymentInfo.creditPartner == PaymentPartnerType.TRADE_CREDIT &&
                    rioPaymentInfo.paymentMode.equals(TRADE_CREDIT_PAYMENT_MODE, ignoreCase = true)

            var opsPayMode =  OPSPaymentMode.RIO_PAY
            if(PAYMENT_TYPE_CASH.equals(rioPaymentInfo.retailerTxnType, ignoreCase = true) && INITIATED_BY.contains((rioPaymentInfo.initiatedBy?:"").uppercase())){
                opsPayMode = OPSPaymentMode.CASH
            }else if(isTradeCredit){
                opsPayMode = OPSPaymentMode.TRADE_CREDIT
            }

            val rioOrderId = bkInvoice.invoiceId!!
            val rioPayment = getOrCreatePayment(rioOrderId, bkInvoice, null)
            val lockedPayment = paymentRepository.getLockedPaymentById(rioPayment.id!!)
            if(bkInvoice.status in listOf(InvoiceStatus.PENDING, InvoiceStatus.PARTIAL_PAID)) {
                addPaymentTransaction(
                    rioPaymentInfo.retailerTxnAmount!!.toBigDecimal(),
                    opsPayMode,
                    lockedPayment,
                    rioPaymentInfo.retailerTxnId!!,
                    bkInvoice,
                    null
                )
            } else if(bkInvoice.status == InvoiceStatus.PAID) {
                addPaymentForClosedTransactions(
                    rioPaymentInfo.retailerTxnAmount!!.toBigDecimal(),
                    opsPayMode,
                    lockedPayment,
                    rioPaymentInfo.retailerTxnId!!
                )
            }
            if(isTradeCredit){
                val tradeCreditDto= TradeCreditDto(
                    tenant = bkInvoice.tenant!!,
                    partnerId = bkInvoice.partnerId!!,
                    partnerName = bkInvoice.supplierName!!,
                    transactionId = rioPaymentInfo.retailerTxnId!!,
                    partnerDetailId = bkInvoice.partnerDetailId!!,
                    creditAmount = 0.00,
                    debitAmount = rioPaymentInfo.retailerTxnAmount,
                    userId = rioPaymentInfo.initiatedBy,
                    dueDate = DateUtils.getDateTimeFromEpochStamp(rioPaymentInfo.creditDueDate)!!,
                    repaymentReferenceNumber = null
                )
                tradeCreditPaymentService.saveTradeCreditPayment(tradeCreditDto)
            }

    }

    @Transactional
    fun processRioPaymentEventForDebitNote(rioPaymentInfo: RIOPaymentDTO, tenant: String){
        val retailerDN = retailerDebitNoteService.getRetailerDebitNoteByDebitNoteNumber(rioPaymentInfo.invoiceNumber!!)

        if(retailerDN == null) {
            log.error("Debit note not found for ${rioPaymentInfo.invoiceNumber}")
            return
        }
        var opsPayMode =  OPSPaymentMode.RIO_PAY
        if(PAYMENT_TYPE_CASH.equals(rioPaymentInfo.retailerTxnType, ignoreCase = true) && INITIATED_BY.contains((rioPaymentInfo.initiatedBy?:"").uppercase())){
            opsPayMode = OPSPaymentMode.CASH
        }
        val rioOrderId = retailerDN.documentNumber

        val payment = getOrCreatePayment(rioOrderId, null, retailerDN)

        val lockedPayment = paymentRepository.getLockedPaymentById(payment.id!!)

        if(retailerDN.status in listOf(InvoiceStatus.PENDING, InvoiceStatus.PARTIAL_PAID)) {
            addPaymentTransaction(
                rioPaymentInfo.retailerTxnAmount!!.toBigDecimal(),
                opsPayMode,
                lockedPayment,
                rioPaymentInfo.retailerTxnId!!,
                null,
                retailerDN
            )
        } else if(retailerDN.status == InvoiceStatus.PAID) {
            addPaymentForClosedTransactions(
                rioPaymentInfo.retailerTxnAmount!!.toBigDecimal(),
                opsPayMode,
                lockedPayment,
                rioPaymentInfo.retailerTxnId!!
            )
        }
    }

    fun getOrCreatePayment(referenceId: String,bkInvoice: BkInvoice?, retailerDebitNote: RetailerDebitNote?): Payment {
        val existingPayment = paymentRepository.getPaymentForReferenceId(referenceId)
        if(existingPayment != null){
            return existingPayment
        }

        val payableAmount = bkInvoice?.amount?: retailerDebitNote?.amount
        val paidAmount = bkInvoice?.paidAmount?: retailerDebitNote?.amountReceived
        val newPayment = Payment(
            referenceId = referenceId,
            bkInvoiceId = bkInvoice?.id?: retailerDebitNote?.id,
            invoiceNumber = bkInvoice?.invoiceNum ?: retailerDebitNote?.documentNumber ?: "",
            invoicedAmount = payableAmount!!.toBigDecimal(),
            paidAmount = paidAmount!!.toBigDecimal(),
            isDisbursed = false,
            isPaid = (payableAmount  <= paidAmount),
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
        paymentRepository.save(newPayment)
        return newPayment
    }

    fun addPaymentForClosedTransactions(transactionAmount: BigDecimal,paymentMode: OPSPaymentMode, payment: Payment, paymentReferenceNumber: String) {
        paymentTransactionService.savePaymentTransaction(BigDecimal.ZERO, paymentMode, payment, paymentReferenceNumber, transactionAmount)
        payment.isPaid = true
        paymentRepository.save(payment)
    }

    @Transactional
    fun addPaymentTransaction(transactionAmount: BigDecimal, paymentMode: OPSPaymentMode, payment: Payment, paymentReferenceNumber: String, bkInvoice: BkInvoice?, retailerDN: RetailerDebitNote?): PaymentTransaction {
        if(bkInvoice != null && retailerDN != null){
            throw RequestException("Invalid Transaction. Both BkInvoice and RetailerDebitNote found.")
        }
        var remainingAmount = 0.0
        var settledAmount: BigDecimal = BigDecimal.ZERO
        if(bkInvoice != null) {
            remainingAmount = bkInvoice.amount - bkInvoice.paidAmount
            settledAmount = transactionAmount
            if (transactionAmount > BigDecimal(remainingAmount)) {
                settledAmount = BigDecimal(remainingAmount)
            }
            payment.paidAmount += settledAmount
            payment.isPaid = payment.invoicedAmount <= payment.paidAmount
        }else if(retailerDN!=null){
            remainingAmount = retailerDN.amount - retailerDN.amountReceived
            settledAmount = transactionAmount
            if(transactionAmount > BigDecimal(remainingAmount)){
                settledAmount = BigDecimal(remainingAmount)
            }
            payment.paidAmount += settledAmount
            payment.isPaid = payment.invoicedAmount <= payment.paidAmount
        }

        val paymentTransaction = paymentTransactionService.savePaymentTransaction(settledAmount, paymentMode, payment, paymentReferenceNumber, transactionAmount)

        paymentRepository.save(payment)

        val ds: String? = null

        val tenant = bkInvoice?.tenant ?: retailerDN?.tenant ?:throw RequestException("Invalid Transaction. No tenant found.")

        if(bkInvoice != null){
            settlementService.createSettlementForInvoicePayment(
                bkInvoice,
                settledAmount,
                paymentReferenceNumber,
                paymentMode.paymentType,
                ds,
                tenant,
                AdvancePaymentSource.RIO_PAY
            )
            var retailer = ""
            try {
                val retailerData = supplierProxy.retailerInfo(bkInvoice.partnerDetailId!!)
                if (retailerData.isNotEmpty()) {
                    retailer = retailerData[0]?.systemOwnership!!
                }
            } catch (e: ProxyException) {
                log.error("Error in one roof flag check", e)
            }
            if (retailer == "RIO" && (bkInvoice.client == InvoiceType.RIO || bkInvoice.client == InvoiceType.EASY_SOL)) {
                return paymentTransaction
            } else {
                //todo() : check if this is required
                opsLogisticsEventPusher.logisticsPayableAmountUpdateProducer(
                    LogisticsPaymentUpdateDTO(
                        rioOrderId = bkInvoice.invoiceId!!,
                        payableAmount = BigDecimal(remainingAmount) - settledAmount,
                        tenant = tenant
                    )
                )
            }
        }else if(retailerDN!=null){
            settlementService.createSettlementForDebitNotePayment(retailerDN, settledAmount, paymentReferenceNumber, paymentMode.paymentType,ds, tenant, AdvancePaymentSource.RIO_PAY)
        }
        return paymentTransaction
    }

    fun processUPIQRCodePaymentEvent(bkInvoice: BkInvoice, amount: BigDecimal, transactionNumber: String) {
        val rioPayment = getOrCreatePayment(bkInvoice.invoiceId!!, bkInvoice, null)
        val lockedPayment = paymentRepository.getLockedPaymentById(rioPayment.id!!)
        addPaymentTransaction(amount, OPSPaymentMode.UPI, lockedPayment, transactionNumber, bkInvoice, null)
    }

    @Transactional
    fun getPaymentsForReferenceId(rioOrderId: String, tenant: String, orderType: OrderType, sync: Boolean? = false): PaymentInfoDTO {
        log.info("inside getPaymentsForReferenceId $sync")
        val paymentInfoDTO = PaymentInfoDTO(
            externalOrderId = rioOrderId,
            amount = null,
            paidAmount = null,
            remainingAmount = null
        )
        val rioPayment = paymentRepository.getPaymentForReferenceIdV2(rioOrderId)
        if(sync == false && rioPayment == null){
            return paymentInfoDTO
        }
        if(sync == true){
            syncLivePayment(rioOrderId, orderType, tenant)
        }

        val payment = paymentRepository.getPaymentForReferenceIdV2(rioOrderId) ?: return paymentInfoDTO
        val paymentTransactions = paymentTransactionService.getTransactionsForPayment(payment)
        paymentInfoDTO.amount = payment.invoicedAmount
        paymentInfoDTO.paidAmount = payment.paidAmount
        paymentInfoDTO.remainingAmount = payment.invoicedAmount - payment.paidAmount
        paymentInfoDTO.paymentTransactions = mutableListOf()
        paymentTransactions.forEach {
            paymentInfoDTO.paymentTransactions!!.add(
                PaymentTransactionDTO(
                    paymentReferenceId = it.paymentReferenceId,
                    amount = it.amount,
                    paymentMode = it.paymentMode
                )
            )
        }
        return paymentInfoDTO
    }

    fun syncLivePayment(rioOrderId: String, orderType: OrderType, tenant: String) {
        val queryTenant = if(orderType == OrderType.B2B) {tenant} else {companyService.findTenants(tenant,true)[0]!!}
        val bkInvoice = invoiceService.getRioInvoice(rioOrderId, queryTenant)
        val paymentRefreshData = rioIntegrationProxy.getPaymentDetails(partnerDetailId = bkInvoice.partnerDetailId!!, invoiceId = bkInvoice.invoiceNum!!.replace('/', '-'))
        if(paymentRefreshData.isEmpty()){
            return
        }
        log.debug("paymentRefreshData is not null")
        val payment = getOrCreatePayment(rioOrderId, bkInvoice, null)
        val paymentTransactionsMap = paymentTransactionService.getTransactionsForPayment(payment).associateBy { it.paymentReferenceId }
        paymentRefreshData.forEach{
            if(paymentTransactionsMap.containsKey(it.retailerTransactionId)){
                return@forEach
            }
            try {
                processRioPaymentEvent(
                    RIOPaymentDTO(
                        invoiceNumber = it.invoiceNumber!!,
                        retailerTxnId = it.retailerTransactionId!!,
                        retailerTxnAmount = it.invoicePaymentTransactionAmount!!
                    )
                )
            } catch (e: ConstraintViolationException) {
                log.warn("Duplicate transaction process, ignoring")
            }catch (e: Exception) {
                log.error("Error handling live payment via sync, ${e.printStackTrace()}")
            }
        }
    }

    @Transactional
    fun processRioDisbursement(rioDisbursement: RIODisbursementDTO){
        log.debug("received event for rio disbursement, processing")
        if(rioDisbursement.invoiceNumber == null){
            return
        }
        log.debug("rio disbursement: ${ObjectMapper().writeValueAsString(rioDisbursement)}")
        val invoice = invoiceService.findCustomerInvoiceByInvoiceNum(rioDisbursement.invoiceNumber!!)
        if(invoice == null){
            log.debug("Invoice not found for number: ${rioDisbursement.invoiceNumber!!}, skipping")
            return
        }
        val payment = paymentRepository.getPaymentForReferenceId(invoice.invoiceId!!)
        if(payment == null) {
            log.debug("Payment Not found for RIO: ${invoice.invoiceId!!}, skipping")
            return
        }
        val disbursement = Disbursement(id=0,
            payment = payment,
            utr = rioDisbursement.disbursementUtr!!,
            amount = rioDisbursement.distributorInvoiceAmount!!.toBigDecimal(),
            createdAt = LocalDateTime.now()
        )
        disbursementRepository.save(disbursement)
        val totalDisbursement = disbursementRepository.getTotalDisbursementForPayment(payment.id!!)
        if(totalDisbursement!! >= payment.invoicedAmount){
            payment.isDisbursed = true
            paymentRepository.save(payment)
        }
    }



    @Transactional
    fun createRioPayAdvanceAmountReceipt(rioPaymentDTOList: List<RIOPaymentDTO>){
        val tenant = getTenantByDistributorId(rioPaymentDTOList[0].distributorId, rioPaymentDTOList[0].retailerFrontEndPartyCode?.toLong()!!)

        if (tenant.isNullOrEmpty()){
            throw RequestException("Tenant not found. distributorId: ${rioPaymentDTOList[0].distributorId!!.toLong()} partnerDetailId: ${rioPaymentDTOList[0].retailerFrontEndPartyCode!!.toLong()}")
        }
        val transactionType = when(rioPaymentDTOList[0].retailerTxnType?.uppercase(Locale.getDefault())){
            PaymentType.CASH.name -> PaymentType.CASH
            else -> PaymentType.RIO_PAY
        }


        val retailerTxnAmountSumByTxnId = rioPaymentDTOList.associateBy(
            keySelector = { it.retailerTxnId },
            valueTransform = { it.retailerTotalTxnAmount }
        )

        val distinctRetailerTxnIds = rioPaymentDTOList
            .mapNotNull { it.retailerTxnId }
            .distinct()

        val paymentTransactions = paymentTransactionService.getPaymentsByPaymentTransactionIds(distinctRetailerTxnIds)

        if(paymentTransactions.isEmpty() && rioPaymentDTOList[0].category == "ADVANCE_PAYMENT"){
            val excessAmount = rioPaymentDTOList[0].retailerTotalTxnAmount?:0.0

            paymentExcessService.handleAdvancePayment(BigDecimal(excessAmount), rioPaymentDTOList[0].retailerTxnId!!, rioPaymentDTOList[0].retailerFrontEndPartyCode?.toLong(), rioPaymentDTOList[0].distributorId, tenant, transactionType)
        }else {
            paymentTransactions.forEach {
                val totalTransactionAmountForTransactionId = retailerTxnAmountSumByTxnId[it.paymentTransactionNumber]
                val paymentTransactionAmount = it.totalTransactionAmount
                val excessAmount = BigDecimal(totalTransactionAmountForTransactionId!!) - paymentTransactionAmount
                val excessPayment = if (excessAmount > BigDecimal.ZERO) {
                    paymentExcessService.handleAdvancePayment(
                        excessAmount,
                        it.paymentTransactionNumber,
                        rioPaymentDTOList[0].retailerFrontEndPartyCode?.toLong(),
                        rioPaymentDTOList[0].distributorId,
                        tenant,
                        transactionType
                    )
                } else {
                    null
                }
                val advancePayment = if (excessPayment != null) {
                    advancePaymentService.getAdvancePaymentByDocumentNumber(excessPayment.advancePaymentId!!)
                } else {
                    null
                }

                val settlement = settlementService.getSettlementByReferenceNumberAndPartnerDetailId(
                    it.paymentTransactionNumber,
                    rioPaymentDTOList[0].retailerFrontEndPartyCode?.toLong()!!,
                    tenant
                )

                if (advancePayment != null && settlement.isNotEmpty()) {
                    receiptService.mapAdvanceIdToReceipt(settlement[0], advancePayment, AdvancePaymentSource.RIO_PAY)
                } else if (advancePayment == null && settlement.isNotEmpty()) {
                    receiptService.addLedgerForReceipt(settlement[0])
                } else {
                    log.info("Advance reference number: ${advancePayment?.documentId} created for reference number ${it.paymentTransactionNumber}")
                }
            }
        }
    }

    fun handleTradeCreditRepayment(rioPaymentInfo: RIOPaymentDTO, tenant: String){
        val supplier = supplierProxy.supplier(null, rioPaymentInfo.retailerFrontEndPartyCode!!.toLong())

        val tradeCreditDto= TradeCreditDto(
            tenant = tenant,
            partnerId = supplier[0]?.partnerId!!,
            partnerName = supplier[0]?.partnerName!!,
            transactionId = rioPaymentInfo.creditTransactionId!!,
            partnerDetailId = rioPaymentInfo.retailerFrontEndPartyCode!!.toLong(),
            creditAmount = rioPaymentInfo.retailerTxnAmount,
            debitAmount = 0.00,
            userId = rioPaymentInfo.initiatedBy,
            dueDate = DateUtils.getDateTimeFromEpochStamp(rioPaymentInfo.creditDueDate)!!,
            repaymentReferenceNumber = rioPaymentInfo.retailerTxnId
        )
        tradeCreditPaymentService.updateTradeCreditPayment(tradeCreditDto)

    }

    fun getTenantByDistributorId(distributorId: Long?, partnerDetailId: Long): String?{
        return when (distributorId) {
            null -> coreServiceProxy.getRetailerByPartnerDetailId(partnerDetailId)?.tenant
            else -> {
                val distributorTenantOrderType = invoiceService.getTenantCodeAndCustomerType(distributorId)
                when(distributorTenantOrderType.fulfilmentType) {
                    OrderType.B2B2B -> {
                        val companyTenantMapping = companyService.getTenantByPDI(distributorId)
                        companyTenantMapping.tenant
                    }
                    OrderType.B2B -> distributorTenantOrderType.tenant
                }
            }
        }
    }
}
