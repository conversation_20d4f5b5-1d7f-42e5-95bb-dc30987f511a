package com.pharmeasy.service

import com.pharmeasy.data.DraftReceiptEntityMapping
import com.pharmeasy.data.Receipt
import com.pharmeasy.data.RetailerDebitNote
import com.pharmeasy.data.Settlement
import com.pharmeasy.dto.SettleableDetails
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.InvoiceStatus
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.service.abstracts.BaseSettleableProcessor
import com.pharmeasy.stream.InvoiceSettlementUpdateHandler
import com.pharmeasy.type.CreationType
import com.pharmeasy.type.PartnerType
import com.pharmeasy.type.SettleableType
import org.springframework.stereotype.Service
import java.time.LocalDate

@Service
class DebitNoteSettleableProcessor(
    private val retailerDebitNoteService: RetailerDebitNoteService,
    private val supplierProxy: SupplierProxy,
    private val invoiceSettlementUpdateHandler: InvoiceSettlementUpdateHandler
) : BaseSettleableProcessor {
    override fun getSettleableDetails(
        number: String,
        tenant: String
    ): SettleableDetails? {
        val debitNote =
            retailerDebitNoteService.getRetailerDebitNoteByDebitNoteNumber(number)
                ?: return null
        return toSettleableDetails(debitNote)
    }

    override fun getSettleableDetails(id: Long): SettleableDetails? {
        return retailerDebitNoteService.getDebitNoteById(id)?.let { debitNote ->
            toSettleableDetails(debitNote)
        }
    }

    private fun toSettleableDetails(debitNote: RetailerDebitNote): SettleableDetails = SettleableDetails(
        debitNote.id!!,
        debitNote.documentNumber,
        debitNote.amount,
        (debitNote.amount - debitNote.amountReceived),
        debitNote.status,
        SettleableType.DEBIT_NOTE
    )

    override fun getSettleableDetails(
        numbers: List<String>,
        tenant: String
    ): List<SettleableDetails> {
        return retailerDebitNoteService.getRetailerDebitNoteByDebitNoteNumbers(numbers)
            .map { toSettleableDetails(it) }
    }

    override fun getSettleableDetails(ids: List<Long>): List<SettleableDetails> {
        return retailerDebitNoteService.getDebitNotesByIds(ids).map { toSettleableDetails(it) }
    }

    override fun constructSettlement(
        receipt: Receipt,
        settleableDetails: SettleableDetails,
        settlementMapping: DraftReceiptEntityMapping
    ): Settlement {
        val partnerInfo = supplierProxy.supplier(supplierId = listOf(receipt.partnerId))[0]!!
        val debitNote =
            retailerDebitNoteService.getRetailerDebitNoteByDebitNoteNumber(settleableDetails.number)
                ?: throw RequestException("Invoice not found for invoice number: ${settleableDetails.number}")
        val exInvoiceOutstanding = (debitNote.amount - debitNote.amountReceived)

        require(
            debitNote.status !in listOf(InvoiceStatus.PAID, InvoiceStatus.WRITE_OFF)
        ) { "Invoice ${debitNote.documentNumber} is already paid" }
        require(debitNote.amountReceived + settlementMapping.entityTxAmount!! <= debitNote.amount) {
            "Receipt amount ${settlementMapping.entityTxAmount} is greater than invoice outstanding $exInvoiceOutstanding"
        }

        debitNote.amountReceived = debitNote.amountReceived.plus(settlementMapping.entityTxAmount!!)
        return Settlement(
            user = receipt.updatedBy,
            supplierId = debitNote.partnerId,
            supplierName = partnerInfo.partnerName,
            amount = exInvoiceOutstanding,
            paidAmount = settlementMapping.entityTxAmount,
            remarks = "Settled via RIO Payment", // FixMe: Update as per source
            settlementNumber = null,
            invoices = mutableListOf(),
            creditNotes = mutableListOf(),
            paymentType = receipt.paymentType!!,
            paymentReference = receipt.paymentTransactionId!!,
            paymentDate = LocalDate.now(),
            partnerId = debitNote.partnerId,
            partnerDetailId = debitNote.partnerDetailId,
            type = PartnerType.CUSTOMER,
            tenant = receipt.tenant!!,
            chequeDate = null,
            bankId = null,
            bankName = null,
            advancePayment = mutableListOf(),
            chargeInvoice = mutableListOf(),
            paymentSource = receipt.source,
            debitNotes = mutableListOf(debitNote.copy())
        )
    }

    override fun updateSettlementMapping(
        settlement: Settlement,
        settleableDetails: SettleableDetails,
        settlementMapping: DraftReceiptEntityMapping
    ) {
        val debitNoteList: MutableList<RetailerDebitNote> = mutableListOf()
        settlement.retailerDebitNotes.forEach {
            val rdn = retailerDebitNoteService.getRetailerDebitNoteByDebitNoteNumber(it.documentNumber)
            if (rdn != null) {
                val (updateDNSettlement, _) = retailerDebitNoteService.updateDNSettlement(
                    rdn,
                    settlement,
                    settlementMapping
                )
                debitNoteList.add(updateDNSettlement)
            }
        }
        settlement.retailerDebitNotes = debitNoteList
        debitNoteList.forEach {
            invoiceSettlementUpdateHandler.createSettlementConsumer(it.id!!, CreationType.DEBIT_NOTE)
        }
    }
}
