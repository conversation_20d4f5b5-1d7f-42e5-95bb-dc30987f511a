package com.pharmeasy.resource

import com.pharmeasy.data.ReceiptDto
import com.pharmeasy.dto.DraftReceiptUpdateResponseDto
import com.pharmeasy.dto.ReceiptListFilter
import com.pharmeasy.dto.ReceiptStatusUpdateDto
import com.pharmeasy.dto.ReceiptUpdateDto
import com.pharmeasy.service.ReceiptOrchestratorService
import org.springframework.data.domain.Slice
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/receipts")
class ReceiptResource(private val receiptOrchestratorService: ReceiptOrchestratorService) {

    @GetMapping
    fun getReceipts(receiptListFilter: ReceiptListFilter): Slice<ReceiptDto> {
        return receiptOrchestratorService.getReceiptsByFilter(receiptListFilter)
    }

    @PutMapping("/draft/{id}/status")
    fun updateDraftStatus(@PathVariable id: Long, @RequestBody receiptStatusUpdateDto: ReceiptStatusUpdateDto) {
        receiptOrchestratorService.updateDraftStatus(id, receiptStatusUpdateDto)
    }

    @PutMapping("/draft/{id}")
    fun updateDraft(@PathVariable id: Long, @RequestBody receiptUpdateDto: ReceiptUpdateDto): DraftReceiptUpdateResponseDto {
        return receiptOrchestratorService.updateDraft(id, receiptUpdateDto)
    }
}
