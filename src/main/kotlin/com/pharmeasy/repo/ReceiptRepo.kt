package com.pharmeasy.repo

import com.pharmeasy.data.Receipt
import com.pharmeasy.data.ReceiptSettlement
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.ReceiptStatus
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.history.RevisionRepository
import org.springframework.data.repository.query.Param
import java.time.LocalDate

interface ReceiptRepo : JpaRepository<Receipt, Long> {
    @Query("select id from Receipt where paymentTransactionId = :paymentTransactionNumber")
    fun getByPaymentTransactionNumber(@Param("paymentTransactionNumber") paymentTransactionNumber: String): Long?

    @Query(
        "SELECT r from Receipt r inner join ReceiptSettlement rs on r.id=rs.id.receiptId inner join" +
            " Settlement s on rs.id.settlementId = s.id where s.paymentReference = :paymentReference and" +
            " s.paymentDate = :paymentDate and s.paymentType = :paymentMode and s.partnerDetailId = :partnerDetailId " +
            "and r.status not in ('REVERSED','CANCELLED') and (:bankName = null or s.bankName = :bankName) " +
            "and s.isBounced = false and s.reversed = false"
    )
    fun getReceiptsByPaymentReferenceAndPaymentDateAndPaymentModeAndPartnerDetailIdAndBankName(
        @Param("paymentReference") paymentReference: String?,
        @Param("paymentDate") paymentDate: LocalDate?,
        @Param("paymentMode") paymentMode: PaymentType,
        @Param("partnerDetailId") partnerDetailId: Long?,
        @Param("bankName") bankName: String?
    ): List<Receipt>?

    @Query(
        "Select r from Receipt r inner join ReceiptSettlement rs on r.id = rs.id.receiptId inner join " +
            "Settlement s on rs.id.settlementId = s.id where s.id=:settlementId"
    )
    fun getReceiptBySettlementId(@Param("settlementId") settlementId: Long): List<Receipt>?

    @Query("SELECT r FROM Receipt r where r.advanceId = :advanceId")
    fun getReceiptByAdvanceId(@Param("advanceId") advanceId: Long?): Receipt?

    @Query("SELECT r FROM Receipt r where r.draftReceipt.id = :draftId")
    fun getReceiptByDraftId(draftId: Long): Receipt?

    fun findByDraftReceiptIdIn(draftIds: List<Long>): List<Receipt>
}
