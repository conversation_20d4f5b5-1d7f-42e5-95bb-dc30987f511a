package com.pharmeasy.repo;

import com.pharmeasy.data.ChequeHandle
import com.pharmeasy.model.AggregatedChequeHandleDto
import com.pharmeasy.model.ChequeHandleDto
import com.pharmeasy.type.ChequeHandleType
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

interface ChequeHandlingRepo : JpaRepository<ChequeHandle, Long> {


    @Query(
        " SELECT new com.pharmeasy.model.ChequeHandleDto(chs.id, chs.chequeNum, s.supplierName, s.partnerId," +
                "s.bankName, s.paidAmount, s.chequeDate, chs.updatedOn, s.settlementNumber, chs.status, chs.reason, chs.assignTo, chs.inApprove, " +
                "s.partnerDetailId,chs.charge,chs.chargeAmt,s.type, '', case when chs.status='BOUNCE' then chs.bounceDate else null end, chs.assignTo,s.paidAmount) " +
                "FROM Settlement s, ChequeHandle chs WHERE chs.settlementId = s.id " +
                "AND chs.tenant in :tenants " +
                "AND (:chequeNum = null OR chs.chequeNum = :chequeNum) " +
                "AND (:settlementNum = null OR s.settlementNumber = :settlementNum) " +
                "AND (:status = null OR chs.status = :status) " +
                "AND (:bankName = null OR s.bankName = :bankName) " +
                "AND (:partnerDetailId = null OR s.partnerDetailId = :partnerDetailId) " +
                "AND (s.partnerId IN :partnerIds) " +
                "AND ((:fromDate = null AND :toDate = null) OR (s.chequeDate >= :fromDate AND s.chequeDate <= :toDate))"
    )
    fun getChequeHandlingData(
        @Param("chequeNum") chequeNum: String?,
        @Param("settlementNum") settlementNum: String?,
        @Param("status") status: ChequeHandleType?,
        @Param("bankName") bankName: String?,
        @Param("partnerIds") partnerIds: List<Long>?,
        @Param("fromDate") fromDate: LocalDate?,
        @Param("toDate") toDate: LocalDate?,
        @Param("tenants") tenants: MutableList<String?>,
        @Param("partnerDetailId") partnerDetailId: Long?,
        pageable: Pageable
    ): Page<ChequeHandleDto>


    @Modifying
    @Query(
        "UPDATE ChequeHandle chs SET chs.updatedOn = :updatedOn, chs.status = :status, chs.reason = :reason," +
                "chs.updatedBy = :updatedBy" +
                " WHERE chs.id = :Id"
    )
    fun updateChequeStatus(
        @Param("Id") Id: Long,
        @Param("updatedOn") updatedOn: LocalDateTime?,
        @Param("status") status: ChequeHandleType?,
        @Param("reason") reason: String?,
        @Param("updatedBy") updatedBy: String?
    ): Int


    @Query(" SELECT chs from ChequeHandle chs WHERE chs.id =:id")
    fun get(@Param("id") id: Long): ChequeHandle?


    @Query(
        " SELECT new com.pharmeasy.model.AggregatedChequeHandleDto(SUM(CASE WHEN chs.status = 'RECEIVED' THEN 1 ELSE 0 END), " +
                "SUM(CASE WHEN chs.status = 'RECEIVED' THEN s.paidAmount ELSE 0 END), " +
                "SUM(CASE WHEN chs.status = 'DEPOSITED' THEN 1 ELSE 0 END), " +
                "SUM(CASE WHEN chs.status = 'DEPOSITED' THEN s.paidAmount ELSE 0 END)) " +
                "FROM Settlement s JOIN ChequeHandle chs ON chs.settlementId = s.id " +
                "WHERE chs.tenant in :tenants and (:partnerDetailId = null or s.partnerDetailId = :partnerDetailId)"
    )
    fun getAggregatedChequeData(
        @Param("tenants") tenants: MutableList<String?>,
        @Param("partnerDetailId") partnerDetailId: Long?
    ): AggregatedChequeHandleDto

    @Query(
        " SELECT new com.pharmeasy.model.ChequeHandleDto(chs.id, chs.chequeNum, s.supplierName, s.partnerId," +
                "s.bankName, s.paidAmount, s.chequeDate, chs.updatedOn, s.settlementNumber, chs.status, chs.reason, chs.assignTo, chs.inApprove, " +
                "s.partnerDetailId,chs.charge,chs.chargeAmt,s.type, '', case when chs.status='BOUNCE' then chs.bounceDate else null end, chs.assignTo,s.paidAmount) " +
                "FROM Settlement s, ChequeHandle chs WHERE chs.settlementId = s.id " +
                "AND chs.tenant in :tenants " +
                "AND (:chequeNum = null OR chs.chequeNum = :chequeNum) " +
                "AND (:settlementNum = null OR s.settlementNumber = :settlementNum) " +
                "AND (:status = null OR chs.status = :status) " +
                "AND (:bankName = null OR s.bankName = :bankName) " +
                "AND (:partnerDetailId = null OR s.partnerDetailId = :partnerDetailId) " +
                "AND ((:fromDate = null AND :toDate = null) OR (s.chequeDate >= :fromDate AND s.chequeDate <= :toDate))"
    )
    fun getChequeHandlingDataWithoutIds(
        @Param("chequeNum") chequeNum: String?,
        @Param("settlementNum") settlementNum: String?,
        @Param("status") status: ChequeHandleType?,
        @Param("bankName") bankName: String?,
        @Param("fromDate") fromDate: LocalDate?,
        @Param("toDate") toDate: LocalDate?,
        @Param("tenants") tenants: MutableList<String?>,
        @Param("partnerDetailId") partnerDetailId: Long?,
        pageable: Pageable
    ): Page<ChequeHandleDto>

    @Query(
        "SELECT " +
                "NEW com.pharmeasy.model.ChequeHandleDto(chs.id, chs.chequeNum, " +
                "   COALESCE(r.retailerName, s.supplierName, ap.vendorName), " +
                "   COALESCE(r.partnerId, s.partnerId, ap.partnerId), " +
                "   COALESCE(r.bankName, s.bankName, ap.bankName), " +
                "   COALESCE(r.amount, s.paidAmount, ap.amount), " +
                "   COALESCE(r.transactionDate, s.chequeDate, ap.chequeDate), " +
                "   chs.updatedOn, " +
                "   COALESCE(s.settlementNumber, null), " +
                "   chs.status, " +
                "   chs.reason, " +
                "   chs.assignTo, " +
                "   chs.inApprove, " +
                "   COALESCE(r.partnerDetailId, s.partnerDetailId, ap.partnerDetailId), " +
                "   chs.charge, " +
                "   chs.chargeAmt, " +
                "   COALESCE(s.type, ap.type), " +
                "   COALESCE(r.receiptNumber, ap.documentId), " +
                "   CASE WHEN chs.status in ('BOUNCE','AFTER_CLEAR_BOUNCE') then chs.bounceDate else null end, " +
                "   chs.assignTo, " +
                "   COALESCE(r.amount, s.paidAmount)) " +
                "FROM ChequeHandle chs " +
                "LEFT JOIN Receipt r ON chs.receiptId = r.id " +
                "LEFT JOIN ReceiptSettlement rs ON r.id = rs.id.receiptId " +
                "LEFT JOIN Settlement s ON (chs.settlementId = s.id OR rs.id.settlementId = s.id) " +
                "LEFT JOIN AdvancePayment ap ON (chs.advancePaymentId = ap.id OR r.advanceId = ap.id) " +
                "WHERE chs.tenant IN :tenants " +
                "AND (:chequeNum IS NULL OR chs.chequeNum = :chequeNum) " +
                "AND (:partnerDetailId = null OR COALESCE(r.partnerDetailId, s.partnerDetailId, ap.partnerDetailId) = :partnerDetailId) " +
                "AND (:settlementNumber IS NULL OR s.settlementNumber = :settlementNumber) " +
                "AND (:advancePaymentNumber IS NULL OR ap.documentId = :advancePaymentNumber) " +
                "AND (:status IS NULL OR chs.status = :status) " +
                "AND (:bankName IS NULL OR COALESCE(r.bankName, s.bankName, ap.bankName) = :bankName) " +
                "AND ((:fromDate IS NULL AND :toDate IS NULL) OR (COALESCE(r.transactionDate, s.chequeDate, ap.chequeDate) BETWEEN :fromDate AND :toDate))"
    )
    fun getChequeHandlingDataWithoutIdsV2(
        @Param("chequeNum") chequeNum: String?,
        @Param("settlementNumber") settlementNumber: String?,
        @Param("advancePaymentNumber") advancePaymentNumber: String?,
        @Param("status") status: ChequeHandleType?,
        @Param("bankName") bankName: String?,
        @Param("fromDate") fromDate: LocalDate?,
        @Param("toDate") toDate: LocalDate?,
        @Param("tenants") tenants: MutableList<String?>,
        @Param("partnerDetailId") partnerDetailId: Long?,
        pageable: Pageable
    ): Page<ChequeHandleDto>

    @Query(
        " SELECT new com.pharmeasy.model.ChequeHandleDto(chs.id, chs.chequeNum, s.supplierName, s.partnerId," +
                "s.bankName, s.paidAmount, s.chequeDate, chs.updatedOn, s.settlementNumber, chs.status, chs.reason, chs.assignTo, chs.inApprove, " +
                "s.partnerDetailId,chs.charge,chs.chargeAmt,s.type, '', case when chs.status in ('BOUNCE','AFTER_CLEAR_BOUNCE') then chs.bounceDate else null end, chs.assignTo,s.paidAmount) " +
                "FROM Settlement s, ChequeHandle chs WHERE chs.settlementId = s.id " +
                "AND chs.tenant in :tenants " +
                "AND (:chequeNum = null OR chs.chequeNum = :chequeNum) " +
                "AND (:settlementNum = null OR s.settlementNumber = :settlementNum) " +
                "AND (:status = null OR chs.status = :status) " +
                "AND (:bankName = null OR s.bankName = :bankName) " +
                "AND ((:fromDate = null AND :toDate = null) OR (s.chequeDate >= :fromDate AND s.chequeDate <= :toDate))"
    )
    fun getChequeHandlingDataWithoutIds(
        @Param("chequeNum") chequeNum: String?,
        @Param("settlementNum") settlementNum: String?,
        @Param("status") status: ChequeHandleType?,
        @Param("bankName") bankName: String?,
        @Param("fromDate") fromDate: LocalDate?,
        @Param("toDate") toDate: LocalDate?,
        @Param("tenants") tenants: MutableList<String?>,
        pageable: Pageable
    ): Page<ChequeHandleDto>

    @Query(
        "SELECT COUNT(chs) " +
                "FROM ChequeHandle chs " +
                "LEFT JOIN Receipt r ON chs.receiptId = r.id " +
                "WHERE chs.tenant IN :tenants " +
                "AND chs.status = 'BOUNCE' " +
                "AND r.partnerId = :partnerId"
    )
    fun getPastBounceNumberV2(
        @Param("tenants") tenants: MutableList<String?>,
        @Param("partnerId") partnerId: Long
    ): Long

    @Query(
        "SELECT " +
                "NEW com.pharmeasy.model.ChequeHandleDto(" +
                "   chs.id, chs.chequeNum, " +
                "   r.retailerName, " +
                "   r.partnerId, " +
                "   r.bankName, " +
                "   r.amount, " +
                "   r.transactionDate, " +
                "   chs.updatedOn, " +
                "   null, " +
                "   chs.status, " +
                "   chs.reason, " +
                "   chs.assignTo, " +
                "   chs.inApprove, " +
                "   r.partnerDetailId, " +
                "   chs.charge, " +
                "   chs.chargeAmt, " +
                "   type, " +
                "   null," +
                "   case when chs.status='BOUNCE' then chs.bounceDate else null end, " +
                "   chs.assignTo," +
                "   r.amount) " +
                "FROM ChequeHandle chs " +
                "LEFT JOIN Receipt r ON chs.receiptId = r.id " +
                "WHERE chs.tenant IN :tenants " +
                "AND (:chequeNum IS NULL OR chs.chequeNum = :chequeNum) " +
                "AND (:status IS NULL OR chs.status = :status) " +
                "AND (:bankName IS NULL OR r.bankName = :bankName) " +
                "AND (r.partnerId IN :partnerIds) " +
                "AND (:partnerDetailId = null OR r.partnerDetailId = :partnerDetailId) " +
                "AND ((:fromDate IS NULL AND :toDate IS NULL) OR (r.transactionDate BETWEEN :fromDate AND :toDate))"
    )
    fun getChequeHandlingDataV2(
        @Param("chequeNum") chequeNum: String?,
        @Param("status") status: ChequeHandleType?,
        @Param("bankName") bankName: String?,
        @Param("partnerIds") partnerIds: List<Long>?,
        @Param("fromDate") fromDate: LocalDate?,
        @Param("toDate") toDate: LocalDate?,
        @Param("tenants") tenants: MutableList<String?>,
        @Param("partnerDetailId") partnerDetailId: Long?,
        pageable: Pageable
    ): Page<ChequeHandleDto>

    @Query(
        "SELECT " +
                "NEW com.pharmeasy.model.AggregatedChequeHandleDto(" +
                "   SUM(CASE WHEN chs.status = 'RECEIVED' THEN 1 ELSE 0 END), " +
                "   SUM(CASE WHEN chs.status = 'RECEIVED' THEN COALESCE(r.amount, s.paidAmount, ap.amount) ELSE 0 END), " +
                "   SUM(CASE WHEN chs.status = 'DEPOSITED' THEN 1 ELSE 0 END), " +
                "   SUM(CASE WHEN chs.status = 'DEPOSITED' THEN COALESCE(r.amount, s.paidAmount, ap.amount) ELSE 0 END)" +
                ") " +
                "FROM ChequeHandle chs " +
                "LEFT JOIN Receipt r ON chs.receiptId = r.id " +
                "LEFT JOIN ReceiptSettlement rs ON r.id = rs.id.receiptId " +
                "LEFT JOIN Settlement s ON (chs.settlementId = s.id OR rs.id.settlementId = s.id) " +
                "LEFT JOIN AdvancePayment ap ON (chs.advancePaymentId = ap.id OR r.advanceId = ap.id) " +
                "WHERE chs.tenant IN :tenants and (:partnerDetailId = null or COALESCE(r.partnerDetailId, s.partnerDetailId, ap.partnerDetailId) = :partnerDetailId)"
    )
    fun getAggregatedChequeDataV2(
        @Param("tenants") tenants: MutableList<String?>,
        @Param("partnerDetailId") partnerDetailId: Long?
    ): AggregatedChequeHandleDto

    @Query(
        "SELECT " +
                "NEW com.pharmeasy.model.ChequeHandleDto(chs.id, chs.chequeNum, " +
                "   CASE WHEN chs.settlementId IS NOT NULL THEN s.supplierName ELSE ap.vendorName END, " +
                "   CASE WHEN chs.settlementId IS NOT NULL THEN s.partnerId ELSE ap.partnerId END, " +
                "   CASE WHEN chs.settlementId IS NOT NULL THEN s.bankName ELSE ap.bankName END, " +
                "   CASE WHEN chs.settlementId IS NOT NULL THEN s.paidAmount ELSE ap.amount END, " +
                "   CASE WHEN chs.settlementId IS NOT NULL THEN s.chequeDate ELSE ap.chequeDate END, " +
                "   chs.updatedOn, " +
                "   CASE WHEN chs.settlementId IS NOT NULL THEN s.settlementNumber ELSE null END, " +
                "   chs.status, " +
                "   chs.reason, " +
                "   chs.assignTo, " +
                "   chs.inApprove, " +
                "   CASE WHEN chs.settlementId IS NOT NULL THEN s.partnerDetailId ELSE ap.partnerDetailId END, " +
                "   chs.charge, " +
                "   chs.chargeAmt, " +
                "   CASE WHEN chs.settlementId IS NOT NULL THEN s.type ELSE ap.type END, " +
                "   CASE WHEN chs.advancePaymentId IS NOT NULL THEN ap.documentId ELSE null END, " +
                "   case when chs.status='BOUNCE' then chs.bounceDate else null end, chs.assignTo, s.paidAmount ) " +
                "FROM ChequeHandle chs " +
                "LEFT JOIN Settlement s ON chs.settlementId = s.id " +
                "LEFT JOIN AdvancePayment ap ON chs.advancePaymentId = ap.id " +
                "WHERE chs.id = :Id"
    )
    fun getChequeUpdateDataV2(@Param("Id") Id: Long): ChequeHandleDto


    @Query(" SELECT count(chs) FROM Settlement s JOIN ChequeHandle chs ON chs.settlementId = s.id WHERE chs.tenant in :tenants AND chs.status = 'BOUNCE' AND s.partnerId =:partnerId ")
    fun getPastBounceNumber(@Param("tenants") tenants: MutableList<String?>, @Param("partnerId") partnerId: Long): Long

    @Query(
        "SELECT bkc FROM ChequeHandle bkc " +
                "LEFT JOIN Receipt r ON bkc.receiptId = r.id " +
                "LEFT JOIN ReceiptSettlement rsm ON r.id = rsm.id.receiptId " +
                "LEFT JOIN Settlement s ON (bkc.settlementId = s.id OR rsm.id.settlementId = s.id) " +
                "LEFT JOIN AdvancePayment ap ON (bkc.advancePaymentId = ap.id OR r.advanceId = ap.id) " +
                "WHERE bkc.chequeNum = :chequeNum " +
                "AND COALESCE(r.partnerDetailId, s.partnerDetailId, ap.partnerDetailId) = :partnerDetailId " +
                "AND (r.receiptNumber = :receiptNumber OR ap.documentId = :receiptNumber)"
    )
    fun getChequeForStatusChange(
        @Param("chequeNum") chequeNum: String,
        @Param("partnerDetailId") partnerDetailId: Long,
        @Param("receiptNumber") receiptNumber: String
    ): ChequeHandle?


    @Query(
        "select bkc from ChequeHandle bkc join AdvancePayment ap on ap.id = bkc.advancePaymentId" +
                " where bkc.chequeNum = :chequeNum and ap.partnerDetailId = :partnerDetailId and ap.documentId = :receiptNumber "
    )
    fun getChequeForStatusChangeAdvance(
        @Param("chequeNum") chequeNum: String,
        @Param("partnerDetailId") partnerDetailId: Long,
        @Param("receiptNumber") receiptNumber: String
    ): ChequeHandle?

    @Query(
        """SELECT chs FROM ChequeHandle chs 
                JOIN Receipt r ON chs.receiptId = r.id
                WHERE chs.chequeNum = :chequeNum
                AND r.partnerDetailId = :pdi
                AND r.transactionDate = :chequeDate
                AND r.tenant = :tenant
                AND chs.status NOT IN ('CANCELLED', 'BOUNCE', 'AFTER_CLEAR_BOUNCE')
                """
    )
    fun findDuplicateCheques(
        @Param("chequeNum") chequeNum: String,
        @Param("pdi") pdi: Long,
        @Param("chequeDate") chequeDate: LocalDate,
        @Param("tenant") tenant: String
    ): ChequeHandle?

    @Query(
        "SELECT SUM(CASE WHEN chs.settlementId IS NOT NULL THEN s.paidAmount ELSE ap.amount END) " +
                "FROM ChequeHandle chs " +
                "LEFT JOIN Settlement s ON chs.settlementId = s.id " +
                "LEFT JOIN AdvancePayment ap ON chs.advancePaymentId = ap.id " +
                "WHERE (s.partnerDetailId = :partnerDetailId OR ap.partnerDetailId = :partnerDetailId) and chs.tenant= :tenant and chs.settled = false and chs.status NOT IN ('CANCELLED', 'BOUNCE')"
    )
    fun getTotalOutstandingChequeAmount(
        @Param("partnerDetailId") partnerDetailId: Long,
        @Param("tenant") tenant: String
    ): BigDecimal?

    @Query(
        "SELECT COALESCE(SUM(ism.paidAmount), 0) " +
                "FROM ChequeHandle chs " +
                "LEFT JOIN Settlement s ON chs.settlementId = s.id " +
                "LEFT JOIN InvoiceSettlement ism ON ism.id.settlementId = s.id " +
                "LEFT JOIN BkInvoice bk ON ism.id.invoiceId = bk.id " +
                "WHERE s.partnerDetailId = :partnerDetailId " +
                "AND chs.tenant = :tenant " +
                "AND chs.settled = false " +
                "AND chs.status NOT IN ('CANCELLED', 'BOUNCE') " +
                "AND bk.dueDate < CURRENT_DATE"
    )
    fun getTotalDueOutstandingChequeAmount(
        @Param("partnerDetailId") partnerDetailId: Long,
        @Param("tenant") tenant: String
    ): BigDecimal?

    @Query(
        "SELECT " +
                "NEW com.pharmeasy.model.ChequeHandleDto(" +
                "   chs.id, chs.chequeNum, " +
                "   COALESCE(r.retailerName, s.supplierName, ap.vendorName), " +
                "   COALESCE(r.partnerId, s.partnerId, ap.partnerId), " +
                "   COALESCE(r.bankName, s.bankName, ap.bankName), " +
                "   COALESCE(r.amount, s.paidAmount, ap.amount), " +
                "   COALESCE(r.transactionDate, s.chequeDate, ap.chequeDate), " +
                "   chs.updatedOn, " +
                "   COALESCE(s.settlementNumber, null), " +
                "   chs.status, " +
                "   chs.reason, " +
                "   chs.assignTo, " +
                "   chs.inApprove, " +
                "   COALESCE(r.partnerDetailId, s.partnerDetailId, ap.partnerDetailId), " +
                "   chs.charge, " +
                "   chs.chargeAmt, " +
                "   COALESCE(s.type, ap.type), " +
                "   COALESCE(r.receiptNumber, ap.documentId)," +
                "   case when chs.status='BOUNCE' then chs.bounceDate else null end, " +
                "   chs.assignTo," +
                "   COALESCE(r.amount, s.paidAmount)) " +
                "FROM ChequeHandle chs " +
                "LEFT JOIN Receipt r ON chs.receiptId = r.id " +
                "LEFT JOIN ReceiptSettlement rs ON r.id = rs.id.receiptId " +
                "LEFT JOIN Settlement s ON (chs.settlementId = s.id OR rs.id.settlementId = s.id) " +
                "LEFT JOIN AdvancePayment ap ON (chs.advancePaymentId = ap.id OR r.advanceId = ap.id) " +
                "WHERE chs.id = :chequeId "
    )
    fun getChequeHandlingDataById(
        @Param("chequeId") chequeId: Long?
    ): ChequeHandleDto
}
