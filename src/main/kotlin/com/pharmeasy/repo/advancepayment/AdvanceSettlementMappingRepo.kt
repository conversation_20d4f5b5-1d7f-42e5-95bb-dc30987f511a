package com.pharmeasy.repo.advancepayment

import com.pharmeasy.data.*
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param

interface AdvanceSettlementMappingRepo : JpaRepository<AdvanceSettlementMapping, AdvanceSettlementId> {

    @Query( "SELECT ap.documentId FROM AdvancePayment ap, AdvanceSettlementMapping asm " +
            "WHERE asm.id.settlementId = :settlementId AND ap.id = asm.id.advancePaymentId")
    fun getAdvanceInfoForSettlement(@Param("settlementId") settlementId: Long): MutableList<String>?

    @Query( "SELECT asm FROM AdvanceSettlementMapping asm " +
            "WHERE asm.id.settlementId = :settlementId ")
    fun getSettlementAdvPayBySettlementId(@Param("settlementId") settlementId: Long): List<AdvanceSettlementMapping>

    @Query(" SELECT ap from AdvancePayment ap, AdvanceSettlementMapping ast WHERE ast.id.settlementId = :settlementId and ap.id = ast.id.advancePaymentId")
    fun getAdvPayForSettlement(@Param("settlementId") settlementId: Long): List<AdvancePayment>

    @Query("SELECT s FROM Settlement s inner join AdvanceSettlementMapping ast on s.id = ast.id.settlementId where ast.id.advancePaymentId = :advancePaymentId")
    fun getAllSettlementsByAdvancePaymentId(@Param("advancePaymentId") advancePaymentId: Long): List<Settlement>
}