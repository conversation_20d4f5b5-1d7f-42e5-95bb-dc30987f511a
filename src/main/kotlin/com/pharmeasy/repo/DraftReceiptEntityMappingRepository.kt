package com.pharmeasy.repo

import com.pharmeasy.data.DraftReceiptEntityMapping
import com.pharmeasy.type.SettleableType
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.util.Streamable

interface DraftReceiptEntityMappingRepository : JpaRepository<DraftReceiptEntityMapping, Long> {
    @Query(
        """
        SELECT d FROM DraftReceiptEntityMapping d
        WHERE d.receipt.id = :receiptId
        AND d.entityId = :entityId
        AND d.entityType = :settleableType
        AND d.active = true
    """,
        nativeQuery = false
    )
    fun findExistingMapping(
        receiptId: Long,
        entityId: Long,
        settleableType: SettleableType
    ): DraftReceiptEntityMapping?

    @Query(
        """
        SELECT d.draft_receipt_id, COUNT(*) FROM draft_receipt_entity_mapping d
        WHERE d.draft_receipt_id IN (:draftIds)
        AND d.active = true
        group by d.draft_receipt_id
    """,
        nativeQuery = true
    )
    fun countByReceiptIds(draftIds: Collection<Long>): List<Array<Any>>
}
