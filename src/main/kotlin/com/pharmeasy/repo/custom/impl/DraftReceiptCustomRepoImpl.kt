package com.pharmeasy.repo.custom.impl

import com.pharmeasy.data.DraftReceipt
import com.pharmeasy.dto.ReceiptListFilter
import com.pharmeasy.repo.custom.DraftReceiptCustomRepo
import com.pharmeasy.specification.DraftReceiptListSpecification
import org.mapstruct.Qualifier
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Slice
import org.springframework.data.domain.SliceImpl
import org.springframework.stereotype.Repository
import javax.persistence.EntityManager
import javax.persistence.PersistenceContext

@Repository
class DraftReceiptCustomRepoImpl(
): DraftReceiptCustomRepo {

    @PersistenceContext(unitName = "read")
    private lateinit var em: EntityManager

    override fun getByReceiptFilter(receiptListFilter: ReceiptListFilter): Slice<DraftReceipt> {
        val cb = em.criteriaBuilder!!
        val cq = cb.createQuery(DraftReceipt::class.java)
        val root = cq.from(DraftReceipt::class.java)

        val predicate  = DraftReceiptListSpecification(receiptListFilter).toPredicate(root, cq, cb)
        cq.where(predicate)
        cq.orderBy(cb.asc(root.get<Long>("id"))) // TODO: Should this be changed?

        val query = em.createQuery(cq)

        // Pagination logic for Slice implementation avoiding unnecessary count query
        query.firstResult = (receiptListFilter.page * receiptListFilter.size)
        val extraSize = receiptListFilter.size + 1
        query.maxResults = extraSize
        val content = query.resultList
        val hasNext = content.size == extraSize
        if(hasNext){
            content.removeAt(extraSize - 1);
        }
        return SliceImpl(content, PageRequest.of(receiptListFilter.page, content.size), hasNext)
    }
}
