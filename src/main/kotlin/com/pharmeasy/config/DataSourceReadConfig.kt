package com.pharmeasy.config

import com.pharmeasy.annotation.ReadOnlyRepository
import com.pharmeasy.transactionaloutbox.jpa.config.DynamicPhysicalNamingStrategy
import org.hibernate.cfg.Environment
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.jdbc.DataSourceBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile
import org.springframework.data.jpa.repository.config.EnableJpaRepositories
import org.springframework.orm.jpa.JpaTransactionManager
import org.springframework.orm.jpa.JpaVendorAdapter
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.annotation.EnableTransactionManagement
import javax.persistence.EntityManagerFactory
import javax.sql.DataSource

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
    entityManagerFactoryRef = "readerEntityManagerFactory",
    basePackages = ["com.pharmeasy.repo.read"],
    includeFilters = [ComponentScan.Filter(ReadOnlyRepository::class)]
)
@Profile("!test")
class DataSourceReadConfig {

    @Autowired
    private lateinit var jpaVendorAdapter: JpaVendorAdapter

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource-read.hikari")
    fun readerDataSource(): DataSource {
        return DataSourceBuilder.create().build()
    }

    @Bean
    fun readerEntityManagerFactory(readerDataSource: DataSource): LocalContainerEntityManagerFactoryBean {
        val em = LocalContainerEntityManagerFactoryBean()
        em.dataSource = readerDataSource
        em.setPackagesToScan("com.pharmeasy.data")
        em.jpaVendorAdapter = jpaVendorAdapter
        var jpaProperties = mutableMapOf<String, Any>()
        jpaProperties[Environment.PHYSICAL_NAMING_STRATEGY] = DynamicPhysicalNamingStrategy::class.java.name
        em.jpaPropertyMap.putAll(jpaProperties)
        em.persistenceUnitName = "read"
        return em
    }

    @Bean
    fun readerTransactionManager(readerEntityManagerFactory: EntityManagerFactory): PlatformTransactionManager {
        return JpaTransactionManager(readerEntityManagerFactory)
    }
}
