package com.pharmeasy.config

import com.pharmeasy.annotation.ReadOnlyRepository
import com.pharmeasy.transactionaloutbox.jpa.config.DynamicPhysicalNamingStrategy
import org.hibernate.cfg.Environment
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.jdbc.DataSourceBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.context.annotation.Profile
import org.springframework.data.jpa.repository.config.EnableJpaRepositories
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.orm.jpa.JpaTransactionManager
import org.springframework.orm.jpa.JpaVendorAdapter
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.annotation.EnableTransactionManagement
import javax.persistence.EntityManagerFactory
import javax.sql.DataSource

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
    entityManagerFactoryRef = "writerEntityManagerFactory",
    basePackages = ["com.pharmeasy"],
    excludeFilters = [ComponentScan.Filter(ReadOnlyRepository::class)]
)
@Profile("!test")
class DataSourceWriteConfig {

    @Autowired
    private lateinit var jpaVendorAdapter: JpaVendorAdapter

    @Autowired
    private lateinit var dynamicPhysicalNamingStrategy: DynamicPhysicalNamingStrategy
    @Bean
    @ConfigurationProperties(prefix = "spring.datasource-write.hikari")
    fun writerDataSource(): DataSource {
        return DataSourceBuilder.create().build()
    }

    @Bean
    fun writerEntityManagerFactory(writerDataSource: DataSource): LocalContainerEntityManagerFactoryBean {
        val em = LocalContainerEntityManagerFactoryBean()
        em.dataSource = writerDataSource
        em.setPackagesToScan("com.pharmeasy.data","com.pharmeasy.transactionaloutbox")
        var jpaProperties = mutableMapOf<String, Any>()
        jpaProperties[Environment.PHYSICAL_NAMING_STRATEGY] = dynamicPhysicalNamingStrategy
        em.jpaPropertyMap.putAll(jpaProperties)

        em.jpaVendorAdapter = jpaVendorAdapter
        em.persistenceUnitName = "write"
        return em
    }

    @Bean
    @Primary
    fun transactionManager(writerEntityManagerFactory: EntityManagerFactory): PlatformTransactionManager {
        return JpaTransactionManager(writerEntityManagerFactory)
    }

    @Bean
    fun updateJdbcTemplate(): JdbcTemplate {
        return JdbcTemplate(writerDataSource())
    }
}
