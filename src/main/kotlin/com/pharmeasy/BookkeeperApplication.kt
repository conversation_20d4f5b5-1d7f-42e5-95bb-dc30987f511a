package com.pharmeasy

import com.fasterxml.jackson.annotation.JsonAutoDetect
import com.fasterxml.jackson.annotation.PropertyAccessor
import com.fasterxml.jackson.databind.ObjectMapper
import com.pharmeasy.annotation.ReadOnlyRepository
import org.springframework.boot.SpringApplication
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.domain.EntityScan
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.cache.annotation.EnableCaching
import org.springframework.cloud.openfeign.EnableFeignClients
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.Configuration
import org.springframework.data.jpa.repository.config.EnableJpaRepositories
import org.springframework.data.redis.connection.RedisConnectionFactory
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer
import org.springframework.integration.redis.util.RedisLockRegistry
import org.springframework.scheduling.annotation.EnableScheduling

@SpringBootApplication(scanBasePackages = ["com.pharmeasy"])
@EnableFeignClients
@EnableScheduling
@EnableCaching
@EnableJpaRepositories(
    entityManagerFactoryRef = "writerEntityManagerFactory",
    basePackages = ["com.pharmeasy.transactionaloutbox","com.pharmeasy.transactionaloutbox.jpa.entity"]
)
class BookkeeperApplication

fun main(args: Array<String>) {
     SpringApplication.run(BookkeeperApplication::class.java, *args)
}

@Configuration
class Config {

     @Bean
     fun redisTemplate(factory: RedisConnectionFactory): RedisTemplate<String, String> {
          val om = ObjectMapper()
          om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY)
          om.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL)
          val jackson2JsonRedisSerializer = Jackson2JsonRedisSerializer(Any::class.java)
          jackson2JsonRedisSerializer.setObjectMapper(om)
          val template = StringRedisTemplate(factory)
          template.valueSerializer = jackson2JsonRedisSerializer
          template.hashKeySerializer = jackson2JsonRedisSerializer
          template.hashValueSerializer = jackson2JsonRedisSerializer
          template.valueSerializer = jackson2JsonRedisSerializer
          template.afterPropertiesSet()
          return template
     }

     @Bean
     fun redisLockRegistry(factory: RedisConnectionFactory): RedisLockRegistry {
          return RedisLockRegistry(factory, "bookkeeper-lock")
     }
}
