package com.pharmeasy.dto

import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.ReceiptStatus
import com.pharmeasy.type.ReceiptType
import org.springframework.format.annotation.DateTimeFormat
import java.time.LocalDate

data class ReceiptListFilter(
    val partnerDetailId: Long?,
    val receiptNumber: String?,
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    val receiptDateStart: LocalDate?,
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    val receiptDateEnd: LocalDate?,
    val status: ReceiptStatus?,
    val paymentType: PaymentType?,
    val receiptType: ReceiptType?,
    val page: Int = 0,
    val size: Int = 10
)
