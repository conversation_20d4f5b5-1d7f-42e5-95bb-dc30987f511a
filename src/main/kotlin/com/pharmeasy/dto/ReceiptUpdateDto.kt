package com.pharmeasy.dto

import com.pharmeasy.data.DraftReceiptEntityMappingDto
import com.pharmeasy.type.PaymentType
import java.time.LocalDate

data class ReceiptUpdateDto(
    var id: Long,
    var amount: Double,
    var paymentType: PaymentType,
    var txReferenceNumber: String? = null,
    var transactionDate: LocalDate? = null,
    var bankName: String? = null,
    var remarks: String? = null,
    var settleableMappings: MutableList<DraftReceiptEntityMappingDto>? = null
)
