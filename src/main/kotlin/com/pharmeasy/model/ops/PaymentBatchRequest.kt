package com.pharmeasy.model.ops

import com.pharmeasy.type.AdvancePaymentSource
import com.pharmeasy.type.CreationType
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.ops.PaymentPartnerType
import java.time.LocalDate

data class PaymentBatchRequest(
    val partnerInfo: PartnerInfo,
    val payments: List<PaymentInfo>,
    var user: String = "SYSTEM",
    var settlementGroupNumber: SettlementGroupNumber? = null
)

data class SettlementGroupNumber(
    val number: String
) {
    var iteration: Int = 1

    fun next(): String {
        return number + "-" + iteration++
    }
}

data class PartnerInfo(
    val partnerId: Long,
    val partnerDetailId: Long, // retailerFrontEndPartyCode
    val partnerName: String,
    val tenant: String,
    val distributorPdi: Long?,
)

data class PaymentInfo(
    val customerTransactionId: String,
    val transactionDate: Long,
    val transactionAmount: Double,
    val paymentType: PaymentType,
    val paymentMode: String? = null,
    val initiatedBy: String? = null,
    var paymentSource: AdvancePaymentSource? = null,
    val bankDetails: BankDetails? = null,
    val creditDetails: CreditDetails? = null,
    val settleables: List<SettleableInfo> = emptyList(),
    val salesmanId: Long? = null,
    val salesmanName: String? = null
)

data class BankDetails(
    val bankName: String? = null,
    val chequeNo: String? = null,
    val chequeDate: LocalDate? = null,
    val neftId: String? = null,
    val isBankDeposit: Boolean? = false,
    val depositSlipNo: String? = null
)

data class CreditDetails(
    val transactionId: String? = null,
    val dueDate: Long? = null,
    val partner: PaymentPartnerType? = null
)

data class SettleableInfo(
    val number: String,
    val type: CreationType = CreationType.INVOICE,
    val amount: Double,  // amount to settle for this invoice/debit note
    val category: String? = null
)
