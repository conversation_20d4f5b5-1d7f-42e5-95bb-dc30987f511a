package com.pharmeasy.model

import java.math.BigDecimal

data class AggregatedChequeHandleDto(

    var totalRecievedCheques: Long? = 0L,
    var totalRecievedChequesAmount: BigDecimal? = BigDecimal.ZERO,
    var totalDepositedCheques: Long? = 0L,
    var totalDepositedChequesAmount: BigDecimal? = BigDecimal.ZERO

) {
    constructor(l1: Long?, d1: Double?, l2: Long?, d2: Double?) : this(
        totalRecievedCheques = l1 ?: 0L, totalRecievedChequesAmount = BigDecimal(d1 ?: 0.0),
        totalDepositedCheques = l2 ?: 0L, totalDepositedChequesAmount = BigDecimal(d2 ?: 0.0)
    )
}
