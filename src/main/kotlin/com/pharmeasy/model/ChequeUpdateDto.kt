package com.pharmeasy.model

import com.pharmeasy.exception.RequestException
import com.pharmeasy.type.ChequeHandleType
import java.math.BigDecimal
import java.time.LocalDateTime

class ChequeUpdateDto (
        var id: Long,
        var updatedOn: LocalDateTime? = null,
        var status: ChequeHandleType,
        var reason: String?,
        var updatedBy: String,
        var assignTo: String? = null,
        var charge: Boolean? = false,
        var chargeAmt: Double? = null,
        var bounceDate: LocalDateTime? = null
) {
    fun validate() {
        val errors = mutableListOf<String>()
        if ((status == ChequeHandleType.BOUNCE || status == ChequeHandleType.AFTER_CLEAR_BOUNCE )&& bounceDate == null) {
            throw RequestException("Please select cheque bounce date")
        }
    }
}