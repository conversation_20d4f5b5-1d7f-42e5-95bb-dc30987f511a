package com.pharmeasy.specification

import com.pharmeasy.data.DraftReceipt
import com.pharmeasy.dto.ReceiptListFilter
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.ReceiptStatus
import com.pharmeasy.type.ReceiptType
import org.springframework.data.jpa.domain.Specification
import java.time.LocalDateTime
import javax.persistence.criteria.CriteriaBuilder
import javax.persistence.criteria.CriteriaQuery
import javax.persistence.criteria.Predicate
import javax.persistence.criteria.Root

class DraftReceiptListSpecification(
    val receiptListFilter: ReceiptListFilter
) : Specification<DraftReceipt> {
    override fun toPredicate(
        root: Root<DraftReceipt?>,
        query: CriteriaQuery<*>,
        criteriaBuilder: CriteriaBuilder
    ): Predicate {
        val predicates = ArrayList<Predicate>()

        if (receiptListFilter.partnerDetailId != null) {
            predicates.add(
                criteriaBuilder.equal(
                    root.get<Long>(DraftReceipt::partnerDetailId.name),
                    receiptListFilter.partnerDetailId
                )
            )
        }
        if (receiptListFilter.receiptNumber != null) {
            predicates.add(
                criteriaBuilder.equal(
                    root.get<String>(DraftReceipt::receiptNumber.name),
                    receiptListFilter.receiptNumber
                )
            )
        }
        if (receiptListFilter.receiptDateStart != null && receiptListFilter.receiptDateEnd != null) {
            predicates.add(
                criteriaBuilder.greaterThanOrEqualTo(
                    root.get<LocalDateTime>(DraftReceipt::createdOn.name),
                    receiptListFilter.receiptDateStart.atStartOfDay()
                )
            )
            predicates.add(
                criteriaBuilder.lessThan(
                    root.get<LocalDateTime>(DraftReceipt::createdOn.name),
                    receiptListFilter.receiptDateEnd.plusDays(1).atStartOfDay()
                )
            )
        }
        if (receiptListFilter.status != null) {
            predicates.add(
                criteriaBuilder.equal(
                    root.get<ReceiptStatus>(DraftReceipt::status.name),
                    receiptListFilter.status
                )
            )
        }
        if (receiptListFilter.paymentType != null) {
            predicates.add(
                criteriaBuilder.equal(
                    root.get<PaymentType>(DraftReceipt::paymentType.name),
                    receiptListFilter.paymentType
                )
            )
        }
        if(receiptListFilter.receiptType != null){
            predicates.add(criteriaBuilder.equal(root.get<ReceiptType>(DraftReceipt::receiptType.name), receiptListFilter.receiptType))
        }

        return criteriaBuilder.and(*predicates.toTypedArray())
    }
}
