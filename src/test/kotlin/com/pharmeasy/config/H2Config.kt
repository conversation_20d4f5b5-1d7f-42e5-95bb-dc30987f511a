package com.pharmeasy.config

import com.pharmeasy.annotation.Post
import org.mvnsearch.h2.H2FunctionsLoader
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
import org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.context.annotation.Profile
import org.springframework.core.env.Environment
import org.springframework.data.jpa.repository.config.EnableJpaRepositories
import org.springframework.jdbc.datasource.DriverManagerDataSource
import org.springframework.orm.jpa.JpaTransactionManager
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter
import org.springframework.stereotype.Repository
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.annotation.EnableTransactionManagement
import org.springframework.transaction.annotation.Transactional
import java.util.*
import javax.annotation.PostConstruct
import javax.persistence.EntityManager
import javax.persistence.EntityManagerFactory
import javax.persistence.PersistenceContext
import javax.sql.DataSource

@TestConfiguration
@EnableJpaRepositories(
    entityManagerFactoryRef = "writerEntityManagerFactory",
    basePackages = ["com.pharmeasy.repo"]
)
@EnableTransactionManagement
@Profile("test")
class H2Config {

    @Autowired
    private lateinit var env: Environment

    @Primary
    @Bean
    fun dataSource(): DataSource {
        val dataSource = DriverManagerDataSource()
        dataSource.setDriverClassName(env.getProperty("spring.datasource.driver-class-name")!!)
        dataSource.url = env.getProperty("spring.datasource.url")
        dataSource.username = env.getProperty("spring.datasource.username")
        dataSource.password = env.getProperty("spring.datasource.password")
        return dataSource
    }

    @Primary
    @Bean(name = ["transactionManager"])
    fun dbTransactionManager(): PlatformTransactionManager {
        val transactionManager = JpaTransactionManager()
        transactionManager.entityManagerFactory = entityManagerFactory().getObject()
        return transactionManager
    }

    @Primary
    @Bean
    fun entityManagerFactory(): LocalContainerEntityManagerFactoryBean {
        val em = LocalContainerEntityManagerFactoryBean()
        em.dataSource = dataSource()
        em.setPackagesToScan("com.pharmeasy")
        em.jpaVendorAdapter = HibernateJpaVendorAdapter()
        em.setJpaProperties(additionalProperties())
        return em
    }

    @Bean
    fun writerEntityManagerFactory(writerDataSource: DataSource): LocalContainerEntityManagerFactoryBean {
        return entityManagerFactory()
    }

    @Bean
    internal fun transactionManager(@Autowired entityManagerFactory: EntityManagerFactory): JpaTransactionManager {
        val transactionManager = JpaTransactionManager()
        transactionManager.entityManagerFactory = entityManagerFactory
        return transactionManager
    }

    internal fun additionalProperties(): Properties {
        val hibernateProperties = Properties()
        hibernateProperties.setProperty("hibernate.hbm2ddl.auto", env.getProperty("spring.jpa.hibernate.ddl-auto"))
        hibernateProperties.setProperty("hibernate.dialect", env.getProperty("spring.jpa.properties.hibernate.dialect"))
        hibernateProperties.setProperty("hibernate.show_sql", env.getProperty("spring.jpa.show-sql"))
        hibernateProperties["hibernate.physical_naming_strategy"] =
            SpringPhysicalNamingStrategy::class.java.name
        hibernateProperties["hibernate.implicit_naming_strategy"] = SpringImplicitNamingStrategy::class.java.name
        return hibernateProperties
    }

    @PostConstruct
    fun initDbFunctions() {
        H2FunctionsLoader.loadMysqlFunctions(dataSource());
    }
}

@Repository
@Profile("test")
class EntityManagerRepository {
    @PersistenceContext
    private lateinit var entityManager: EntityManager

    @Transactional
    fun truncateTable(tableName: List<String>) {
        fun exec(sql: String) {
            val query = entityManager.createNativeQuery(sql)
            query.executeUpdate()
        }
        exec("SET REFERENTIAL_INTEGRITY FALSE")
        tableName.forEach { exec("TRUNCATE TABLE $it") }
        exec("SET REFERENTIAL_INTEGRITY TRUE")
    }
}
