package com.pharmeasy.utils

import com.pharmeasy.data.AdvancePayment
import com.pharmeasy.data.BkInvoice
import com.pharmeasy.data.Charges
import com.pharmeasy.data.Company
import com.pharmeasy.data.CreditNote
import com.pharmeasy.data.DraftReceipt
import com.pharmeasy.data.DraftReceiptEntityMapping
import com.pharmeasy.data.Receipt
import com.pharmeasy.data.RetailerDebitNote
import com.pharmeasy.data.RetailerDebitNoteItems
import com.pharmeasy.data.Settlement
import com.pharmeasy.dto.SettleableDetails
import com.pharmeasy.model.InvoiceStatus
import com.pharmeasy.model.Supplier
import com.pharmeasy.model.ops.BankDetails
import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.model.ops.RIOPaymentDTO
import com.pharmeasy.model.ops.SettleableInfo
import com.pharmeasy.type.AdvancePaymentSource
import com.pharmeasy.type.CreationType
import com.pharmeasy.type.DnType
import com.pharmeasy.type.PartnerType
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.ReceiptStatus
import com.pharmeasy.type.ReceiptType
import com.pharmeasy.type.SettleableType
import com.pharmeasy.type.UseCaseType
import com.pharmeasy.type.ops.PaymentPartnerType
import com.pharmeasy.util.UUIDUtil
import io.mockk.every
import io.mockk.mockk
import java.math.BigDecimal
import java.time.Duration
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.Date

object DataProvider {

    const val PARTNER_ID = 1L
    const val PARTNER_NAME = "partner"
    const val PDI = 1L
    const val DISTRIBUTOR_PDI = 2L
    const val TENANT = "th001"
    const val TX_ID = "DR12345678"
    const val INVOICE_NUMBER = "BA0123-456"
    const val DN_NUMBER = "DN1234-567"
    const val CREDIT_NOTE_NUMBER = "CN1234-567"
    const val CREDIT_TRANSACTON_ID = "CR12345678"
    const val NEFT_ID = "**********"
    const val CHEQUE_NO = "123456"
    const val BANK_NAME = "Bank"
    const val COMPANY_CODE = "companyCode"


    fun supplier(
        partnerId: Long = PARTNER_ID,
        partnerName: String = PARTNER_NAME
    ): Supplier {
        return Supplier(
            partnerId = partnerId,
            partnerName = partnerName,
            firmTypes = null,
            partnerDetailList = mutableListOf(),
            retailerCommercial = null,
            distributorCommercial = null
        )
    }

    /**
     * CASH transaction for one invoice without excess payment
     */
    fun rioPaymentDto(
        retailerTxnType: String = "CASH",
        retailerTxnAmount: Double = 1000.0,
        invoiceNumber: String = INVOICE_NUMBER,
        retailerTxnId: String = TX_ID,
        initiatedBy: String = "SALESMAN",
        paymentMode: String = "DIGITAL_RECEIPT",
        type: CreationType = CreationType.INVOICE,
        retailerTxnDate: Long = Date().time - Duration.ofSeconds(10).toMillis(),
        retailerName: String = "Opt_NoGST",
        retailerPartyCode: String = "$PDI",
        retailerFrontEndPartyCode: String = "$PDI",
        distributorInvoiceId: String = "230761Unit",
        distributorInvoiceAmount: Double = 1000.0,
        distributorInvoiceOutstandingAmount: Double = 1000.0,
        distributorInvoiceDueDate: Long = (Date().time + Duration.ofDays(1).toMillis()),
        retailerTxnStatus: String = "SUCCESS",
        advanceAmount: Double? = null,
        invoicePrefix: String = "SB",
        distributorInvoiceDate: Long = (Date().time - Duration.ofDays(1).toMillis()),
        category: String = "INVOICE",
        retailerTotalTxnAmount: Double = 1000.0,
        distributorId: Long = DISTRIBUTOR_PDI,
        creditTransactionId: String? = null,
        creditDueDate: Long? = null,
        creditPartner: PaymentPartnerType? = null,
        salesmanId: String? = "1007",
        salesmanName: String? = "Test Salesman"
    ): RIOPaymentDTO {
        return RIOPaymentDTO(
            invoiceNumber = invoiceNumber,  // Invoice number
            retailerTxnId = retailerTxnId,  // Retailer Transaction ID
            retailerTxnAmount = retailerTxnAmount,  // Retailer Transaction Amount
            retailerTxnType = retailerTxnType,  // Retailer Transaction Type
            initiatedBy = initiatedBy,  // Initiator of the transaction
            paymentMode = paymentMode,  // Payment Mode
            type = type,  // Type of the payment, mapped to CreationType enum
            retailerTxnDate = retailerTxnDate,  // Retailer Transaction Date (timestamp)
            retailerName = retailerName,  // Retailer Name
            retailerPartyCode = retailerPartyCode,  // Retailer Party Code
            retailerFrontEndPartyCode = retailerFrontEndPartyCode,  // Retailer Front End Party Code
            distributorInvoiceId = distributorInvoiceId,  // Distributor Invoice ID
            distributorInvoiceAmount = distributorInvoiceAmount,  // Distributor Invoice Amount
            distributorInvoiceOutstandingAmount = distributorInvoiceOutstandingAmount,  // Outstanding Amount for the distributor invoice
            distributorInvoiceDueDate = distributorInvoiceDueDate,  // Distributor Invoice Due Date (timestamp)
            retailerTxnStatus = retailerTxnStatus,  // Retailer Transaction Status
            advanceAmount = advanceAmount,  // Advance Amount (nullable)
            invoicePrefix = invoicePrefix,  // Invoice Prefix
            distributorInvoiceDate = distributorInvoiceDate,  // Distributor Invoice Date (timestamp)
            category = category,  // Category of payment
            retailerTotalTxnAmount = retailerTotalTxnAmount,  // Total Retailer Transaction Amount
            distributorId = distributorId,  // Distributor ID
            creditTransactionId = creditTransactionId,  // Credit Transaction ID (nullable)
            creditDueDate = creditDueDate,  // Credit Due Date (nullable)
            creditPartner = creditPartner, // Credit Partner (nullable)
            salesmanId = salesmanId,
            salesmanName = salesmanName
        )
    }

    fun chequePaymentDto(
        chequeNo: String = CHEQUE_NO,
        chequeDate: Long = Date().time - Duration.ofSeconds(10).toMillis(),
        bankName: String = BANK_NAME,
        retailerTxnAmount: Double = 1000.0,
        invoiceNumber: String = INVOICE_NUMBER,
        retailerTxnId: String = TX_ID,
        initiatedBy: String = "SALESMAN",
        type: CreationType = CreationType.INVOICE,
        retailerTxnDate: Long = Date().time - Duration.ofSeconds(10).toMillis(),
        retailerName: String = "Opt_NoGST",
        retailerPartyCode: String = "$PDI",
        retailerFrontEndPartyCode: String = "$PDI",
        distributorInvoiceId: String = "230761Unit",
        distributorInvoiceAmount: Double = 1000.0,
        distributorInvoiceOutstandingAmount: Double = 1000.0,
        distributorInvoiceDueDate: Long = (Date().time + Duration.ofDays(1).toMillis()),
        retailerTxnStatus: String = "SUCCESS",
        advanceAmount: Double? = null,
        invoicePrefix: String = "SB",
        distributorInvoiceDate: Long = (Date().time - Duration.ofDays(1).toMillis()),
        category: String = "INVOICE",
        retailerTotalTxnAmount: Double = 1000.0,
        distributorId: Long = DISTRIBUTOR_PDI,
        creditTransactionId: String? = null,
        creditDueDate: Long? = null,
        creditPartner: PaymentPartnerType? = null
    ): RIOPaymentDTO {
        return rioPaymentDto().apply {
            this.chequeNo = chequeNo
            this.chequeDate = chequeDate
            this.bankName = bankName
            this.retailerTxnType = PaymentType.CHEQUE.name
            this.retailerTxnAmount = retailerTxnAmount
            this.invoiceNumber = invoiceNumber
            this.retailerTxnId = retailerTxnId
            this.initiatedBy = initiatedBy
            this.paymentMode = "DIGITAL_RECEIPT"
            this.type = type
            this.retailerTxnDate = retailerTxnDate
            this.retailerName = retailerName
            this.retailerPartyCode = retailerPartyCode
            this.retailerFrontEndPartyCode = retailerFrontEndPartyCode
            this.distributorInvoiceId = distributorInvoiceId
            this.distributorInvoiceAmount = distributorInvoiceAmount
            this.distributorInvoiceOutstandingAmount = distributorInvoiceOutstandingAmount
            this.distributorInvoiceDueDate = distributorInvoiceDueDate
            this.retailerTxnStatus = retailerTxnStatus
            this.advanceAmount = advanceAmount
            this.invoicePrefix = invoicePrefix
            this.distributorInvoiceDate = distributorInvoiceDate
            this.category = category
            this.retailerTotalTxnAmount = retailerTotalTxnAmount
            this.distributorId = distributorId
            this.creditTransactionId = creditTransactionId
            this.creditDueDate = creditDueDate
            this.creditPartner = creditPartner
        }
    }

    fun neftPaymentDto(
        neftId: String = NEFT_ID,
        invoiceNumber: String = INVOICE_NUMBER,
        retailerTxnId: String = TX_ID,
        initiatedBy: String = "SALESMAN",
        paymentMode: String = "DIGITAL_RECEIPT",
        type: CreationType = CreationType.INVOICE,
        retailerTxnDate: Long = Date().time - Duration.ofSeconds(10).toMillis(),
        retailerName: String = "Opt_NoGST",
        retailerPartyCode: String = "$PDI",
        retailerFrontEndPartyCode: String = "$PDI",
        distributorInvoiceId: String = "230761Unit",
        distributorInvoiceAmount: Double = 1000.0,
        distributorInvoiceOutstandingAmount: Double = 1000.0,
        distributorInvoiceDueDate: Long = (Date().time + Duration.ofDays(1).toMillis()),
        retailerTxnStatus: String = "SUCCESS",
        advanceAmount: Double? = null,
        invoicePrefix: String = "SB",
        distributorInvoiceDate: Long = (Date().time - Duration.ofDays(1).toMillis()),
        category: String = "INVOICE",
        retailerTotalTxnAmount: Double = 1000.0,
        distributorId: Long = DISTRIBUTOR_PDI,
        creditTransactionId: String? = null,
        creditDueDate: Long? = null,
        creditPartner: PaymentPartnerType? = null
    ): RIOPaymentDTO {
        return rioPaymentDto().apply {
            this.neftId = neftId
            this.invoiceNumber = invoiceNumber
            this.retailerTxnId = retailerTxnId
            this.initiatedBy = initiatedBy
            this.paymentMode = paymentMode
            this.type = type
            this.retailerTxnDate = retailerTxnDate
            this.retailerName = retailerName
            this.retailerPartyCode = retailerPartyCode
            this.retailerFrontEndPartyCode = retailerFrontEndPartyCode
            this.distributorInvoiceId = distributorInvoiceId
            this.distributorInvoiceAmount = distributorInvoiceAmount
            this.distributorInvoiceOutstandingAmount = distributorInvoiceOutstandingAmount
            this.distributorInvoiceDueDate = distributorInvoiceDueDate
            this.retailerTxnStatus = retailerTxnStatus
            this.advanceAmount = advanceAmount
            this.invoicePrefix = invoicePrefix
            this.distributorInvoiceDate = distributorInvoiceDate
            this.category = category
            this.retailerTotalTxnAmount = retailerTotalTxnAmount
            this.distributorId = distributorId
            this.creditTransactionId = creditTransactionId
            this.creditDueDate = creditDueDate
            this.creditPartner = creditPartner
        }
    }

    fun advanceRioPaymentDto(
        retailerTxnType: String = "CASH",
        retailerTxnId: String = TX_ID,
        initiatedBy: String = "SALESMAN",
        paymentMode: String = "DIGITAL_RECEIPT",
        retailerTxnDate: Long = Date().time - Duration.ofSeconds(10).toMillis(),
        retailerName: String = "Opt_NoGST",
        retailerPartyCode: String = "$PDI",
        retailerFrontEndPartyCode: String = "$PDI",
        retailerTxnStatus: String = "SUCCESS",
        advanceAmount: Double? = 1000.0,
        retailerTotalTxnAmount: Double = 1000.0,
        distributorId: Long = DISTRIBUTOR_PDI,
    ): RIOPaymentDTO {
        return RIOPaymentDTO(
            invoiceNumber = null,  // Invoice number
            retailerTxnId = retailerTxnId,  // Retailer Transaction ID
            retailerTxnAmount = null,  // Retailer Transaction Amount
            retailerTxnType = retailerTxnType,  // Retailer Transaction Type
            initiatedBy = initiatedBy,  // Initiator of the transaction
            paymentMode = paymentMode,  // Payment Mode
            type = null,  // Type of the payment, mapped to CreationType enum
            retailerTxnDate = retailerTxnDate,  // Retailer Transaction Date (timestamp)
            retailerName = retailerName,  // Retailer Name
            retailerPartyCode = retailerPartyCode,  // Retailer Party Code
            retailerFrontEndPartyCode = retailerFrontEndPartyCode,  // Retailer Front End Party Code
            distributorInvoiceId = null,  // Distributor Invoice ID
            distributorInvoiceAmount = null,  // Distributor Invoice Amount
            distributorInvoiceOutstandingAmount = null,  // Outstanding Amount for the distributor invoice
            distributorInvoiceDueDate = null,  // Distributor Invoice Due Date (timestamp)
            retailerTxnStatus = retailerTxnStatus,  // Retailer Transaction Status
            advanceAmount = advanceAmount,  // Advance Amount (nullable)
            invoicePrefix = null,  // Invoice Prefix
            distributorInvoiceDate = null,  // Distributor Invoice Date (timestamp)
            category = "ADVANCE_PAYMENT",  // Category of payment
            retailerTotalTxnAmount = retailerTotalTxnAmount,  // Total Retailer Transaction Amount
            distributorId = distributorId,  // Distributor ID
            creditTransactionId = null,  // Credit Transaction ID (nullable)
            creditDueDate = null,  // Credit Due Date (nullable)
            creditPartner = null  // Credit Partner (nullable)
        )
    }

    fun tradeCreditRepaymentDTO(): RIOPaymentDTO {
        return rioPaymentDto().apply {
            category = "CREDIT_REPAYMENT"
            creditTransactionId = "CR12345678"
            creditDueDate = Date().time + Duration.ofDays(1).toMillis()

        }
    }

    fun company(
        id: Long = 1L,
        companyCode: String = "companyCode",
        name: String = "company",
        darkStore: Boolean = false,
        updatedBy: String = "user",
        cashBalance: BigDecimal = BigDecimal.valueOf(5000),
        bankBalance: BigDecimal = BigDecimal.valueOf(5000),
        enableRioAutoCn: Boolean = false,
        isActive: Boolean = true,
        rioEnabled: Int = 0,
        isAutomail: Boolean = false,
        bankSlipTemplateName: String = "template",
        dummyAccountPdi: Long = 1L,
        isRetailerDnEnabled: Boolean = false,
        isCnPrintEnabled: Boolean = false
    ): Company {
        return Company(
            id = id,
            companyCode = companyCode,
            name = name,
            darkStore = darkStore,
            updatedBy = updatedBy,
            cashBalance = cashBalance,
            bankBalance = bankBalance,
            enableRioAutoCn = enableRioAutoCn,
            isActive = isActive,
            rioEnabled = rioEnabled,
            isAutomail = isAutomail,
            bankSlipTemplateName = bankSlipTemplateName,
            dummyAccountPdi = dummyAccountPdi,
            isRetailerDnEnabled = isRetailerDnEnabled,
            isCnPrintEnabled = isCnPrintEnabled
        )
    }

    fun bkInvoice(
        id: Long = 0L,
        invoiceNum: String = INVOICE_NUMBER,
        amount: Double = 1000.0,
        paidAmount: Double = 0.0,
        tenant: String = TENANT,
        createdOn: LocalDateTime = LocalDateTime.now(),
        updatedOn: LocalDateTime = LocalDateTime.now(),
        createdBy: String = "user",
        updatedBy: String = "user",
        supplierName: String = PARTNER_NAME,
        dueDate: LocalDate = LocalDate.now().plusDays(2),
        settledOn: LocalDateTime? = null,
        partnerDetailId: Long = PDI,
        partnerId: Long = PARTNER_ID,
        invoiceId: String = "230761Unit"
    ): BkInvoice {
        return BkInvoice(
            id = id,
            invoiceNum = invoiceNum,
            amount = amount,
            paidAmount = paidAmount,
            tenant = tenant,
            createdOn = createdOn,
            updatedOn = updatedOn,
            createdBy = createdBy,
            updatedBy = updatedBy,
            supplierName = supplierName,
            dueDate = dueDate,
            settledOn = settledOn,
            partnerDetailId = partnerDetailId,
            partnerId = partnerId,
            invoiceId = invoiceId
        )
    }

    fun paymentInfo(
        paymentType: PaymentType = PaymentType.CASH,
        customerTransactionId: String = TX_ID,
        paymentMode: String? = "DIGITAL_RECEIPT",
        initiatedBy: String? = "SALESMAN",
        isBankDeposit: Boolean = false
    ): PaymentInfo {
        // Create a real BankDetails object with the isBankDeposit property
        val bankDetails = BankDetails(
            bankName = null,
            chequeNo = null,
            chequeDate = null,
            neftId = null,
            isBankDeposit = isBankDeposit,
            depositSlipNo = null
        )

        // Create a real PaymentInfo object
        val paymentInfo = PaymentInfo(
            customerTransactionId = customerTransactionId,
            transactionDate = System.currentTimeMillis(),
            transactionAmount = 1000.0,
            paymentType = paymentType,
            paymentMode = paymentMode,
            initiatedBy = initiatedBy,
            paymentSource = null,
            bankDetails = bankDetails,
            creditDetails = null,
            settleables = emptyList(),
            salesmanId = null,
            salesmanName = null
        )

        return paymentInfo
    }

    // Real PaymentInfo objects for different payment types
    fun cashPaymentInfo(
        customerTransactionId: String = TX_ID,
        transactionAmount: Double = 1000.0,
        settleables: List<SettleableInfo> = emptyList()
    ): PaymentInfo {
        return PaymentInfo(
            customerTransactionId = customerTransactionId,
            transactionDate = System.currentTimeMillis(),
            transactionAmount = transactionAmount,
            paymentType = PaymentType.CASH,
            paymentMode = "CASH",
            initiatedBy = "USER",
            settleables = settleables
        )
    }

    fun chequePaymentInfo(
        customerTransactionId: String = TX_ID,
        transactionAmount: Double = 1000.0,
        chequeNo: String = CHEQUE_NO,
        chequeDate: LocalDate = LocalDate.now(),
        bankName: String = BANK_NAME,
        settleables: List<SettleableInfo> = emptyList()
    ): PaymentInfo {
        return PaymentInfo(
            customerTransactionId = customerTransactionId,
            transactionDate = System.currentTimeMillis(),
            transactionAmount = transactionAmount,
            paymentType = PaymentType.CHEQUE,
            paymentMode = "CHEQUE",
            initiatedBy = "USER",
            bankDetails = BankDetails(
                chequeNo = chequeNo,
                chequeDate = chequeDate,
                bankName = bankName
            ),
            settleables = settleables
        )
    }

    fun neftPaymentInfo(
        customerTransactionId: String = TX_ID,
        transactionAmount: Double = 1000.0,
        neftId: String = NEFT_ID,
        bankName: String = BANK_NAME,
        settleables: List<SettleableInfo> = emptyList()
    ): PaymentInfo {
        return PaymentInfo(
            customerTransactionId = customerTransactionId,
            transactionDate = System.currentTimeMillis(),
            transactionAmount = transactionAmount,
            paymentType = PaymentType.NEFT,
            paymentMode = "NEFT",
            initiatedBy = "USER",
            bankDetails = BankDetails(
                neftId = neftId,
                bankName = bankName
            ),
            settleables = settleables
        )
    }

    fun advancePaymentInfo(
        customerTransactionId: String = TX_ID,
        transactionAmount: Double = 1000.0,
        paymentType: PaymentType = PaymentType.CASH
    ): PaymentInfo {
        return PaymentInfo(
            customerTransactionId = customerTransactionId,
            transactionDate = System.currentTimeMillis(),
            transactionAmount = transactionAmount,
            paymentType = paymentType,
            paymentMode = paymentType.name,
            initiatedBy = "SYSTEM",
            paymentSource = AdvancePaymentSource.SYSTEM,
            settleables = emptyList()
        )
    }

    // SettleableInfo objects
    fun invoiceSettleableInfo(
        number: String = INVOICE_NUMBER,
        amount: Double = 500.0
    ): SettleableInfo {
        return SettleableInfo(
            number = number,
            type = CreationType.INVOICE,
            amount = amount,
            category = "INVOICE"
        )
    }

    fun debitNoteSettleableInfo(
        number: String = DN_NUMBER,
        amount: Double = 500.0
    ): SettleableInfo {
        return SettleableInfo(
            number = number,
            type = CreationType.DEBIT_NOTE,
            amount = amount,
            category = "DEBIT_NOTE"
        )
    }

    // PartnerInfo object
    fun partnerInfo(
        partnerId: Long = PARTNER_ID,
        partnerDetailId: Long = PDI,
        partnerName: String = PARTNER_NAME,
        distributorPdi: Long = DISTRIBUTOR_PDI,
        tenant: String = TENANT
    ): PartnerInfo {
        return PartnerInfo(
            partnerId = partnerId,
            partnerDetailId = partnerDetailId,
            partnerName = partnerName,
            tenant = tenant,
            distributorPdi = distributorPdi
        )
    }

    // DraftReceipt with DRAFT status
    fun draftReceipt(
        id: Long? = null,
        receiptNumber: String = "REC-001",
        paymentTransactionId: String? = TX_ID,
        remarks: String? = "Receipt Created from RIO Payment",
        amount: Double = 1000.0,
        status: ReceiptStatus = ReceiptStatus.DRAFT,
        source: AdvancePaymentSource = AdvancePaymentSource.RIO_PAY,
        tenant: String = TENANT,
        partnerId: Long = PARTNER_ID,
        partnerDetailId: Long = PDI,
        unUtilizedAmount: Double = 1000.0,
        salesmanId: Long? = null,
        salesmanName: String? = null,
        paymentType: PaymentType? = PaymentType.CASH,
        txReferenceNumber: String? = null,
        transactionDate: LocalDate? = null,
        bankName: String? = null,
        retailerTxnDate: LocalDate? = LocalDate.now(),
        retailerName: String? = null,
        isBankDeposit: Boolean? = false,
        bankDepositSlipNo: String? = null,
        receiptType: ReceiptType? = null,
        settleableMappings: MutableList<com.pharmeasy.data.DraftReceiptEntityMapping> = mutableListOf()
    ): DraftReceipt {
        val draftReceipt = DraftReceipt(
            receiptNumber = receiptNumber,
            paymentTransactionId = paymentTransactionId,
            remarks = remarks,
            amount = amount,
            status = status,
            source = source,
            tenant = tenant,
            partnerId = partnerId,
            partnerDetailId = partnerDetailId,
            unUtilizedAmount = unUtilizedAmount,
            salesmanId = salesmanId,
            salesmanName = salesmanName,
            paymentType = paymentType,
            txReferenceNumber = txReferenceNumber,
            transactionDate = transactionDate,
            bankName = bankName,
            retailerTxnDate = retailerTxnDate,
            retailerName = retailerName,
            isBankDeposit = isBankDeposit,
            bankDepositSlipNo = bankDepositSlipNo,
            receiptType = receiptType,
            settleableMappings = settleableMappings,
        )
        draftReceipt.id = id
        return draftReceipt
    }

    // DraftReceipt with APPROVED status
    fun approvedDraftReceipt(
        id: Long? = 1L,
        receiptNumber: String = "REC-001",
        paymentTransactionId: String? = TX_ID,
        remarks: String? = "Receipt Created from RIO Payment",
        amount: Double = 1000.0,
        source: AdvancePaymentSource = AdvancePaymentSource.RIO_PAY,
        tenant: String = TENANT,
        partnerId: Long = PARTNER_ID,
        partnerDetailId: Long = PDI,
        unUtilizedAmount: Double = 1000.0,
        salesmanId: Long? = null,
        salesmanName: String? = null,
        paymentType: PaymentType? = PaymentType.CASH,
        txReferenceNumber: String? = null,
        transactionDate: java.time.LocalDate? = null,
        bankName: String? = null,
        retailerTxnDate: java.time.LocalDate? = null,
        retailerName: String? = null,
        isBankDeposit: Boolean? = false,
        bankDepositSlipNo: String? = null,
        receiptType: ReceiptType? = null,
        settleableMappings: MutableList<com.pharmeasy.data.DraftReceiptEntityMapping> = mutableListOf()
    ): DraftReceipt {
        return draftReceipt(
            id = id,
            receiptNumber = receiptNumber,
            paymentTransactionId = paymentTransactionId,
            remarks = remarks,
            amount = amount,
            status = ReceiptStatus.APPROVED,
            source = source,
            tenant = tenant,
            partnerId = partnerId,
            partnerDetailId = partnerDetailId,
            unUtilizedAmount = unUtilizedAmount,
            salesmanId = salesmanId,
            salesmanName = salesmanName,
            paymentType = paymentType,
            txReferenceNumber = txReferenceNumber,
            transactionDate = transactionDate,
            bankName = bankName,
            retailerTxnDate = retailerTxnDate,
            retailerName = retailerName,
            isBankDeposit = isBankDeposit,
            bankDepositSlipNo = bankDepositSlipNo,
            receiptType = receiptType,
            settleableMappings = settleableMappings
        )
    }

    // Receipt object
    fun receipt(
        id: Long? = 1L,
        receiptNumber: String = "REC-001",
        createdAt: java.time.LocalDateTime = java.time.LocalDateTime.now(),
        createdBy: String = "SYSTEM",
        updatedAt: java.time.LocalDateTime? = java.time.LocalDateTime.now(),
        updatedBy: String? = "SYSTEM",
        paymentTransactionId: String? = TX_ID,
        remarks: String? = "Receipt Created from RIO Payment",
        amount: Double? = 1000.0,
        status: ReceiptStatus = ReceiptStatus.GENERATED,
        version: Int = 0,
        iteration: Int = 0,
        advanceAmount: Double? = null,
        source: AdvancePaymentSource = AdvancePaymentSource.RIO_PAY,
        advanceId: Long? = null,
        tenant: String = TENANT,
        partnerId: Long = PARTNER_ID,
        partnerDetailId: Long = PDI,
        unUtilizedAmount: Double = 1000.0,
        salesmanId: Long? = null,
        salesmanName: String? = null,
        paymentType: PaymentType? = PaymentType.CASH,
        txReferenceNumber: String? = null,
        transactionDate: java.time.LocalDate? = null,
        bankName: String? = null,
        retailerTxnDate: java.time.LocalDate? = null,
        retailerName: String? = null,
        isBankDeposit: Boolean? = false,
        bankDepositSlipNo: String? = null,
        receiptType: ReceiptType? = null,
        draftReceipt: DraftReceipt? = null
    ): Receipt {
        return Receipt(
            id = id,
            receiptNumber = receiptNumber,
            createdAt = createdAt,
            createdBy = createdBy,
            updatedAt = updatedAt,
            updatedBy = updatedBy,
            paymentTransactionId = paymentTransactionId,
            remarks = remarks,
            amount = amount,
            status = status,
            version = version,
            iteration = iteration,
            advanceAmount = advanceAmount,
            source = source,
            advanceId = advanceId,
            tenant = tenant,
            partnerId = partnerId,
            partnerDetailId = partnerDetailId,
            unUtilizedAmount = unUtilizedAmount,
            salesmanId = salesmanId,
            salesmanName = salesmanName,
            paymentType = paymentType,
            txReferenceNumber = txReferenceNumber,
            transactionDate = transactionDate,
            bankName = bankName,
            retailerTxnDate = retailerTxnDate,
            retailerName = retailerName,
            receiptType = receiptType,
            draftReceipt = draftReceipt
        )
    }

    // SettleableDetails object
    fun settleableDetails(
        id: Long = 1L,
        number: String = DN_NUMBER,
        amount: Double = 1000.0,
        pendingAmount: Double = 1000.0,
        status: InvoiceStatus = InvoiceStatus.PENDING,
        type: SettleableType = SettleableType.DEBIT_NOTE
    ): SettleableDetails {
        return SettleableDetails(
            id = id,
            number = number,
            amount = amount,
            pendingAmount = pendingAmount,
            status = status,
            type = type
        )
    }

    // DraftReceiptEntityMapping object
    fun draftReceiptEntityMapping(
        receipt: DraftReceipt? = null,
        entityType: SettleableType = SettleableType.DEBIT_NOTE,
        entityId: Long = 1L,
        entityTxAmount: Double = 1000.0,
        active: Boolean = true
    ): DraftReceiptEntityMapping {
        val mapping = DraftReceiptEntityMapping(
            receipt = receipt ?: draftReceipt(),
            entityType = entityType,
            entityId = entityId,
            entityTxAmount = entityTxAmount,
            active = active
        )
        mapping.id = 1L
        return mapping
    }

    // RetailerDebitNote object
    fun retailerDebitNote(
        id: Long? = 1L,
        createdBy: String = "SYSTEM",
        updatedBy: String = "SYSTEM",
        remarks: String? = "Test Debit Note",
        status: InvoiceStatus = InvoiceStatus.PENDING,
        type: DnType = DnType.ADHOC,
        amount: Double = 1000.0,
        amountReceived: Double = 0.0,
        taxableValue: Double = 900.0,
        taxAmount: Double = 100.0,
        interstate: Boolean? = false,
        documentNumber: String = DN_NUMBER,
        partnerDetailId: Long = PDI,
        partnerId: Long = PARTNER_ID,
        partnerName: String = PARTNER_NAME,
        qrCode: String? = null,
        irn: String? = null,
        tenant: String = TENANT,
        companyId: Long = 1L,
        useCase: UseCaseType? = UseCaseType.BOUNCE,
        isGstApplicable: Boolean? = false,
        retailerDebitItems: MutableList<RetailerDebitNoteItems> = mutableListOf(),
        refId: String? = "",
        isMigrated: Boolean? = false,
        narration: String? = null,
        assignedTo: String? = null,
        assignedToId: String? = null,
        uuid: String? = null,
        version: Int = 0
    ): RetailerDebitNote {
        val debitNote = RetailerDebitNote(
            createdBy = createdBy,
            updatedBy = updatedBy,
            remarks = remarks,
            status = status,
            type = type,
            amount = amount,
            amountReceived = amountReceived,
            taxableValue = taxableValue,
            taxAmount = taxAmount,
            interstate = interstate,
            documentNumber = documentNumber,
            partnerDetailId = partnerDetailId,
            partnerId = partnerId,
            partnerName = partnerName,
            qrCode = qrCode,
            irn = irn,
            tenant = tenant,
            companyId = companyId,
            useCase = useCase,
            isGstApplicable = isGstApplicable,
            retailerDebitItems = retailerDebitItems,
            refId = refId,
            isMigrated = isMigrated,
            narration = narration,
            assignedTo = assignedTo,
            assignedToId = assignedToId,
            uuid = uuid,
            version = version
        )
        debitNote.id = id
        return debitNote
    }

    // Settlement object
    fun settlement(
        id: Long = 1L,
        createdOn: LocalDateTime? = LocalDateTime.now(),
        updatedOn: LocalDateTime? = LocalDateTime.now(),
        createdBy: String? = "SYSTEM",
        supplierId: Long = PARTNER_ID,
        supplierName: String? = PARTNER_NAME,
        amount: Double = 1000.0,
        paidAmount: Double = 1000.0,
        remarks: String? = "Settled via RIO Payment",
        settlementNumber: String? = "SET-001",
        invoices: MutableList<BkInvoice> = mutableListOf(),
        creditNotes: MutableList<CreditNote> = mutableListOf(),
        paymentType: PaymentType = PaymentType.CASH,
        paymentReference: String? = TX_ID,
        paymentDate: LocalDate? = LocalDate.now(),
        partnerId: Long? = PARTNER_ID,
        partnerDetailId: Long? = PDI,
        type: PartnerType = PartnerType.CUSTOMER,
        tenant: String = TENANT,
        chequeDate: LocalDate? = null,
        bankId: Long? = null,
        bankName: String? = null,
        isBounced: Boolean? = false,
        reversed: Boolean? = false,
        advancePayment: MutableList<AdvancePayment> = mutableListOf(),
        chargeInvoice: MutableList<Charges> = mutableListOf(),
        charge: Boolean = false,
        receipt: List<Receipt>? = null,
        paymentSource: AdvancePaymentSource? = AdvancePaymentSource.SYSTEM,
        retailerDebitNotes: MutableList<RetailerDebitNote> = mutableListOf(),
        uuid: String = UUIDUtil.generateUuid(),
        settlementGroupNumber: String? = null
    ): Settlement {
        return Settlement(
            id = id,
            createdOn = createdOn,
            updatedOn = updatedOn,
            createdBy = createdBy,
            supplierId = supplierId,
            supplierName = supplierName,
            amount = amount,
            paidAmount = paidAmount,
            remarks = remarks,
            settlementNumber = settlementNumber,
            invoices = invoices,
            creditNotes = creditNotes,
            paymentType = paymentType,
            paymentReference = paymentReference,
            paymentDate = paymentDate,
            partnerId = partnerId,
            partnerDetailId = partnerDetailId,
            type = type,
            tenant = tenant,
            chequeDate = chequeDate,
            bankId = bankId,
            bankName = bankName,
            isBounced = isBounced,
            reversed = reversed,
            advancePayment = advancePayment,
            chargeInvoice = chargeInvoice,
            charge = charge,
            receipt = receipt,
            paymentSource = paymentSource,
            retailerDebitNotes = retailerDebitNotes,
            uuid = uuid,
            settlementGroupNumber = settlementGroupNumber
        )
    }
}
