package com.pharmeasy.service

import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.type.PaymentType
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test

class DirectPaymentProcessorTest {

    private lateinit var directPaymentProcessor: DirectPaymentProcessor
    private lateinit var draftReceiptService: DraftReceiptService
    private val partnerDetailId = 1L
    private val tenant = "th001"
    private val transactionId = "TX123456"

    @BeforeEach
    fun setUp() {
        draftReceiptService = mockk(relaxed = true)
        directPaymentProcessor = DirectPaymentProcessor(draftReceiptService)
    }

    @Test
    @DisplayName("validatePayment should always return null")
    fun testValidatePaymentAlwaysReturnsNull() {
        // Arrange
        val paymentInfo = PaymentInfo(
            customerTransactionId = transactionId,
            transactionDate = System.currentTimeMillis(),
            transactionAmount = 1000.0,
            paymentType = PaymentType.CASH,
            paymentMode = "CASH",
            initiatedBy = "USER"
        )
        
        val partnerInfo = PartnerInfo(
            partnerId = 1L,
            partnerDetailId = partnerDetailId,
            partnerName = "Test Partner",
            tenant = tenant,
            distributorPdi = 2L
        )
        
        // Act
        val result = directPaymentProcessor.validatePayment(paymentInfo, partnerInfo)
        
        // Assert
        assertNull(result)
    }
}
