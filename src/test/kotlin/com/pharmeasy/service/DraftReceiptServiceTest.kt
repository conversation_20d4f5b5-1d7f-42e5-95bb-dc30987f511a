package com.pharmeasy.service

import com.pharmeasy.dto.SettleableDetails
import com.pharmeasy.mapper.ReceiptMapper
import com.pharmeasy.model.InvoiceStatus
import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.model.ops.SettleableInfo
import com.pharmeasy.repo.DraftReceiptEntityMappingRepository
import com.pharmeasy.repo.DraftReceiptRepository
import com.pharmeasy.service.abstracts.BaseSettleableProcessor
import com.pharmeasy.type.AdvancePaymentSource
import com.pharmeasy.type.CreationType
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.ReceiptStatus
import com.pharmeasy.type.SettleableType
import com.pharmeasy.utils.DataProvider
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.spyk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertSame
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.mapstruct.factory.Mappers
import java.time.LocalDate
import java.time.LocalDateTime

class DraftReceiptServiceTest {

    private lateinit var draftReceiptService: DraftReceiptService
    private lateinit var settleableProcessorFactory: SettleableProcessorFactory
    private lateinit var draftReceiptRepo: DraftReceiptRepository
    private lateinit var settleableProcessor: BaseSettleableProcessor
    private lateinit var receiptMapper: ReceiptMapper
    private lateinit var draftReceiptEntityMappingRepository: DraftReceiptEntityMappingRepository

    @BeforeEach
    fun setUp() {
        settleableProcessorFactory = mockk(relaxed = true)
        draftReceiptRepo = mockk(relaxed = true)
        settleableProcessor = mockk(relaxed = true)
        receiptMapper = Mappers.getMapper(ReceiptMapper::class.java)
        draftReceiptEntityMappingRepository = mockk(relaxed = true)

        
        draftReceiptService = DraftReceiptService(settleableProcessorFactory, draftReceiptRepo, receiptMapper, draftReceiptEntityMappingRepository)
        
        every { settleableProcessorFactory.getSettleableProcessor(any()) } returns settleableProcessor
    }

    @Test
    @DisplayName("Service should be initialized with correct source")
    fun testServiceInitialization() {
        // Assert
        assertEquals(AdvancePaymentSource.RIO_PAY, draftReceiptService.source)
    }

    @Nested
    @DisplayName("saveDraftReceipt Tests")
    inner class SaveDraftReceiptTests {
        
        @Test
        @DisplayName("saveDraftReceipt should save and return the draft receipt")
        fun testSaveDraftReceipt() {
            // Arrange
            val draftReceipt = DataProvider.draftReceipt(
                receiptNumber = "REC-001",
                paymentTransactionId = "TX123",
                amount = 1000.0
            )
            
            every { draftReceiptRepo.save(any()) } returns draftReceipt
            
            // Act
            val result = draftReceiptService.saveDraftReceipt(draftReceipt)
            
            // Assert
            assertSame(draftReceipt, result)
            verify { draftReceiptRepo.save(draftReceipt) }
        }
    }
    
    @Nested
    @DisplayName("getReceiptByPaymentTransactionId Tests")
    inner class GetReceiptByPaymentTransactionIdTests {
        
        @Test
        @DisplayName("getReceiptByPaymentTransactionId should return receipt when found")
        fun testGetReceiptByPaymentTransactionIdFound() {
            // Arrange
            val txId = "TX123"
            val draftReceipt = DataProvider.draftReceipt(
                receiptNumber = "REC-001",
                paymentTransactionId = txId,
                amount = 1000.0
            )
            
            every { draftReceiptRepo.findByPaymentTransactionId(txId) } returns draftReceipt
            
            // Act
            val result = draftReceiptService.getReceiptByPaymentTransactionId(txId)
            
            // Assert
            assertSame(draftReceipt, result)
            verify { draftReceiptRepo.findByPaymentTransactionId(txId) }
        }
        
        @Test
        @DisplayName("getReceiptByPaymentTransactionId should return null when not found")
        fun testGetReceiptByPaymentTransactionIdNotFound() {
            // Arrange
            val txId = "TX123"
            
            every { draftReceiptRepo.findByPaymentTransactionId(txId) } returns null
            
            // Act
            val result = draftReceiptService.getReceiptByPaymentTransactionId(txId)
            
            // Assert
            assertEquals(null, result)
            verify { draftReceiptRepo.findByPaymentTransactionId(txId) }
        }
    }
    
    @Nested
    @DisplayName("findDuplicatesByPaymentTransactionId Tests")
    inner class FindDuplicatesByPaymentTransactionIdTests {
        
        @Test
        @DisplayName("findDuplicatesByPaymentTransactionId should return list of duplicates")
        fun testFindDuplicatesByPaymentTransactionId() {
            // Arrange
            val txId = "TX123"
            val partnerDetailId = 1L
            val tenant = "th001"
            val draftReceipt = DataProvider.draftReceipt(
                receiptNumber = "REC-001",
                paymentTransactionId = txId,
                amount = 1000.0
            )
            
            every { 
                draftReceiptRepo.findDuplicatesByPaymentTransactionId(txId, partnerDetailId, tenant) 
            } returns listOf(draftReceipt)
            
            // Act
            val result = draftReceiptService.findDuplicatesByPaymentTransactionId(txId, partnerDetailId, tenant)
            
            // Assert
            assertEquals(1, result.size)
            assertSame(draftReceipt, result[0])
            verify { draftReceiptRepo.findDuplicatesByPaymentTransactionId(txId, partnerDetailId, tenant) }
        }
    }
    
    @Nested
    @DisplayName("findCashTotalForPartner Tests")
    inner class FindCashTotalForPartnerTests {
        
        @Test
        @DisplayName("findCashTotalForPartner should return total cash amount")
        fun testFindCashTotalForPartner() {
            // Arrange
            val partnerDetailId = 1L
            val startDate = LocalDateTime.now().minusDays(1)
            val endDate = LocalDateTime.now()
            val totalCash = 5000.0
            
            every { 
                draftReceiptRepo.findCashTotalForPartner(partnerDetailId, startDate, endDate) 
            } returns totalCash
            
            // Act
            val result = draftReceiptService.findCashTotalForPartner(partnerDetailId, startDate, endDate)
            
            // Assert
            assertEquals(totalCash, result)
            verify { draftReceiptRepo.findCashTotalForPartner(partnerDetailId, startDate, endDate) }
        }
    }
    
    @Nested
    @DisplayName("findDuplicateCheque Tests")
    inner class FindDuplicateChequeTests {
        
        @Test
        @DisplayName("findDuplicateCheque should return list of duplicates")
        fun testFindDuplicateCheque() {
            // Arrange
            val chequeNo = "123456"
            val bankName = "test Bank"
            val chequeDate = LocalDate.now()
            val partnerDetailId = 1L
            val tenant = "th001"
            val draftReceipt = DataProvider.draftReceipt(
                receiptNumber = "REC-001",
                paymentTransactionId = "TX123",
                amount = 1000.0,
                paymentType = PaymentType.CHEQUE,
                txReferenceNumber = chequeNo,
                transactionDate = chequeDate
            )
            
            every { 
                draftReceiptRepo.findDuplicateCheque(chequeNo, chequeDate, partnerDetailId, tenant, bankName)
            } returns listOf(draftReceipt)
            
            // Act
            val result = draftReceiptService.findDuplicateCheque(
                chequeNo,
                chequeDate,
                partnerDetailId,
                tenant,
                bankName
            )
            
            // Assert
            assertEquals(1, result.size)
            assertSame(draftReceipt, result[0])
            verify { draftReceiptRepo.findDuplicateCheque(chequeNo, chequeDate, partnerDetailId, tenant, bankName) }
        }
    }
    
    @Nested
    @DisplayName("findDuplicateNeftPayments Tests")
    inner class FindDuplicateNeftPaymentsTests {
        
        @Test
        @DisplayName("findDuplicateNeftPayments should return list of duplicates")
        fun testFindDuplicateNeftPayments() {
            // Arrange
            val neftId = "NEFT123456"
            val partnerDetailId = 1L
            val tenant = "th001"
            val draftReceipt = DataProvider.draftReceipt(
                receiptNumber = "REC-001",
                paymentTransactionId = "TX123",
                amount = 1000.0,
                paymentType = PaymentType.NEFT,
                txReferenceNumber = neftId
            )
            
            every { 
                draftReceiptRepo.findDuplicateNeftPayments(neftId, partnerDetailId, tenant, any())
            } returns listOf(draftReceipt)
            
            // Act
            val result = draftReceiptService.findDuplicateNeftPayments(neftId, partnerDetailId, tenant, draftReceipt.retailerTxnDate!!)

            // Assert
            assertEquals(1, result.size)
            assertSame(draftReceipt, result[0])
            verify { draftReceiptRepo.findDuplicateNeftPayments(neftId, partnerDetailId, tenant, any()) }
        }
    }
    
    @Nested
    @DisplayName("save Tests")
    inner class SaveTests {
        
        @Test
        @DisplayName("save should save and return the receipt")
        fun testSave() {
            // Arrange
            val draftReceipt = DataProvider.draftReceipt(
                receiptNumber = "REC-001",
                paymentTransactionId = "TX123",
                amount = 1000.0
            )
            
            every { draftReceiptRepo.save(any()) } returns draftReceipt
            
            // Act
            val result = draftReceiptService.save(draftReceipt)
            
            // Assert
            assertSame(draftReceipt, result)
            verify { draftReceiptRepo.save(draftReceipt) }
        }
    }
    
    @Nested
    @DisplayName("updateSettleableMappings Tests")
    inner class UpdateSettleableMappingsTests {
        
        @Test
        @DisplayName("updateSettleableMappings should add mappings for each settleable")
        fun testUpdateSettleableMappings() {
            // Arrange
            val draftReceipt = spyk(DataProvider.draftReceipt(
                receiptNumber = "REC-001",
                paymentTransactionId = "TX123",
                amount = 1000.0,
                unUtilizedAmount = 1000.0,
                status = ReceiptStatus.DRAFT
            ))
            
            val paymentInfo = PaymentInfo(
                customerTransactionId = "TX123",
                transactionDate = System.currentTimeMillis(),
                transactionAmount = 1000.0,
                paymentType = PaymentType.CASH,
                paymentMode = "CASH",
                initiatedBy = "USER",
                settleables = listOf(
                    SettleableInfo(
                        number = "INV001",
                        type = CreationType.INVOICE,
                        amount = 500.0,
                        category = "INVOICE"
                    )
                )
            )
            
            val partnerInfo = PartnerInfo(
                partnerId = 1L,
                partnerDetailId = 1L,
                partnerName = "Test Partner",
                tenant = "th001",
                distributorPdi = 2L
            )
            
            val settleableDetails = SettleableDetails(
                id = 1L,
                number = "INV001",
                amount = 1000.0,
                pendingAmount = 800.0,
                status = InvoiceStatus.PENDING,
                type = SettleableType.INVOICE
            )
            
            every { 
                settleableProcessor.getSettleableDetails("INV001", "th001") 
            } returns settleableDetails
            
            every { draftReceipt.addSettleableMapping(any(), any()) } just runs
            
            // Act
            draftReceiptService.updateSettleableMappings(draftReceipt, paymentInfo, partnerInfo)
            
            // Assert
            verify { settleableProcessorFactory.getSettleableProcessor(SettleableType.INVOICE) }
            verify { settleableProcessor.getSettleableDetails("INV001", "th001") }
            verify { draftReceipt.addSettleableMapping(settleableDetails, 500.0) }
        }
        
        @Test
        @DisplayName("updateSettleableMappings should skip paid off settleables")
        fun testUpdateSettleableMappingsWithPaidOffSettleable() {
            // Arrange
            val draftReceipt = spyk(DataProvider.draftReceipt(
                receiptNumber = "REC-001",
                paymentTransactionId = "TX123",
                amount = 1000.0,
                unUtilizedAmount = 1000.0,
                status = ReceiptStatus.DRAFT
            ))
            
            val paymentInfo = PaymentInfo(
                customerTransactionId = "TX123",
                transactionDate = System.currentTimeMillis(),
                transactionAmount = 1000.0,
                paymentType = PaymentType.CASH,
                paymentMode = "CASH",
                initiatedBy = "USER",
                settleables = listOf(
                    SettleableInfo(
                        number = "INV001",
                        type = CreationType.INVOICE,
                        amount = 500.0,
                        category = "INVOICE"
                    )
                )
            )
            
            val partnerInfo = PartnerInfo(
                partnerId = 1L,
                partnerDetailId = 1L,
                partnerName = "Test Partner",
                tenant = "th001",
                distributorPdi = 2L
            )
            
            val settleableDetails = SettleableDetails(
                id = 1L,
                number = "INV001",
                amount = 1000.0,
                pendingAmount = 0.0,
                status = InvoiceStatus.PAID,
                type = SettleableType.INVOICE
            )
            
            every { 
                settleableProcessor.getSettleableDetails("INV001", "th001") 
            } returns settleableDetails
            
            // Act
            draftReceiptService.updateSettleableMappings(draftReceipt, paymentInfo, partnerInfo)
            
            // Assert
            verify { settleableProcessorFactory.getSettleableProcessor(SettleableType.INVOICE) }
            verify { settleableProcessor.getSettleableDetails("INV001", "th001") }
            // Verify that addSettleableMapping is not called for paid off settleables
            verify(exactly = 0) { draftReceipt.addSettleableMapping(any(), any()) }
        }
    }
}
