package com.pharmeasy.service

import com.pharmeasy.model.ops.BankDetails
import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.type.PaymentType
import com.pharmeasy.utils.DataProvider
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import java.time.LocalDate

class ChequePaymentProcessorTest {

    private lateinit var chequePaymentProcessor: ChequePaymentProcessor
    private lateinit var draftReceiptService: DraftReceiptService
    private lateinit var chequeHandlingService: ChequeHandlingService

    private val partnerDetailId = 1L
    private val tenant = "th001"
    private val transactionId = "TX123456"
    private val chequeNo = "123456"
    private val chequeDate = LocalDate.now()
    private val bankName = "Test Bank"

    @BeforeEach
    fun setUp() {
        draftReceiptService = mockk(relaxed = true)
        chequeHandlingService = mockk(relaxed = true)
        chequePaymentProcessor = ChequePaymentProcessor(draftReceiptService, chequeHandlingService)
    }

    @Test
    @DisplayName("validatePayment should return error when cheque number is missing")
    fun testValidatePaymentWithMissingChequeNumber() {
        // Arrange
        val paymentInfo = PaymentInfo(
            customerTransactionId = transactionId,
            transactionDate = System.currentTimeMillis(),
            transactionAmount = 1000.0,
            paymentType = PaymentType.CHEQUE,
            paymentMode = "CHEQUE",
            initiatedBy = "USER",
            bankDetails = BankDetails(
                chequeNo = null,
                chequeDate = chequeDate,
                bankName = bankName
            )
        )
        
        val partnerInfo = PartnerInfo(
            partnerId = 1L,
            partnerDetailId = partnerDetailId,
            partnerName = "Test Partner",
            tenant = tenant,
            distributorPdi = 2L
        )
        
        every { draftReceiptService.findDuplicatesByPaymentTransactionId(any(), any(), any()) } returns emptyList()
        
        // Act
        val result = chequePaymentProcessor.validatePayment(paymentInfo, partnerInfo)
        
        // Assert
        assertEquals("Cheque number is required for cheque payments", result)
    }

    @Test
    @DisplayName("validatePayment should return null when cheque date is missing")
    fun testValidatePaymentWithMissingChequeDate() {
        // Arrange
        val paymentInfo = PaymentInfo(
            customerTransactionId = transactionId,
            transactionDate = System.currentTimeMillis(),
            transactionAmount = 1000.0,
            paymentType = PaymentType.CHEQUE,
            paymentMode = "CHEQUE",
            initiatedBy = "USER",
            bankDetails = BankDetails(
                chequeNo = chequeNo,
                chequeDate = null,
                bankName = bankName
            )
        )
        
        val partnerInfo = PartnerInfo(
            partnerId = 1L,
            partnerDetailId = partnerDetailId,
            partnerName = "Test Partner",
            tenant = tenant,
            distributorPdi = 2L
        )
        
        every { draftReceiptService.findDuplicatesByPaymentTransactionId(any(), any(), any()) } returns emptyList()
        
        // Act
        val result = chequePaymentProcessor.validatePayment(paymentInfo, partnerInfo)
        
        // Assert
        assertNull(result)
    }

    @Test
    @DisplayName("validatePayment should return null when bank name is missing")
    fun testValidatePaymentWithMissingBankName() {
        // Arrange
        val paymentInfo = PaymentInfo(
            customerTransactionId = transactionId,
            transactionDate = System.currentTimeMillis(),
            transactionAmount = 1000.0,
            paymentType = PaymentType.CHEQUE,
            paymentMode = "CHEQUE",
            initiatedBy = "USER",
            bankDetails = BankDetails(
                chequeNo = chequeNo,
                chequeDate = chequeDate,
                bankName = null
            )
        )
        
        val partnerInfo = PartnerInfo(
            partnerId = 1L,
            partnerDetailId = partnerDetailId,
            partnerName = "Test Partner",
            tenant = tenant,
            distributorPdi = 2L
        )
        
        every { draftReceiptService.findDuplicatesByPaymentTransactionId(any(), any(), any()) } returns emptyList()
        
        // Act
        val result = chequePaymentProcessor.validatePayment(paymentInfo, partnerInfo)
        
        // Assert
        assertNull(result)
    }

    @Test
    @DisplayName("validatePayment should return null when cheque details are valid and no duplicates")
    fun testValidatePaymentWithValidChequeDetails() {
        // Arrange
        val paymentInfo = PaymentInfo(
            customerTransactionId = transactionId,
            transactionDate = System.currentTimeMillis(),
            transactionAmount = 1000.0,
            paymentType = PaymentType.CHEQUE,
            paymentMode = "CHEQUE",
            initiatedBy = "USER",
            bankDetails = BankDetails(
                chequeNo = chequeNo,
                chequeDate = chequeDate,
                bankName = bankName
            )
        )
        
        val partnerInfo = PartnerInfo(
            partnerId = 1L,
            partnerDetailId = partnerDetailId,
            partnerName = "Test Partner",
            tenant = tenant,
            distributorPdi = 2L
        )
        
        every { draftReceiptService.findDuplicatesByPaymentTransactionId(any(), any(), any()) } returns emptyList()
        every { draftReceiptService.findDuplicateCheque(chequeNo, chequeDate, partnerDetailId, tenant, bankName) } returns emptyList()
        
        // Act
        val result = chequePaymentProcessor.validatePayment(paymentInfo, partnerInfo)
        
        // Assert
        assertNull(result)
        verify { draftReceiptService.findDuplicateCheque(chequeNo, chequeDate, partnerDetailId, tenant, bankName) }
    }

    @Test
    @DisplayName("validatePayment should return error when duplicate cheque exists")
    fun testValidatePaymentWithDuplicateCheque() {
        // Arrange
        val paymentInfo = PaymentInfo(
            customerTransactionId = transactionId,
            transactionDate = System.currentTimeMillis(),
            transactionAmount = 1000.0,
            paymentType = PaymentType.CHEQUE,
            paymentMode = "CHEQUE",
            initiatedBy = "USER",
            bankDetails = BankDetails(
                chequeNo = chequeNo,
                chequeDate = chequeDate,
                bankName = bankName
            )
        )
        
        val partnerInfo = PartnerInfo(
            partnerId = 1L,
            partnerDetailId = partnerDetailId,
            partnerName = "Test Partner",
            tenant = tenant,
            distributorPdi = 2L
        )
        
        val existingReceipt = DataProvider.draftReceipt(
            id = 1L,
            receiptNumber = "REC-001",
            paymentTransactionId = "TX-EXISTING",
            amount = 1000.0,
            paymentType = PaymentType.CHEQUE,
            txReferenceNumber = chequeNo,
            transactionDate = chequeDate
        )
        
        every { draftReceiptService.findDuplicatesByPaymentTransactionId(any(), any(), any()) } returns emptyList()
        every { draftReceiptService.findDuplicateCheque(chequeNo, chequeDate, partnerDetailId, tenant, bankName) } returns listOf(existingReceipt)
        
        // Act
        val result = chequePaymentProcessor.validatePayment(paymentInfo, partnerInfo)
        
        // Assert
        assertEquals("Cheque number $chequeNo already exists for the given date in receipt: REC-001", result)
        verify { draftReceiptService.findDuplicateCheque(chequeNo, chequeDate, partnerDetailId, tenant, bankName) }
    }

    @Test
    @DisplayName("validatePayment should return parent validation error if present")
    fun testValidatePaymentWithParentValidationError() {
        // Arrange
        val paymentInfo = PaymentInfo(
            customerTransactionId = transactionId,
            transactionDate = System.currentTimeMillis(),
            transactionAmount = 1000.0,
            paymentType = PaymentType.CHEQUE,
            paymentMode = "CHEQUE",
            initiatedBy = "USER",
            bankDetails = BankDetails(
                chequeNo = chequeNo,
                chequeDate = chequeDate,
                bankName = bankName
            )
        )
        
        val partnerInfo = PartnerInfo(
            partnerId = 1L,
            partnerDetailId = partnerDetailId,
            partnerName = "Test Partner",
            tenant = tenant,
            distributorPdi = 2L
        )
        
        val existingReceipt = DataProvider.draftReceipt(
            id = 1L,
            receiptNumber = "REC-001",
            paymentTransactionId = transactionId,
            amount = 1000.0
        )
        
        every { draftReceiptService.findDuplicatesByPaymentTransactionId(transactionId, partnerDetailId, tenant) } returns listOf(existingReceipt)
        
        // Act
        val result = chequePaymentProcessor.validatePayment(paymentInfo, partnerInfo)
        
        // Assert
        assertEquals("Transaction ID $transactionId already exists for the given partner in receipt: REC-001", result)
        verify { draftReceiptService.findDuplicatesByPaymentTransactionId(transactionId, partnerDetailId, tenant) }
    }
}
