package com.pharmeasy.service

import com.pharmeasy.data.RetailerDebitNoteSettlementMapping
import com.pharmeasy.data.VendorLedger
import com.pharmeasy.model.InvoiceStatus
import com.pharmeasy.model.VendorLedgerDto
import com.pharmeasy.repo.VendorLedgerRepo
import com.pharmeasy.type.LedgerEntryType
import com.pharmeasy.type.PaymentType
import com.pharmeasy.utils.DataProvider
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertSame
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.springframework.test.util.ReflectionTestUtils
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

class PartnerServiceTest {

    private lateinit var partnerService: PartnerService
    private lateinit var receiptService: ReceiptService
    private lateinit var invoiceService: InvoiceService
    private lateinit var retailerDebitNoteService: RetailerDebitNoteService
    private lateinit var retailerDebitNoteSettlementMappingService: RetailerDebitNoteSettlementMappingService
    private lateinit var vendorLedgerRepo: VendorLedgerRepo

    @BeforeEach
    fun setUp() {
        // Create mocks
        receiptService = mockk(relaxed = true)
        invoiceService = mockk(relaxed = true)
        retailerDebitNoteService = mockk(relaxed = true)
        retailerDebitNoteSettlementMappingService = mockk(relaxed = true)
        vendorLedgerRepo = mockk(relaxed = true)

        // Create service with constructor parameters
        partnerService = PartnerService(
            ledgerTemplate = "test-template",
            roundOffLimit = BigDecimal.valueOf(0.5)
        )

        // Inject mocked dependencies
        ReflectionTestUtils.setField(partnerService, "receiptService", receiptService)
        ReflectionTestUtils.setField(partnerService, "invoiceService", invoiceService)
        ReflectionTestUtils.setField(partnerService, "retailerDebitNoteService", retailerDebitNoteService)
        ReflectionTestUtils.setField(partnerService, "retailerDebitNoteSettlementMappingService", retailerDebitNoteSettlementMappingService)
        ReflectionTestUtils.setField(partnerService, "vendorLedgerRepo", vendorLedgerRepo)
    }

    @Nested
    @DisplayName("getPaymentDetails Tests")
    inner class GetPaymentDetailsTests {

        @Test
        @DisplayName("getPaymentDetails should return ledger payments for retailer debit notes")
        fun testGetPaymentDetailsWithRetailerDebitNotes() {
            // Arrange
            val settlementNumber = "SET-001"
            val settlementId = 1L
            val createdOn = LocalDateTime.now()
            val createdBy = "test-user"
            val paymentDate = LocalDate.now()
            val paymentReference = "REF-001"
            val paymentType = PaymentType.CASH

            val settlement = DataProvider.settlement(
                id = settlementId,
                createdOn = createdOn,
                createdBy = createdBy,
                paymentDate = paymentDate,
                paymentReference = paymentReference,
                paymentType = paymentType,
                settlementNumber = settlementNumber
            )

            val debitNoteId = 1L
            val documentNumber = "DN-001"
            val amount = 1000.0

            val retailerDebitNote = DataProvider.retailerDebitNote(
                id = debitNoteId,
                documentNumber = documentNumber,
                amount = amount,
                status = InvoiceStatus.PAID
            )

            val paidAmount = 1000.0

            val retailerDebitNoteSettlementMapping = mockk<RetailerDebitNoteSettlementMapping> {
                every { <EMAIL> } returns paidAmount
            }

            every { receiptService.getAllSettlementsFromReceipt(settlementNumber) } returns listOf(settlement)
            every { invoiceService.getInvoicesBySettlementId(settlementId) } returns emptyList()
            every { retailerDebitNoteService.getRetailerDebitNoteBySettlementId(settlementId) } returns listOf(retailerDebitNote)
            every { 
                retailerDebitNoteSettlementMappingService.getRetailerDebitNoteSettlementMapping(
                    debitNoteId, 
                    settlementId
                ) 
            } returns retailerDebitNoteSettlementMapping

            // Act
            val result = partnerService.getPaymentDetails(settlementNumber)

            // Assert
            assertEquals(1, result.size)

            val ledgerPayment = result[0]
            assertEquals(createdOn, ledgerPayment.createdOn)
            assertEquals(createdBy, ledgerPayment.createdBy)
            assertEquals(settlementId, ledgerPayment.settlementId)
            assertEquals(documentNumber, ledgerPayment.invoiceNumber)
            assertEquals(paymentType, ledgerPayment.mode)
            assertEquals(paymentDate, ledgerPayment.paymentDate)
            assertEquals(paymentReference, ledgerPayment.referenceNumber)
            assertEquals(BigDecimal(amount), ledgerPayment.invoiceAmount)
            assertEquals(BigDecimal(paidAmount), ledgerPayment.paidAmount)
            assertEquals(InvoiceStatus.PAID, ledgerPayment.status)
            assertEquals(settlementNumber, ledgerPayment.settlementNumber)

            verify { receiptService.getAllSettlementsFromReceipt(settlementNumber) }
            verify { invoiceService.getInvoicesBySettlementId(settlementId) }
            verify { retailerDebitNoteService.getRetailerDebitNoteBySettlementId(settlementId) }
            verify { 
                retailerDebitNoteSettlementMappingService.getRetailerDebitNoteSettlementMapping(
                    debitNoteId, 
                    settlementId
                ) 
            }
        }
    }

    @Nested
    @DisplayName("checkAndAddLedgerEntry Tests")
    inner class CheckAndAddLedgerEntryTests {

        @Test
        @DisplayName("checkAndAddLedgerEntry should return existing entry when duplicate is found")
        fun testCheckAndAddLedgerEntryWithDuplicate() {
            // Arrange
            val user = "test-user"
            val documentNumber = "DOC-001"
            val referenceNumber = "REF-001"
            val ledgerEntryType = LedgerEntryType.CREDIT

            val vendorLedgerDto = mockk<VendorLedgerDto> {
                every { <EMAIL> } returns documentNumber
                every { <EMAIL> } returns referenceNumber
                every { <EMAIL> } returns ledgerEntryType
            }

            val existingLedger = mockk<VendorLedger>()

            every { 
                vendorLedgerRepo.findDuplicateLedger(
                    documentNumber,
                    referenceNumber,
                    ledgerEntryType
                ) 
            } returns existingLedger

            // Act
            val result = partnerService.checkAndAddLedgerEntry(user, vendorLedgerDto)

            // Assert
            assertSame(existingLedger, result)
            verify { 
                vendorLedgerRepo.findDuplicateLedger(
                    documentNumber,
                    referenceNumber,
                    ledgerEntryType
                ) 
            }
        }

        @Test
        @DisplayName("checkAndAddLedgerEntry should create new entry when no duplicate is found")
        fun testCheckAndAddLedgerEntryWithoutDuplicate() {
            // Arrange
            val user = "test-user"
            val documentNumber = "DOC-001"
            val referenceNumber = "REF-001"
            val ledgerEntryType = LedgerEntryType.CREDIT

            val vendorLedgerDto = mockk<VendorLedgerDto> {
                every { <EMAIL> } returns documentNumber
                every { <EMAIL> } returns referenceNumber
                every { <EMAIL> } returns ledgerEntryType
            }

            val newLedger = mockk<VendorLedger>()

            every { 
                vendorLedgerRepo.findDuplicateLedger(
                    documentNumber,
                    referenceNumber,
                    ledgerEntryType
                ) 
            } returns null

            // Create a spy of the partnerService to mock the addVendorLedgerEntry method
            val spyPartnerService = spyk(partnerService)
            every { spyPartnerService.addVendorLedgerEntry(user, vendorLedgerDto) } returns newLedger

            // Act
            val result = spyPartnerService.checkAndAddLedgerEntry(user, vendorLedgerDto)

            // Assert
            assertSame(newLedger, result)
            verify { 
                vendorLedgerRepo.findDuplicateLedger(
                    documentNumber,
                    referenceNumber,
                    ledgerEntryType
                ) 
            }
            verify { spyPartnerService.addVendorLedgerEntry(user, vendorLedgerDto) }
        }
    }
}
