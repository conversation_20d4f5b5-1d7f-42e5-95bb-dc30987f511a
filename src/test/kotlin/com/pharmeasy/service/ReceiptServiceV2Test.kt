package com.pharmeasy.service

import com.pharmeasy.data.DraftReceiptEntityMapping
import com.pharmeasy.data.Receipt
import com.pharmeasy.data.ReceiptSettlement
import com.pharmeasy.model.ops.SettlementGroupNumber
import com.pharmeasy.repo.ReceiptRepo
import com.pharmeasy.repo.ReceiptSettlementRepo
import com.pharmeasy.type.ReceiptStatus
import com.pharmeasy.utils.DataProvider
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class ReceiptServiceV2Test {

    private lateinit var receiptService: ReceiptServiceV2
    private lateinit var receiptRepo: ReceiptRepo
    private lateinit var settlementService: SettlementService
    private lateinit var receiptSettlementRepo: ReceiptSettlementRepo

    companion object {
        const val RECEIPT_NUMBER = "REC-001"
        const val TENANT = DataProvider.TENANT
        const val PARTNER_ID = DataProvider.PARTNER_ID
        const val PARTNER_DETAIL_ID = DataProvider.PDI
        const val PAYMENT_TRANSACTION_ID = DataProvider.TX_ID
        const val AMOUNT = 1000.0
        const val USER = "test-user"
        const val SETTLEMENT_ID = 1L
    }

    private lateinit var mockReceipt: Receipt
    private lateinit var mockDraftReceipt: DraftReceiptEntityMapping
    private lateinit var mockSettlementGroupNumber: SettlementGroupNumber

    @BeforeEach
    fun setUp() {
        // Initialize all mocks
        receiptRepo = mockk(relaxed = true)
        settlementService = mockk(relaxed = true)
        receiptSettlementRepo = mockk(relaxed = true)

        // Create the service with mocked dependencies
        receiptService = ReceiptServiceV2(
            receiptRepo,
            settlementService,
            receiptSettlementRepo
        )

        // Create mock objects
        mockReceipt = DataProvider.receipt(
            id = 1L,
            receiptNumber = RECEIPT_NUMBER,
            paymentTransactionId = PAYMENT_TRANSACTION_ID,
            amount = AMOUNT,
            status = ReceiptStatus.GENERATED,
            updatedBy = USER
        )

        mockDraftReceipt = mockk(relaxed = true) {
            every { entityId } returns 1L
            every { entityTxAmount } returns AMOUNT
        }

        mockSettlementGroupNumber = SettlementGroupNumber("SGN-001")

        // Default mock behaviors
        every { receiptRepo.getReceiptByDraftId(any()) } returns null
        every { receiptRepo.save(any()) } answers { 
            firstArg()
        }
        every { settlementService.createSettlementFromReceipt(any(), any(), any()) } answers {
            mockk {
                every { id } returns SETTLEMENT_ID
            }
        }
        every { receiptSettlementRepo.save(any()) } answers { firstArg() }
    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    @Nested
    @DisplayName("getReceiptByDraftId Tests")
    inner class GetReceiptByDraftIdTests {

        @Test
        @DisplayName("Should return null when no receipt is found")
        fun testGetReceiptByDraftIdReturnsNull() {
            // Arrange
            every { receiptRepo.getReceiptByDraftId(any()) } returns null

            // Act
            val result = receiptService.getReceiptByDraftId(1L)

            // Assert
            assertNull(result)
            verify { receiptRepo.getReceiptByDraftId(1L) }
        }

        @Test
        @DisplayName("Should return receipt when found")
        fun testGetReceiptByDraftIdReturnsReceipt() {
            // Arrange
            every { receiptRepo.getReceiptByDraftId(1L) } returns mockReceipt

            // Act
            val result = receiptService.getReceiptByDraftId(1L)

            // Assert
            assertNotNull(result)
            assertEquals(mockReceipt, result)
            verify { receiptRepo.getReceiptByDraftId(1L) }
        }
    }

    @Nested
    @DisplayName("saveReceipt Tests")
    inner class SaveReceiptTests {

        @Test
        @DisplayName("Should save receipt")
        fun testSaveReceipt() {
            // Arrange
            val receiptSlot = slot<Receipt>()
            every { receiptRepo.save(capture(receiptSlot)) } answers { receiptSlot.captured }

            // Act
            val result = receiptService.saveReceipt(mockReceipt)

            // Assert
            assertNotNull(result)
            assertEquals(mockReceipt, result)
            verify { receiptRepo.save(mockReceipt) }
        }
    }

    @Nested
    @DisplayName("addSettlement Tests")
    inner class AddSettlementTests {

        @Test
        @DisplayName("Should add settlement to receipt")
        fun testAddSettlement() {
            // Arrange
            val settlementSlot = slot<ReceiptSettlement>()
            every { receiptSettlementRepo.save(capture(settlementSlot)) } answers { settlementSlot.captured }

            // Act
            receiptService.addSettlement(mockReceipt, mockDraftReceipt, mockSettlementGroupNumber)

            // Assert
            verify { settlementService.createSettlementFromReceipt(mockReceipt, mockDraftReceipt, mockSettlementGroupNumber) }
            verify { receiptSettlementRepo.save(match { 
                it.id.receiptId == mockReceipt.id && it.id.settlementId == SETTLEMENT_ID && it.createdBy == USER
            }) }
        }

    }
}
