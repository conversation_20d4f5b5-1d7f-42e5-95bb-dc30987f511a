package com.pharmeasy.service

import com.pharmeasy.data.InvoiceSettlement
import com.pharmeasy.data.InvoiceSettlementId
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.InvoiceStatus
import com.pharmeasy.repo.BkInvoiceRepo
import com.pharmeasy.repo.InvoiceSettlementRepo
import com.pharmeasy.specification.InvoiceMultiGetSpecification
import com.pharmeasy.utils.DataProvider
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertSame
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.test.util.ReflectionTestUtils
import java.math.BigDecimal
import java.time.LocalDateTime

class InvoiceServiceTest {

    private lateinit var invoiceService: InvoiceService
    private lateinit var bkInvoiceRepo: BkInvoiceRepo
    private lateinit var invoiceSettlementRepo: InvoiceSettlementRepo

    @BeforeEach
    fun setUp() {
        // Create mocks
        bkInvoiceRepo = mockk(relaxed = true)
        invoiceSettlementRepo = mockk(relaxed = true)

        // Create service with constructor parameters
        invoiceService = InvoiceService(
            retailer = 1,
            retailerAlpha = 2,
            retailerInClinic = 3,
            retailerFOFO = 4,
            retailerCOCO = 5,
            retailerHospitals = 6,
            hospitals = 7,
            retailerPL = 8,
            cnf = 9,
            distributor = 10,
            holding = 11,
            manufacturer = 12,
            retailerMarketPlace = 13,
            retailIoVersion = "1.0",
            retailIoSource = "test-source",
            retailIoKey = "test-key"
        )

        // Inject mocked dependencies
        ReflectionTestUtils.setField(invoiceService, "bkInvoiceRepo", bkInvoiceRepo)
        ReflectionTestUtils.setField(invoiceService, "invoiceSettlementRepo", invoiceSettlementRepo)
    }

    @Nested
    @DisplayName("findInvoicesByNumberAndTenant Tests")
    inner class FindInvoicesByNumberAndTenantTests {

        @Test
        @DisplayName("findInvoicesByNumberAndTenant should return invoices when found")
        fun testFindInvoicesByNumberAndTenant() {
            // Arrange
            val invoiceNumberTenantList = listOf(
                Pair(DataProvider.INVOICE_NUMBER, DataProvider.TENANT)
            )

            val invoice = DataProvider.bkInvoice(
                invoiceNum = DataProvider.INVOICE_NUMBER,
                tenant = DataProvider.TENANT
            )

            val specificationSlot = slot<InvoiceMultiGetSpecification>()

            every { bkInvoiceRepo.findAll(capture(specificationSlot)) } returns listOf(invoice)

            // Act
            val result = invoiceService.findInvoicesByNumberAndTenant(invoiceNumberTenantList)

            // Assert
            assertEquals(1, result.size)
            assertSame(invoice, result[0])
            assertEquals(invoiceNumberTenantList, specificationSlot.captured.invoiceTenantPairs)
            verify { bkInvoiceRepo.findAll(any<InvoiceMultiGetSpecification>()) }
        }
    }

    @Nested
    @DisplayName("updateInvoiceForSettlement Tests")
    inner class UpdateInvoiceForSettlementTests {

        @Test
        @DisplayName("updateInvoiceForSettlement should update invoice and create settlement mapping")
        fun testUpdateInvoiceForSettlement() {
            // Arrange
            val invoiceId = 1L
            val settlementId = 1L
            val amount = 1000.0
            val paidAmount = 500.0
            val amountBeingPaid = 500.0

            val bkInvoice = DataProvider.bkInvoice(
                id = invoiceId,
                amount = amount,
                paidAmount = 0.0
            ).apply {
                status = InvoiceStatus.PENDING
            }

            val settlementInvoice = DataProvider.bkInvoice(
                id = invoiceId,
                amount = amount,
                paidAmount = paidAmount
            ).apply {
                status = InvoiceStatus.PARTIAL_PAID
            }

            val settlement = DataProvider.settlement(
                id = settlementId,
                settlementNumber = "SET-001",
                invoices = mutableListOf(settlementInvoice)
            )

            val draftReceiptEntityMapping = DataProvider.draftReceiptEntityMapping(
                entityTxAmount = amountBeingPaid
            )

            val savedInvoiceSettlement = mockk<InvoiceSettlement>()

            val invoiceSettlementSlot = slot<InvoiceSettlement>()

            every { bkInvoiceRepo.save(any()) } returns bkInvoice
            every { invoiceSettlementRepo.save(capture(invoiceSettlementSlot)) } returns savedInvoiceSettlement

            // Act
            val result = invoiceService.updateInvoiceForSettlement(bkInvoice, draftReceiptEntityMapping, settlement)

            // Assert
            assertEquals(bkInvoice, result.first)
            assertEquals(savedInvoiceSettlement, result.second)
            assertEquals(paidAmount, bkInvoice.paidAmount)
            assertEquals(InvoiceStatus.PARTIAL_PAID, bkInvoice.status)
            assertEquals(settlementId, bkInvoice.settlementId)
            assertEquals("SET-001", bkInvoice.settlementNumber)
            assertNotNull(bkInvoice.settledOn)

            // Verify the invoice settlement was created correctly
            assertEquals(InvoiceSettlementId(invoiceId, settlementId), invoiceSettlementSlot.captured.id)
            // Amount is max(amountBeingPaid, outstandingAmount), and outstandingAmount is (amount - paidAmount)
            val outstandingAmount = amount - 0.0 // Initial paidAmount is 0.0
            assertEquals(BigDecimal(Math.max(amountBeingPaid, outstandingAmount)), invoiceSettlementSlot.captured.amount)
            assertEquals(BigDecimal(amountBeingPaid), invoiceSettlementSlot.captured.paidAmount)
            assertEquals(InvoiceStatus.PARTIAL_PAID, invoiceSettlementSlot.captured.invoiceStatus)

            verify { bkInvoiceRepo.save(bkInvoice) }
            verify { invoiceSettlementRepo.save(any()) }
        }

        @Test
        @DisplayName("updateInvoiceForSettlement should set status to PAID when remaining amount is within tolerance")
        fun testUpdateInvoiceForSettlementWithFullPayment() {
            // Arrange
            val invoiceId = 1L
            val settlementId = 1L
            val amount = 1000.0
            val paidAmount = 1000.0
            val amountBeingPaid = 1000.0

            val bkInvoice = DataProvider.bkInvoice(
                id = invoiceId,
                amount = amount,
                paidAmount = 0.0
            ).apply {
                status = InvoiceStatus.PENDING
            }

            val settlementInvoice = DataProvider.bkInvoice(
                id = invoiceId,
                amount = amount,
                paidAmount = paidAmount
            ).apply {
                status = InvoiceStatus.PAID
            }

            val settlement = DataProvider.settlement(
                id = settlementId,
                settlementNumber = "SET-001",
                invoices = mutableListOf(settlementInvoice)
            )

            val draftReceiptEntityMapping = DataProvider.draftReceiptEntityMapping(
                entityTxAmount = amountBeingPaid
            )

            val savedInvoiceSettlement = mockk<InvoiceSettlement>()

            every { bkInvoiceRepo.save(any()) } returns bkInvoice
            every { invoiceSettlementRepo.save(any()) } returns savedInvoiceSettlement

            // Act
            val result = invoiceService.updateInvoiceForSettlement(bkInvoice, draftReceiptEntityMapping, settlement)

            // Assert
            assertEquals(bkInvoice, result.first)
            assertEquals(savedInvoiceSettlement, result.second)
            assertEquals(paidAmount, bkInvoice.paidAmount)
            assertEquals(InvoiceStatus.PAID, bkInvoice.status)

            verify { bkInvoiceRepo.save(bkInvoice) }
            verify { invoiceSettlementRepo.save(any()) }
        }

        @Test
        @DisplayName("updateInvoiceForSettlement should throw exception when invoice is in terminal state")
        fun testUpdateInvoiceForSettlementWithTerminalState() {
            // Arrange
            val invoiceId = 1L
            val settlementId = 1L

            val bkInvoice = DataProvider.bkInvoice(
                id = invoiceId
            ).apply {
                status = InvoiceStatus.PAID
            }

            val settlementInvoice = DataProvider.bkInvoice(
                id = invoiceId
            ).apply {
                status = InvoiceStatus.PAID
            }

            val settlement = DataProvider.settlement(
                id = settlementId,
                invoices = mutableListOf(settlementInvoice)
            )

            val draftReceiptEntityMapping = DataProvider.draftReceiptEntityMapping()

            // Act & Assert
            val exception = assertThrows<RequestException> {
                invoiceService.updateInvoiceForSettlement(bkInvoice, draftReceiptEntityMapping, settlement)
            }

            assertEquals("Trying to change the invoice in a terminal state - ${InvoiceStatus.PAID}", exception.message)
        }
    }

    @Nested
    @DisplayName("calculateAllowableRoundOffDifference Tests")
    inner class CalculateAllowableRoundOffDifferenceTests {

        @Test
        @DisplayName("calculateAllowableRoundOffDifference should return default tolerance for whole number amounts")
        fun testCalculateAllowableRoundOffDifferenceWithWholeNumberAmount() {
            // Arrange
            val invoice = DataProvider.bkInvoice(
                amount = 1000.0,
                paidAmount = 1000.0
            )

            // Act
            val result = ReflectionTestUtils.invokeMethod<Double>(
                invoiceService,
                "calculateAllowableRoundOffDifference",
                invoice
            )

            // Assert
            assertEquals(0.01, result)
        }

        @Test
        @DisplayName("calculateAllowableRoundOffDifference should return higher tolerance for decimal amounts with payment within 1 unit")
        fun testCalculateAllowableRoundOffDifferenceWithDecimalAmountAndPaymentWithinOneUnit() {
            // Arrange
            val invoice = DataProvider.bkInvoice(
                amount = 1000.50,
                paidAmount = 1000.0
            )

            // Act
            val result = ReflectionTestUtils.invokeMethod<Double>(
                invoiceService,
                "calculateAllowableRoundOffDifference",
                invoice
            )

            // Assert
            assertEquals(0.99, result)
        }

        @Test
        @DisplayName("calculateAllowableRoundOffDifference should return default tolerance for decimal amounts with payment outside 1 unit")
        fun testCalculateAllowableRoundOffDifferenceWithDecimalAmountAndPaymentOutsideOneUnit() {
            // Arrange
            val invoice = DataProvider.bkInvoice(
                amount = 1000.50,
                paidAmount = 999.0
            )

            // Act
            val result = ReflectionTestUtils.invokeMethod<Double>(
                invoiceService,
                "calculateAllowableRoundOffDifference",
                invoice
            )

            // Assert
            assertEquals(0.01, result)
        }
    }

    // Helper method to assert that a LocalDateTime is not null
    private fun assertNotNull(dateTime: LocalDateTime?) {
        assertEquals(true, dateTime != null)
    }
}
