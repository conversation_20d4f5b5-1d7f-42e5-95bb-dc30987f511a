package com.pharmeasy.service

import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.type.PaymentType
import com.pharmeasy.utils.DataProvider
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import java.time.LocalDate

class CashPaymentProcessorTest {

    private lateinit var cashPaymentProcessor: CashPaymentProcessor
    private lateinit var draftReceiptService: DraftReceiptService
    private val cashThreshold = 200000.0
    private val partnerDetailId = 1L
    private val tenant = "th001"
    private val transactionId = "TX123456"

    @BeforeEach
    fun setUp() {
        draftReceiptService = mockk(relaxed = true)
        cashPaymentProcessor = CashPaymentProcessor(draftReceiptService, cashThreshold)
    }

    @Test
    @DisplayName("validatePayment should return null when cash payment is within threshold")
    fun testValidatePaymentWithinThreshold() {
        // Arrange
        val paymentInfo = PaymentInfo(
            customerTransactionId = transactionId,
            transactionDate = System.currentTimeMillis(),
            transactionAmount = 1000.0,
            paymentType = PaymentType.CASH,
            paymentMode = "DIGITAL_RECEIPT",
            initiatedBy = "USER"
        )
        
        val partnerInfo = PartnerInfo(
            partnerId = 1L,
            partnerDetailId = partnerDetailId,
            partnerName = "Test Partner",
            tenant = tenant,
            distributorPdi = 2L
        )
        
        val startDate = LocalDate.now().atStartOfDay()
        val endDate = LocalDate.now().plusDays(1).atStartOfDay()
        
        every { draftReceiptService.findDuplicatesByPaymentTransactionId(any(), any(), any()) } returns emptyList()

        // Act
        val result = cashPaymentProcessor.validatePayment(paymentInfo, partnerInfo)
        
        // Assert
        assertNull(result)
    }

    @Test
    @DisplayName("validatePayment should return parent validation error if present")
    fun testValidatePaymentWithParentValidationError() {
        // Arrange
        val paymentInfo = PaymentInfo(
            customerTransactionId = transactionId,
            transactionDate = System.currentTimeMillis(),
            transactionAmount = 1000.0,
            paymentType = PaymentType.CASH,
            paymentMode = "DIGITAL_RECEIPT",
            initiatedBy = "USER"
        )
        
        val partnerInfo = PartnerInfo(
            partnerId = 1L,
            partnerDetailId = partnerDetailId,
            partnerName = "Test Partner",
            tenant = tenant,
            distributorPdi = 2L
        )
        
        val existingReceipt = DataProvider.draftReceipt(
            id = 1L,
            receiptNumber = "REC-001",
            paymentTransactionId = transactionId,
            amount = 1000.0
        )
        
        every { draftReceiptService.findDuplicatesByPaymentTransactionId(transactionId, partnerDetailId, tenant) } returns listOf(existingReceipt)
        
        // Act
        val result = cashPaymentProcessor.validatePayment(paymentInfo, partnerInfo)
        
        // Assert
        assertEquals("Transaction ID $transactionId already exists for the given partner in receipt: REC-001", result)
        verify { draftReceiptService.findDuplicatesByPaymentTransactionId(transactionId, partnerDetailId, tenant) }
    }
}
