package com.pharmeasy.service

import com.pharmeasy.data.AdvancePayment
import com.pharmeasy.data.CheckerDetails
import com.pharmeasy.data.Company
import com.pharmeasy.data.CompanyTenantMapping
import com.pharmeasy.data.Receipt
import com.pharmeasy.data.VendorDataEnum
import com.pharmeasy.data.VendorDataLinks
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.Supplier
import com.pharmeasy.model.VendorLedgerDto
import com.pharmeasy.model.advancepayment.AdvancePaymentDto
import com.pharmeasy.model.advancepayment.AggregatedAdvancePaymentDto
import com.pharmeasy.model.advancepayment.PaymentRefItem
import com.pharmeasy.model.advancepayment.UpdateAdvancePaymentDto
import com.pharmeasy.model.advancepayment.VendorAdvancePaymentDto
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.repo.advancepayment.AdvancePayLinksDataRepo
import com.pharmeasy.repo.advancepayment.AdvancePaymentRepo
import com.pharmeasy.type.AdvancePaymentSource
import com.pharmeasy.type.AdvanceType
import com.pharmeasy.type.CheckerType
import com.pharmeasy.type.InvoiceType
import com.pharmeasy.type.PartnerType
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.ReceiptStatus
import com.pharmeasy.type.Role
import com.pharmeasy.type.Status
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.spyk
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.dao.PessimisticLockingFailureException
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.jpa.domain.Specification
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

class AdvancePaymentServiceTest {

    private lateinit var advancePaymentRepo: AdvancePaymentRepo
    private lateinit var supplierProxy: SupplierProxy
    private lateinit var adjustmentService: AdjustmentService
    private lateinit var documentMasterService: DocumentMasterService
    private lateinit var vendorFileService: VendorFileService
    private lateinit var s3FileUtilityService: S3FileUtilityService
    private lateinit var advancePayLinksDataRepo: AdvancePayLinksDataRepo
    private lateinit var checkerService: CheckerService
    private lateinit var companyService: CompanyService
    private lateinit var chequeHandleService: ChequeHandlingService
    private lateinit var receiptService: ReceiptService
    private lateinit var partnerService: PartnerService
    private lateinit var vaultCheckerService: CheckerService

    private lateinit var advancePaymentService: AdvancePaymentService

    // Sample test data
    private lateinit var sampleCompanyTenantMapping: CompanyTenantMapping
    private lateinit var sampleAdvancePayment: AdvancePayment
    private lateinit var sampleAdvancePaymentDto: AdvancePaymentDto
    private lateinit var sampleUpdateAdvancePaymentDto: UpdateAdvancePaymentDto
    private lateinit var sampleCheckerDetails: CheckerDetails
    private lateinit var sampleCompany: Company
    private lateinit var sampleReceipt: Receipt

    @BeforeEach
    fun setUp() {
        // Initialize mocks
        advancePaymentRepo = mockk(relaxed = true)
        supplierProxy = mockk(relaxed = true)
        adjustmentService = mockk(relaxed = true)
        documentMasterService = mockk(relaxed = true)
        vendorFileService = mockk(relaxed = true)
        s3FileUtilityService = mockk(relaxed = true)
        advancePayLinksDataRepo = mockk(relaxed = true)
        checkerService = mockk(relaxed = true)
        companyService = mockk(relaxed = true)
        chequeHandleService = mockk(relaxed = true)
        receiptService = mockk(relaxed = true)
        partnerService = mockk(relaxed = true)
        vaultCheckerService = mockk(relaxed = true)

        // Create service instance with constructor parameter
        advancePaymentService = AdvancePaymentService("http://test-url")
        advancePaymentService = spyk(advancePaymentService)

        // Manually inject dependencies using reflection
        val serviceClass = AdvancePaymentService::class.java
        serviceClass.getDeclaredField("advancePaymentRepo").apply { isAccessible = true; set(advancePaymentService, advancePaymentRepo) }
        serviceClass.getDeclaredField("supplierProxy").apply { isAccessible = true; set(advancePaymentService, supplierProxy) }
        serviceClass.getDeclaredField("adjustmentService").apply { isAccessible = true; set(advancePaymentService, adjustmentService) }
        serviceClass.getDeclaredField("documentMasterService").apply { isAccessible = true; set(advancePaymentService, documentMasterService) }
        serviceClass.getDeclaredField("vendorFileService").apply { isAccessible = true; set(advancePaymentService, vendorFileService) }
        serviceClass.getDeclaredField("s3FileUtilityService").apply { isAccessible = true; set(advancePaymentService, s3FileUtilityService) }
        serviceClass.getDeclaredField("advancePayLinksDataRepo").apply { isAccessible = true; set(advancePaymentService, advancePayLinksDataRepo) }
        serviceClass.getDeclaredField("checkerService").apply { isAccessible = true; set(advancePaymentService, checkerService) }
        serviceClass.getDeclaredField("companyService").apply { isAccessible = true; set(advancePaymentService, companyService) }
        serviceClass.getDeclaredField("chequeHandleService").apply { isAccessible = true; set(advancePaymentService, chequeHandleService) }
        serviceClass.getDeclaredField("receiptService").apply { isAccessible = true; set(advancePaymentService, receiptService) }
        serviceClass.getDeclaredField("partnerService").apply { isAccessible = true; set(advancePaymentService, partnerService) }
        serviceClass.getDeclaredField("vaultCheckerService").apply { isAccessible = true; set(advancePaymentService, vaultCheckerService) }

        // Initialize test data
        sampleCompanyTenantMapping = CompanyTenantMapping(1L, 1L, "test_tenant", "Test Tenant", 123L, 456L)

        sampleCheckerDetails = CheckerDetails(
            id = 1L, companyId = 1L, userName = "checker_user", userId = "checker123",
            type = CheckerType.CHECKER, email = "<EMAIL>", role = Role.APPROVER, isActive = true, priority = 0
        )

        sampleCompany = Company(
            id = 1L, companyCode = "TEST", name = "Test Company", darkStore = false, updatedBy = "user",
            cashBalance = BigDecimal.ZERO, bankBalance = BigDecimal.ZERO, enableRioAutoCn = false,
            isActive = true, rioEnabled = 1, isAutomail = false, bankSlipTemplateName = "template",
            dummyAccountPdi = 1L, isRetailerDnEnabled = false, isCnPrintEnabled = false, isFullCnSettlementEnabled = false
        )

        sampleAdvancePayment = AdvancePayment(
            id = 1L, createdOn = LocalDateTime.now(), updatedOn = LocalDateTime.now(),
            createdBy = "user123", createdByName = "Test User", updatedBy = "user123",
            userEmail = "<EMAIL>", assignedTo = "checker123", assignedToId = "checker123",
            approvalDate = LocalDate.now(), vendorName = "Test Vendor", documentId = "DOC123",
            typeOfAdvance = AdvanceType.INVOICE, type = PartnerType.VENDOR, status = Status.PENDING_APPROVAL,
            amount = BigDecimal("1000.00"), amountPending = BigDecimal("1000.00"), partnerId = 123L,
            partnerDetailId = 456L, tenant = "test_tenant", companyId = 1L, client = InvoiceType.VENDOR,
            remarks = "Test remarks", refDocuments = mutableListOf(), source = AdvancePaymentSource.SYSTEM
        )

        sampleAdvancePaymentDto = AdvancePaymentDto(
            amount = BigDecimal("1000.00"), partnerId = 123L, partnerName = "Test Vendor",
            partnerDetailId = 456L, tenant = "test_tenant", type = PartnerType.VENDOR,
            client = InvoiceType.VENDOR, typeOfAdvance = AdvanceType.INVOICE, remarks = "Test remarks",
            createdByName = "Test User", userEmail = "<EMAIL>",
            refDocuments = listOf(PaymentRefItem("REF123", LocalDate.now())), source = AdvancePaymentSource.SYSTEM
        )

        sampleUpdateAdvancePaymentDto = UpdateAdvancePaymentDto(
            amount = BigDecimal("1000.00"), paymentReference = "PAY123", paymentType = PaymentType.CASH,
            paymentDate = LocalDate.now(), bankName = "Test Bank", remarks = "Test update",
            tenant = "test_tenant", change = true, isRioTransaction = false
        )

        sampleReceipt = Receipt(
            id = 1L, receiptNumber = "REC123", createdAt = LocalDateTime.now(), createdBy = "user123",
            updatedBy = "user123", paymentTransactionId = "TXN123", remarks = "Test receipt",
            amount = 1000.0, advanceAmount = 500.0, advanceId = 1L, tenant = "test_tenant",
            partnerId = 1L, partnerDetailId = 1L, unUtilizedAmount = 1000.0
        )
    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    @Test
    fun `createAdvancePayment should create advance payment successfully`() {
        // Given
        every { companyService.getCompanyTenantMappingObject("test_tenant") } returns sampleCompanyTenantMapping
        every { checkerService.findChecker("test_tenant") } returns mutableListOf(sampleCheckerDetails)
        every { advancePaymentRepo.save(any<AdvancePayment>()) } returns sampleAdvancePayment

        // When
        val result = advancePaymentService.createAdvancePayment(sampleAdvancePaymentDto, "user123")

        // Then
        assertNotNull(result)
        assertEquals(1L, result.id)
        verify { advancePaymentRepo.save(any<AdvancePayment>()) }
    }

    @Test
    fun `createAdvancePayment should throw exception when no tenant mapping found`() {
        // Given
        every { companyService.getCompanyTenantMappingObject("test_tenant") } returns null

        // When & Then
        val exception = assertThrows<RequestException> {
            advancePaymentService.createAdvancePayment(sampleAdvancePaymentDto, "user123")
        }
        assertTrue(exception.message!!.contains("no tenant mapping found"))
    }

    @Test
    fun `createAdvancePayment should throw exception when no checker found`() {
        // Given
        every { companyService.getCompanyTenantMappingObject("test_tenant") } returns sampleCompanyTenantMapping
        every { checkerService.findChecker("test_tenant") } returns mutableListOf()

        // When & Then
        val exception = assertThrows<RequestException> {
            advancePaymentService.createAdvancePayment(sampleAdvancePaymentDto, "user123")
        }
        assertTrue(exception.message!!.contains("No checker found"))
    }

    @Test
    fun `getAllAdvancePayment should return paginated results`() {
        // Given
        val page = PageImpl(listOf(sampleAdvancePayment), PageRequest.of(0, 10), 1)
        every { companyService.getCompanyTenantMappingObject("test_tenant") } returns sampleCompanyTenantMapping
        every { advancePaymentRepo.findAll(any<Specification<AdvancePayment>>(), any<PageRequest>()) } returns page

        // When
        val result = advancePaymentService.getAllAdvancePayment(
            0, 10, null, null, null, null, null, null, null,
            null, null, null, null, "test_tenant", null, null
        )

        // Then
        assertEquals(1, result.elements)
        assertEquals(1, (result.data as List<*>).size)
    }

    @Test
    fun `getAllAdvancePayment should throw exception when no tenant mapping found`() {
        // Given
        every { companyService.getCompanyTenantMappingObject("test_tenant") } returns null

        // When & Then
        val exception = assertThrows<RequestException> {
            advancePaymentService.getAllAdvancePayment(
                0, 10, null, null, null, null, null, null, null,
                null, null, null, null, "test_tenant", null, null
            )
        }
        assertTrue(exception.message!!.contains("No tenant mapping found"))
    }

    @Test
    fun `getAllVendorAdvancePayment should return paginated results`() {
        // Given
        val pagination = PageRequest.of(0, 10)
        val vendorAdvancePayments = listOf(
            VendorAdvancePaymentDto(123L, 456L, "", 5, 2, 3, BigDecimal("1500.00"))
        )
        val page = PageImpl(vendorAdvancePayments, pagination, 1)
        val tenants: MutableList<String?> = mutableListOf("test_tenant")
        val partnerIds = listOf(123L)
        val clientTypes = listOf(InvoiceType.VENDOR)

        every { companyService.findTenants("test_tenant") } returns tenants
        every { supplierProxy.supplier(listOf(123L)) } returns listOf(Supplier(123L, "Test Vendor", null, mutableListOf(), null, null))
        every { advancePaymentRepo.getVendorAdvancePaymentWithPartnerIds(tenants, partnerIds, clientTypes, PartnerType.CUSTOMER, pagination) } returns page

        // When
        val result = advancePaymentService.getAllVendorAdvancePayment(
            0, 10, partnerIds, "test_tenant", null, clientTypes, null
        )

        // Then
        assertEquals(1, result.elements)
        val resultPage = result.data as Page<*>
        assertEquals(1, resultPage.content.size)
    }

    @Test
    fun `getAllVendorAdvancePayment should throw exception when no tenants found`() {
        // Given
        every { companyService.findTenants("test_tenant") } returns mutableListOf()

        // When & Then
        val exception = assertThrows<RequestException> {
            advancePaymentService.getAllVendorAdvancePayment(
                0, 10, null, "test_tenant", null, listOf(InvoiceType.VENDOR), null
            )
        }
        assertTrue(exception.message!!.contains("is not mapped to any Company"))
    }

    @Test
    fun `getAggregatedAdvPayment should return aggregated data`() {
        // Given
        val aggregatedData = AggregatedAdvancePaymentDto(3, BigDecimal("1500.00"))
        every { companyService.findTenants("test_tenant") } returns mutableListOf("test_tenant")
        every { advancePaymentRepo.getAggregatedAdvPaymentWithPartnerIds(any(), any(), any()) } returns aggregatedData

        // When
        val result = advancePaymentService.getAggregatedAdvPayment(
            listOf(123L), "test_tenant", listOf(InvoiceType.VENDOR), null
        )

        // Then
        assertNotNull(result)
        assertEquals(3, result.totalPendingAdvancePayment)
        assertEquals(BigDecimal("1500.00"), result.totalPendingAdvancePayAmount)
    }

    @Test
    fun `getVendorAdvancePayment should return paginated results`() {
        // Given
        val partnerId = 123L
        val tenant = "test_tenant"
        val pagination = PageRequest.of(0, 10, Sort.Direction.DESC, "updatedOn")
        val advancePayments = listOf(sampleAdvancePayment)
        val page = PageImpl(advancePayments, pagination, 1)

        // Mocks
        every { companyService.findTenants(tenant) } returns mutableListOf(tenant)
        every {
            advancePaymentRepo.getAdvancePaySupplierTotal(
                partnerId,
                null, // documentId
                null, // refDocumentNum
                null, // transactionFrom
                null, // transactionTo
                null, // type
                mutableListOf(tenant), // tenants
                PartnerType.CUSTOMER,
                pagination
            )
        } returns page

        // When
        val result = advancePaymentService.getVendorAdvancePayment(
            page = 0,
            size = 10,
            partnerId = partnerId,
            status = null,
            documentId = null,
            refDocumentNum = null,
            transactionFrom = null,
            transactionTo = null,
            type = null,
            tenant = tenant
        )

        // Then
        assertEquals(1, result.elements)
        assertEquals(1, (result.data as List<*>).size)
    }

    @Test
    fun `checkerAdvancePayment should approve advance payment successfully`() {
        // Given
        every { checkerService.findCheckers("test_tenant", null, null) } returns mutableListOf(sampleCheckerDetails)
        every { advancePaymentRepo.findById(1L) } returns Optional.of(sampleAdvancePayment)
        every { checkerService.compareChecker(any(), "checker123") } returns sampleCheckerDetails
        every { companyService.findTenants("test_tenant") } returns mutableListOf("test_tenant")
        every { companyService.getCompanyByTenant("test_tenant") } returns sampleCompany
        every { documentMasterService.getDocumentNumber(any(), any(), any()) } returns "DOC123"
        every { advancePaymentRepo.save(any<AdvancePayment>()) } returns sampleAdvancePayment

        // When
        val result = advancePaymentService.checkerAdvancePayment(1L, sampleUpdateAdvancePaymentDto, "checker123")

        // Then
        assertEquals(200, result.code)
        assertEquals("SUCCESS", result.message)
    }

    @Test
    fun `checkerAdvancePayment should throw exception when advance payment not found`() {
        // Given
        every { checkerService.findCheckers("test_tenant", null, null) } returns mutableListOf(sampleCheckerDetails)
        every { advancePaymentRepo.findById(1L) } returns Optional.empty()

        // When & Then
        val exception = assertThrows<RequestException> {
            advancePaymentService.checkerAdvancePayment(1L, sampleUpdateAdvancePaymentDto, "checker123")
        }
        assertTrue(exception.message!!.contains("is not present in our DB"))
    }

    @Test
    fun `checkerAdvancePayment should throw exception when user is creator`() {
        // Given
        every { checkerService.findCheckers("test_tenant", null, null) } returns mutableListOf(sampleCheckerDetails)
        every { advancePaymentRepo.findById(1L) } returns Optional.of(sampleAdvancePayment)

        // When & Then
        val exception = assertThrows<RequestException> {
            advancePaymentService.checkerAdvancePayment(1L, sampleUpdateAdvancePaymentDto, "user123")
        }
        assertTrue(exception.message!!.contains("Cannot approve the request as created by you"))
    }

    @Test
    fun `checkerAdvancePayment should return success when already approved`() {
        // Given
        val approvedPayment = sampleAdvancePayment.copy(status = Status.APPROVED)
        every { checkerService.findCheckers("test_tenant", null, null) } returns mutableListOf(sampleCheckerDetails)
        every { advancePaymentRepo.findById(1L) } returns Optional.of(approvedPayment)

        // When
        val result = advancePaymentService.checkerAdvancePayment(1L, sampleUpdateAdvancePaymentDto, "checker123")

        // Then
        assertEquals(200, result.code)
        assertEquals("SUCCESS", result.message)
    }

    @Test
    fun `checkerAdvancePayment should reject when change is false`() {
        // Given
        val rejectDto = sampleUpdateAdvancePaymentDto.copy(change = false)
        every { checkerService.findCheckers("test_tenant", null, null) } returns mutableListOf(sampleCheckerDetails)
        every { advancePaymentRepo.findById(1L) } returns Optional.of(sampleAdvancePayment)
        every { checkerService.compareChecker(any(), eq("checker123")) } returns sampleCheckerDetails
        every { advancePaymentRepo.save(any<AdvancePayment>()) } returns sampleAdvancePayment

        // When
        val result = advancePaymentService.checkerAdvancePayment(1L, rejectDto, "checker123")

        // Then
        assertEquals(200, result.code)
        assertEquals("SUCCESS", result.message)
    }

    @Test
    fun `checkerAdvancePayment should throw exception on pessimistic locking failure`() {
        // Given
        every { checkerService.findCheckers("test_tenant", null, null) } returns mutableListOf(sampleCheckerDetails)
        every { advancePaymentRepo.findById(1L) } returns Optional.of(sampleAdvancePayment)
        every { checkerService.compareChecker(any(), eq("checker123")) } returns sampleCheckerDetails
        every { companyService.findTenants("test_tenant") } returns mutableListOf("test_tenant")
        every { companyService.getCompanyByTenant("test_tenant") } returns sampleCompany
        every { documentMasterService.getDocumentNumber(any(), any(), any()) } returns "DOC123"
        every { advancePaymentRepo.save(any<AdvancePayment>()) } throws PessimisticLockingFailureException("Lock failure")

        // When & Then
        val exception = assertThrows<RequestException> {
            advancePaymentService.checkerAdvancePayment(1L, sampleUpdateAdvancePaymentDto, "checker123")
        }
        assertTrue(exception.message!!.contains("The record was already processed"))
    }

    @Test
    fun `getAdvanceOutstanding should return outstanding amount`() {
        // Given
        every { advancePaymentRepo.getAdvancePaymentOutstanding("test_tenant", 456L) } returns BigDecimal("1500.00")

        // When
        val result = advancePaymentService.getAdvanceOutstanding(456L, "test_tenant")

        // Then
        assertEquals(BigDecimal("1500.00"), result)
    }

    @Test
    fun `getAdvanceOutstanding should return zero when null`() {
        // Given
        every { advancePaymentRepo.getAdvancePaymentOutstanding("test_tenant", 456L) } returns null

        // When
        val result = advancePaymentService.getAdvanceOutstanding(456L, "test_tenant")

        // Then
        assertEquals(BigDecimal.ZERO, result)
    }

    @Test
    fun `getAdvancePaymentById should return advance payment`() {
        // Given
        every { advancePaymentRepo.findById(1L) } returns Optional.of(sampleAdvancePayment)

        // When
        val result = advancePaymentService.getAdvancePaymentById(1L)

        // Then
        assertNotNull(result)
        assertEquals(1L, result.id)
    }

    @Test
    fun `getAdvancePaymentById should throw exception when not found`() {
        // Given
        every { advancePaymentRepo.findById(1L) } returns Optional.empty()

        // When & Then
        val exception = assertThrows<RequestException> {
            advancePaymentService.getAdvancePaymentById(1L)
        }
        assertTrue(exception.message!!.contains("Advance Payment not found for id"))
    }

    @Test
    fun `getAdvancePaymentByDocumentNumber should return advance payment`() {
        // Given
        every { advancePaymentRepo.getByDocumentId("DOC123") } returns sampleAdvancePayment

        // When
        val result = advancePaymentService.getAdvancePaymentByDocumentNumber("DOC123")

        // Then
        assertNotNull(result)
        assertEquals("DOC123", result.documentId)
    }

    @Test
    fun `getAdvancePaymentByDocumentNumber should throw exception when not found`() {
        // Given
        every { advancePaymentRepo.getByDocumentId("INVALID") } returns null

        // When & Then
        val exception = assertThrows<RequestException> {
            advancePaymentService.getAdvancePaymentByDocumentNumber("INVALID")
        }
        assertTrue(exception.message!!.contains("Advance Payment not found for documentNumber"))
    }

    @Test
    fun `getAdvancePayUrl should create file and return success`() {
        // Given
        val vendorAdvancePaymentDto = VendorAdvancePaymentDto(123L, 456L, "", 5, 2, 3, BigDecimal("1500.00"))
        val vendorDataLinks = VendorDataLinks(1L, LocalDateTime.now(), LocalDateTime.now(), "IN_PROGRESS", VendorDataEnum.ADVANCE_PAYMENT, null, "SYSTEM", "th124", false)

        every { companyService.findTenants("th124") } returns mutableListOf("th124")
        every { advancePaymentRepo.getAdvancePaymentDataForUrl(mutableListOf("th124")) } returns mutableListOf(vendorAdvancePaymentDto)
        every { supplierProxy.supplier(listOf(123L), null) } returns listOf(Supplier(123L, "Test Vendor", null, mutableListOf(), null, null))
        every { vendorFileService.saveReport(eq("th124"), any()) } returns vendorDataLinks

        // When
        val result = advancePaymentService.getAdvancePayUrl("th124", "SYSTEM")

        // Then
        assertEquals(200, result.code)
        assertEquals("Success", result.message)
    }

    @Test
    fun `getAdvancePayUrl should return no data when no tenants found`() {
        // Given
        every { companyService.findTenants("test_tenant") } returns mutableListOf()

        // When
        val result = advancePaymentService.getAdvancePayUrl("test_tenant", "user123")

        // Then
        assertEquals(200, result.code)
        assertEquals("No Data", result.message)
    }

    @Test
    fun `advancePayDownload should return file link when available`() {
        // Given
        val vendorDataLink = VendorDataLinks(1L, LocalDateTime.now(), LocalDateTime.now(), "http://test-link.com", VendorDataEnum.ADVANCE_PAYMENT, null, "user123", "test_tenant", false)
        every { advancePayLinksDataRepo.getAdvancePayLink("user123", "test_tenant", false) } returns listOf(vendorDataLink)

        // When
        val result = advancePaymentService.advancePayDownload("test_tenant", "user123")

        // Then
        assertEquals(200, result.code)
        assertEquals("Success", result.message)
        assertEquals("http://test-link.com", result.url)
    }

    @Test
    fun `advancePayDownload should return null when no file available`() {
        // Given
        every { advancePayLinksDataRepo.getAdvancePayLink("user123", "test_tenant", false) } returns emptyList()

        // When
        val result = advancePaymentService.advancePayDownload("test_tenant", "user123")

        // Then
        assertEquals(200, result.code)
        assertEquals("Success", result.message)
        assertNull(result.url)
    }

    @Test
    fun `advancePayDetailDownload should return file link when available`() {
        // Given
        val vendorDataLink = VendorDataLinks(1L, LocalDateTime.now(), LocalDateTime.now(), "http://test-detail-link.com", VendorDataEnum.ADVANCE_PAY_DETAIL, null, "user123", "test_tenant", false)
        every { advancePayLinksDataRepo.getAdvancePayDetailLink("test_tenant", "user123", false) } returns listOf(vendorDataLink)

        // When
        val result = advancePaymentService.advancePayDetailDownload("test_tenant", "user123")

        // Then
        assertEquals(200, result.code)
        assertEquals("Success", result.message)
        assertEquals("http://test-detail-link.com", result.url)
    }

    @Test
    fun `updateAdvancePaymentChecker should update checker successfully`() {
        // Given
        val mockAdvancePayment = sampleAdvancePayment.copy(status = Status.PENDING_APPROVAL)
        every { checkerService.findCheckers("test_tenant", 1L, null) } returns mutableListOf(sampleCheckerDetails)
        every { advancePaymentRepo.get(1L) } returns mockAdvancePayment
        every { companyService.getCompanyTenantMappingObject("test_tenant") } returns sampleCompanyTenantMapping
        every { vaultCheckerService.getCheckerByCompanyId(1L) } returns mutableListOf(sampleCheckerDetails)
        every { advancePaymentRepo.save(any<AdvancePayment>()) } answers { it.invocation.args[0] as AdvancePayment }

        // When
        val result = advancePaymentService.updateAdvancePaymentChecker(1L, 1L, "test_tenant", "user123")

        // Then
        assertEquals("success", result.message)
    }

    @Test
    fun `updateAdvancePaymentChecker should throw exception when no checker found`() {
        // Given
        every { checkerService.findCheckers("test_tenant", 1L, null) } returns mutableListOf()

        // When & Then
        val exception = assertThrows(RequestException::class.java) {
            advancePaymentService.updateAdvancePaymentChecker(1L, 1L, "test_tenant", "user123")
        }
        assertTrue(exception.message!!.contains("No active checker found"))
    }

    @Test
    fun `updateAdvancePaymentChecker should throw exception when advance payment not found`() {
        // Given
        every { checkerService.findCheckers("test_tenant", 1L, null) } returns mutableListOf(sampleCheckerDetails)
        every { advancePaymentRepo.get(1L) } returns null

        // When & Then
        val exception = assertThrows(RequestException::class.java) {
            advancePaymentService.updateAdvancePaymentChecker(1L, 1L, "test_tenant", "user123")
        }
        assertTrue(exception.message!!.contains("No advance Pay entry found"))
    }

    @Test
    fun `cancelAdvancePayment should cancel advance payment successfully`() {
        // Given
        val userId = "checker123"
        val advanceId = 1L

        val mockAdvancePayment = sampleAdvancePayment.copy(
            id = advanceId,
            status = Status.APPROVED,
            consumed = false,
            amount = BigDecimal(1000),
            amountPending = BigDecimal(1000),
            tenant = "test_tenant"
        )

        every { advancePaymentRepo.get(advanceId) } returns (mockAdvancePayment)
        every { checkerService.findCheckers("test_tenant", null, null) } returns (mutableListOf(sampleCheckerDetails))
        every { checkerService.compareChecker(any(), eq(userId)) } returns (sampleCheckerDetails)
        every { companyService.getCompanyCodeByTenant("test_tenant") } returns "TEST"
        every { advancePaymentService.createReversalLedgerEntry(mockAdvancePayment, userId, "TEST") } just runs
        every { advancePaymentRepo.save(any<AdvancePayment>()) } answers { firstArg() }

        // When
        val result = advancePaymentService.cancelAdvancePayment(userId, advanceId)

        // Then
        assertEquals("success", result.message)
        assertEquals(Status.CANCELLED, mockAdvancePayment.status)
        assertEquals(userId, mockAdvancePayment.updatedBy)
        assertNotNull(mockAdvancePayment.updatedOn)

        verify { advancePaymentRepo.get(advanceId) }
        verify { checkerService.findCheckers("test_tenant", null, null) }
        verify { checkerService.compareChecker(any(), eq(userId)) }
        verify { companyService.getCompanyCodeByTenant("test_tenant") }
        verify { advancePaymentService.createReversalLedgerEntry(mockAdvancePayment, userId, "TEST") }
        verify { advancePaymentRepo.save(mockAdvancePayment) }
    }

    @Test
    fun `cancelAdvancePayment should throw exception when advance payment not found`() {
        // Given
        every { advancePaymentRepo.get(1L) } returns (null)

        // When & Then
        val exception = assertThrows(RequestException::class.java) {
            advancePaymentService.cancelAdvancePayment("user123", 1L)
        }
        assertTrue(exception.message!!.contains("not found"))
    }

    @Test
    fun `cancelAdvancePayment should throw exception when already consumed`() {
        // Given
        val consumedAdvancePayment = sampleAdvancePayment.copy(consumed = true)
        every { advancePaymentRepo.get(1L) } returns (consumedAdvancePayment)
        every { checkerService.findCheckers("test_tenant", null, null) } returns (mutableListOf(sampleCheckerDetails))
        every { checkerService.compareChecker(any(), eq("checker123")) } returns (sampleCheckerDetails)

        // When & Then
        val exception = assertThrows(RequestException::class.java) {
            advancePaymentService.cancelAdvancePayment("checker123", 1L)
        }
        assertTrue(exception.message!!.contains("used in settlement"))
    }

    @Test
    fun `changeAdvancePaymentStatus should change status successfully`() {
        // Given
        val advancePaymentId = 1L
        val mockAdvancePayment = sampleAdvancePayment.copy(
            id = advancePaymentId,
            status = Status.PENDING_APPROVAL,
            tenant = "test_tenant",
            type = PartnerType.VENDOR,
            paymentType = PaymentType.CASH,
            amount = BigDecimal(1000),
            vendorName = "Test Vendor",
            documentId = "DOC123",
            partnerId = 123L,
            partnerDetailId = 456L,
            createdBy = "user1",
            assignedTo = "assignee1",
            assignedToId = "assigneeId1",
            client = InvoiceType.RIO
        )

        val sampleCompanyInstance = sampleCompany.copy(id = 999L)

        // 👇 Fix: Mock findById(), not findByIdOrNull()
        every { advancePaymentRepo.findById(advancePaymentId) } returns Optional.of(mockAdvancePayment)

        every { companyService.getCompanyByTenant("test_tenant") } returns (sampleCompanyInstance)
        every { advancePaymentRepo.save(any<AdvancePayment>()) } answers { firstArg() }

        every { adjustmentService.addAdjustmentToLedger(any(), any(), isNull()) } just runs
        every { adjustmentService.updateVendorBalance(any()) } just runs

        // When
        val result = advancePaymentService.changeAdvancePaymentStatus(advancePaymentId, Status.APPROVED)

        // Then
        assertEquals(Status.APPROVED, result.status)
        verify { adjustmentService.addAdjustmentToLedger(any(), any(), isNull()) }
        verify { adjustmentService.updateVendorBalance(any()) }
        verify { advancePaymentRepo.save(mockAdvancePayment) }
    }

    @Test
    fun `changeAdvancePaymentStatus should throw exception when advance payment not found`() {
        // Given
        every { advancePaymentRepo.findById(1L) } returns (Optional.empty())

        // When & Then
        val exception = assertThrows(RequestException::class.java) {
            advancePaymentService.changeAdvancePaymentStatus(1L, Status.APPROVED)
        }
        assertTrue(exception.message!!.contains("is not present in our DB"))
    }

    @Test
    fun `checkAndUpdateLedgerForAdvancePayment should update ledger successfully`() {
        // Given
        every { advancePaymentRepo.getByDocumentId("DOC123") } returns (sampleAdvancePayment)
        every { receiptService.getReceiptByAdvancePaymentId(1L) } returns (sampleReceipt)
        every { partnerService.getExistingLedgerAmount(456L, PartnerType.VENDOR, "test_tenant", "DOC123", "REC123")} returns 0.0

        // When
        advancePaymentService.checkAndUpdateLedgerForAdvancePayment("user123", "DOC123")

        // Then
        verify { partnerService.addVendorLedgerEntry(eq("user123"), any<VendorLedgerDto>()) }
    }

    @Test
    fun `checkAndUpdateLedgerForAdvancePayment should throw exception when ledger already exists`() {
        // Given
        val referenceNumber = "DOC123"
        val user = "test_user"

        val advancePayment = sampleAdvancePayment.copy(amount = BigDecimal(1000))
        val receipt = sampleReceipt.copy(amount = 1100.0) // mismatch to make xor true

        every { advancePaymentRepo.getByDocumentId(referenceNumber) } returns (advancePayment)
        every { receiptService.getReceiptByAdvancePaymentId(1L) } returns (receipt)
        every {
            partnerService.getExistingLedgerAmount(
                456L, PartnerType.VENDOR, "test_tenant", "DOC123", "REC123"
            )
        } returns 1000.0

        // When / Then
        val exception = assertThrows<RequestException> {
            advancePaymentService.checkAndUpdateLedgerForAdvancePayment(user, referenceNumber)
        }

        assertTrue(exception.message!!.contains("Ledger already exist for transaction $referenceNumber"))

        verify(exactly = 0) { partnerService.addVendorLedgerEntry(any(), any()) }
    }
}
