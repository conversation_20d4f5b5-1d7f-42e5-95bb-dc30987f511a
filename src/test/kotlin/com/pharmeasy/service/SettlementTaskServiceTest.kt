package com.pharmeasy.service

import com.pharmeasy.data.CompanyTenantMapping
import com.pharmeasy.data.SettlementTask
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.PaginationDto
import com.pharmeasy.model.ProcessSettlementTaskDto
import com.pharmeasy.model.SettlementFileMeta
import com.pharmeasy.repo.SettlementTaskRepo
import com.pharmeasy.repo.read.SettlementTaskReadRepo
import com.pharmeasy.stream.BulkSettlementPusher
import com.pharmeasy.type.PartnerType
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.SelectionType
import com.pharmeasy.type.SettlementFileStatus
import com.pharmeasy.type.SettlementTaskStatus
import io.mockk.every
import io.mockk.impl.annotations.MockK
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDateTime
import java.util.*

class SettlementTaskServiceTest {

    @MockK
    private lateinit var settlementTaskRepo: SettlementTaskRepo
    @MockK
    private lateinit var settlementTaskReadRepo: SettlementTaskReadRepo
    @MockK
    private lateinit var companyService: CompanyService
    @MockK
    private lateinit var bulkSettlementPusher: BulkSettlementPusher
    @MockK
    private lateinit var s3FileUtilityService: S3FileUtilityService
    @MockK
    private lateinit var s3Uploader: S3Uploader

    private lateinit var settlementTaskService: SettlementTaskService

    private lateinit var sampleCompanyTenantMapping: CompanyTenantMapping
    private lateinit var sampleSettlementTask: SettlementTask
    private lateinit var sampleSettlementFileMeta: SettlementFileMeta
    private lateinit var sampleProcessSettlementTaskDto: ProcessSettlementTaskDto
    private lateinit var samplePagination: PaginationDto
    @BeforeEach
    fun setUp() {
        // Initialize MockK annotations
        io.mockk.MockKAnnotations.init(this)

        settlementTaskService = SettlementTaskService(
            settlementTaskRepo = settlementTaskRepo,
            settlementTaskReadRepo = settlementTaskReadRepo,
            companyService = companyService,
            bulkSettlementPusher = bulkSettlementPusher,
            s3FileUtilityService = s3FileUtilityService,
            s3Uploader = s3Uploader,
            bucket = "test-bucket",
            settlementPrefix = "test-prefix"
        )

        sampleCompanyTenantMapping = CompanyTenantMapping(
            id = 1L,
            companyId = 456L,
            tenant = "test_tenant",
            tenantName = "test_tenant",
            theaId = 1,
            partnerDetailId = 123L
        )

        sampleSettlementTask = SettlementTask(
            id = 1L,
            createdOn = LocalDateTime.now(),
            createdBy = "test_user",
            settlementMode = PaymentType.CHEQUE,
            fileStatus = SettlementFileStatus.VALIDATION_IN_PROGRESS,
            taskStatus = SettlementTaskStatus.PENDING,
            s3Uri = "test-uri",
            partnerType = PartnerType.VENDOR,
            company = sampleCompanyTenantMapping,
            isPartnerLevel = false
        )

        sampleSettlementFileMeta = SettlementFileMeta(
            objectKey = "test-uri",
            mode = PaymentType.CHEQUE,
            isPartnerLevel = false
        )

        sampleProcessSettlementTaskDto = ProcessSettlementTaskDto(
            taskId = 1L,
            selectionType = SelectionType.ALL,
            taskItemIdList = null,
            settlementMode = PaymentType.CASH
        )

        samplePagination = PaginationDto(
            elements = 1L,
            page = 1,
            hasPrevious = false,
            hasNext = false,
            data = listOf(sampleSettlementTask)
        )

    }

    @Test
    fun `uploadSettlementFile should throw exception when company not found`() {
        // Given
        every { companyService.getCompanyTenantMappingObject("test_tenant") } returns null

        // When/Then
        assertThrows(RequestException::class.java) {
            settlementTaskService.uploadSettlementFile(
                settlementFileMeta = sampleSettlementFileMeta,
                tenant = "test_tenant",
                user = "test_user",
                ds = null,
                customerType = false
            )
        }
    }


    @Test
    fun `processSettlementTask should throw exception when task not found`() {
        // Given
        every { companyService.getCompanyTenantMappingObject("test_tenant") } returns sampleCompanyTenantMapping
        every { settlementTaskRepo.findById(any()) } returns Optional.empty()
        every { settlementTaskReadRepo.findInProgressTaskInCompany(any()) } returns emptyList()

        // When/Then
        assertThrows(RequestException::class.java) {
            settlementTaskService.processSettlementTask(
                processTaskDto = sampleProcessSettlementTaskDto,
                tenant = "test_tenant",
                user = "test_user",
                ds = null,
                userHash = "test_hash"
            )
        }
    }

    @Test
    fun `getTasks should throw exception when company not found`() {
        // Given
        every { companyService.getCompanyTenantMappingObject("test_tenant") } returns null

        // When/Then
        assertThrows(RequestException::class.java) {
            settlementTaskService.getTasks(
                fromDateTime = null,
                toDateTime = null,
                mode = null,
                status = null,
                customerType = false,
                ds = null,
                tenant = "test_tenant",
                page = 0,
                size = 10,
                isPartnerLevel = null
            )
        }
    }
} 
