package com.pharmeasy.service

import com.pharmeasy.data.CashBankLedger
import com.pharmeasy.model.CashBankLedgerDto
import com.pharmeasy.repo.CashBankLedgerRepo
import com.pharmeasy.type.LedgerEntryType
import com.pharmeasy.type.PaymentMode
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.SettlementType
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.test.util.ReflectionTestUtils
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

class CashBankLedgerServiceTest {

    private lateinit var cashBankLedgerService: CashBankLedgerService
    private lateinit var cashBankLedgerRepo: CashBankLedgerRepo

    @BeforeEach
    fun setUp() {
        cashBankLedgerRepo = mockk(relaxed = true)
        
        // Create the service
        cashBankLedgerService = spyk(CashBankLedgerService())
        
        // Inject the mocked repository using reflection
        ReflectionTestUtils.setField(cashBankLedgerService, "cashBankLedgerRepo", cashBankLedgerRepo)
    }

    @Test
    @DisplayName("checkAndAddCashBankLedgerEntry should return existing entry when duplicate is found")
    fun testCheckAndAddCashBankLedgerEntryWithDuplicate() {
        // Arrange
        val user = "testUser"
        val documentNumber = "DOC-001"
        val referenceNumber = "REF-001"
        val ledgerEntryType = LedgerEntryType.CREDIT
        
        val cashBankLedgerDto = CashBankLedgerDto(
            transactionDate = LocalDate.now(),
            ledgerEntryType = ledgerEntryType,
            documentNumber = documentNumber,
            documentType = SettlementType.RECEIPT,
            referenceNumber = referenceNumber,
            externalReferenceNumber = "EXT-001",
            particulars = "Test transaction",
            debitAmount = BigDecimal.ZERO,
            creditAmount = BigDecimal.valueOf(1000),
            partnerId = 1L,
            partnerDetailId = 1L,
            tenant = "th001",
            paymentMode = PaymentMode.CASH,
            paymentType = PaymentType.CASH
        )
        
        val existingEntry = CashBankLedger(
            id = 1L,
            createdOn = LocalDateTime.now(),
            updatedOn = LocalDateTime.now(),
            createdBy = "system",
            updatedBy = "system",
            transactionDate = LocalDate.now(),
            ledgerEntryType = ledgerEntryType,
            documentNumber = documentNumber,
            documentType = SettlementType.RECEIPT,
            referenceNumber = referenceNumber,
            externalReferenceNumber = "EXT-001",
            particulars = "Test transaction",
            debitAmount = BigDecimal.ZERO,
            creditAmount = BigDecimal.valueOf(1000),
            balance = BigDecimal.valueOf(1000),
            partnerId = 1L,
            partnerDetailId = 1L,
            tenant = "th001",
            companyId = 1L,
            paymentMode = PaymentMode.CASH,
            paymentType = PaymentType.CASH
        )
        
        every { 
            cashBankLedgerRepo.findDup(
                documentNumber,
                referenceNumber,
                ledgerEntryType
            ) 
        } returns existingEntry
        
        // Act
        val result = cashBankLedgerService.checkAndAddCashBankLedgerEntry(user, cashBankLedgerDto)
        
        // Assert
        assertEquals(existingEntry, result)
        verify { 
            cashBankLedgerRepo.findDup(
                documentNumber,
                referenceNumber,
                ledgerEntryType
            ) 
        }
    }

    @Test
    @DisplayName("checkAndAddCashBankLedgerEntry should create new entry when no duplicate is found")
    fun testCheckAndAddCashBankLedgerEntryWithoutDuplicate() {
        // Arrange
        val user = "testUser"
        val documentNumber = "DOC-001"
        val referenceNumber = "REF-001"
        val ledgerEntryType = LedgerEntryType.CREDIT
        
        val cashBankLedgerDto = CashBankLedgerDto(
            transactionDate = LocalDate.now(),
            ledgerEntryType = ledgerEntryType,
            documentNumber = documentNumber,
            documentType = SettlementType.RECEIPT,
            referenceNumber = referenceNumber,
            externalReferenceNumber = "EXT-001",
            particulars = "Test transaction",
            debitAmount = BigDecimal.ZERO,
            creditAmount = BigDecimal.valueOf(1000),
            partnerId = 1L,
            partnerDetailId = 1L,
            tenant = "th001",
            paymentMode = PaymentMode.CASH,
            paymentType = PaymentType.CASH
        )
        
        val newEntry = CashBankLedger(
            id = 1L,
            createdOn = LocalDateTime.now(),
            updatedOn = LocalDateTime.now(),
            createdBy = "system",
            updatedBy = "system",
            transactionDate = LocalDate.now(),
            ledgerEntryType = ledgerEntryType,
            documentNumber = documentNumber,
            documentType = SettlementType.RECEIPT,
            referenceNumber = referenceNumber,
            externalReferenceNumber = "EXT-001",
            particulars = "Test transaction",
            debitAmount = BigDecimal.ZERO,
            creditAmount = BigDecimal.valueOf(1000),
            balance = BigDecimal.valueOf(1000),
            partnerId = 1L,
            partnerDetailId = 1L,
            tenant = "th001",
            companyId = 1L,
            paymentMode = PaymentMode.CASH,
            paymentType = PaymentType.CASH
        )
        
        every { 
            cashBankLedgerRepo.findDup(
                documentNumber,
                referenceNumber,
                ledgerEntryType
            ) 
        } returns null
        
        every { 
            cashBankLedgerService.addCashBankLedgerEntry(user, cashBankLedgerDto) 
        } returns newEntry
        
        // Act
        val result = cashBankLedgerService.checkAndAddCashBankLedgerEntry(user, cashBankLedgerDto)
        
        // Assert
        assertEquals(newEntry, result)
        verify { 
            cashBankLedgerRepo.findDup(
                documentNumber,
                referenceNumber,
                ledgerEntryType
            ) 
        }
        verify { cashBankLedgerService.addCashBankLedgerEntry(user, cashBankLedgerDto) }
    }
}
