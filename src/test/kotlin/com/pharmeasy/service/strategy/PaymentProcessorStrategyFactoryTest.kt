package com.pharmeasy.service.strategy

import com.pharmeasy.service.CashPaymentProcessor
import com.pharmeasy.service.ChequePaymentProcessor
import com.pharmeasy.service.DefaultPaymentProcessor
import com.pharmeasy.service.DirectPaymentProcessor
import com.pharmeasy.service.NeftPaymentProcessor
import com.pharmeasy.type.PaymentType
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertSame
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test

class PaymentProcessorStrategyFactoryTest {

    private lateinit var paymentProcessorStrategyFactory: PaymentProcessorStrategyFactory
    private lateinit var chequePaymentProcessor: ChequePaymentProcessor
    private lateinit var cashPaymentProcessor: CashPaymentProcessor
    private lateinit var neftPaymentProcessor: NeftPaymentProcessor
    private lateinit var directPaymentProcessor: DirectPaymentProcessor
    private lateinit var defaultPaymentProcessor: DefaultPaymentProcessor

    @BeforeEach
    fun setUp() {
        // Create mocks for all payment processors
        chequePaymentProcessor = mockk()
        cashPaymentProcessor = mockk()
        neftPaymentProcessor = mockk()
        directPaymentProcessor = mockk()
        defaultPaymentProcessor = mockk()

        // Create the factory with mocked dependencies
        paymentProcessorStrategyFactory = PaymentProcessorStrategyFactory(
            chequePaymentProcessor,
            cashPaymentProcessor,
            neftPaymentProcessor,
            directPaymentProcessor,
            defaultPaymentProcessor
        )
    }

    @Test
    @DisplayName("getInstance should return ChequePaymentProcessor for CHEQUE payment type")
    fun testGetInstanceForChequePaymentType() {
        // Act
        val result = paymentProcessorStrategyFactory.getInstance(PaymentType.CHEQUE)

        // Assert
        assertSame(chequePaymentProcessor, result)
    }

    @Test
    @DisplayName("getInstance should return CashPaymentProcessor for CASH payment type")
    fun testGetInstanceForCashPaymentType() {
        // Act
        val result = paymentProcessorStrategyFactory.getInstance(PaymentType.CASH)

        // Assert
        assertSame(cashPaymentProcessor, result)
    }

    @Test
    @DisplayName("getInstance should return NeftPaymentProcessor for NEFT payment type")
    fun testGetInstanceForNeftPaymentType() {
        // Act
        val result = paymentProcessorStrategyFactory.getInstance(PaymentType.NEFT)

        // Assert
        assertSame(neftPaymentProcessor, result)
    }

    @Test
    @DisplayName("getInstance should return DirectPaymentProcessor for DIRECT_PAYMENT payment type")
    fun testGetInstanceForDirectPaymentType() {
        // Act
        val result = paymentProcessorStrategyFactory.getInstance(PaymentType.DIRECT_PAYMENT)

        // Assert
        assertSame(directPaymentProcessor, result)
    }

    @Test
    @DisplayName("getInstance should return DefaultPaymentProcessor for unknown payment type")
    fun testGetInstanceForUnknownPaymentType() {
        // Act
        val result = paymentProcessorStrategyFactory.getInstance(PaymentType.OTHERS)

        // Assert
        assertSame(defaultPaymentProcessor, result)
    }
}
