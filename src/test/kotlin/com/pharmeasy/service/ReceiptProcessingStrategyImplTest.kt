package com.pharmeasy.service

import com.pharmeasy.data.DraftReceipt
import com.pharmeasy.data.DraftReceiptEntityMapping
import com.pharmeasy.data.Receipt
import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.SettlementGroupNumber
import com.pharmeasy.service.strategy.PaymentProcessorStrategyFactory
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.ReceiptStatus
import com.pharmeasy.utils.DataProvider
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class ReceiptProcessingStrategyImplTest {

    private lateinit var receiptProcessingStrategy: ReceiptProcessingStrategyImpl
    private lateinit var companyService: CompanyService
    private lateinit var documentService: DocumentMasterService
    private lateinit var paymentProcessorStrategyFactory: PaymentProcessorStrategyFactory
    private lateinit var settleableProcessorFactory: SettleableProcessorFactory
    private lateinit var receiptService: ReceiptServiceV2

    companion object {
        const val RECEIPT_NUMBER = "REC-001"
        const val TENANT = DataProvider.TENANT
        const val PARTNER_ID = DataProvider.PARTNER_ID
        const val PARTNER_DETAIL_ID = DataProvider.PDI
        const val PAYMENT_TRANSACTION_ID = DataProvider.TX_ID
        const val AMOUNT = 1000.0
        const val USER = "test-user"
        const val PARTNER_NAME = DataProvider.PARTNER_NAME
    }

    private lateinit var mockDraftReceipt: DraftReceipt
    private lateinit var mockReceipt: Receipt
    private lateinit var mockPartnerInfo: PartnerInfo
    private lateinit var mockSettlementGroupNumber: SettlementGroupNumber

    @BeforeEach
    fun setUp() {
        // Initialize all mocks
        companyService = mockk(relaxed = true)
        documentService = mockk(relaxed = true)
        paymentProcessorStrategyFactory = mockk(relaxed = true)
        settleableProcessorFactory = mockk(relaxed = true)
        receiptService = mockk(relaxed = true)

        // Create the strategy with mocked dependencies
        receiptProcessingStrategy = ReceiptProcessingStrategyImpl(
            companyService,
            documentService,
            paymentProcessorStrategyFactory,
            settleableProcessorFactory,
            receiptService
        )

        mockDraftReceipt = DataProvider.approvedDraftReceipt(
            id = 1L,
            receiptNumber = RECEIPT_NUMBER,
            paymentTransactionId = PAYMENT_TRANSACTION_ID,
            amount = AMOUNT,
            paymentType = PaymentType.CASH,
            tenant = TENANT,
            partnerId = PARTNER_ID,
            partnerDetailId = PARTNER_DETAIL_ID,
            remarks = "Receipt Created from RIO Payment",
            source = receiptProcessingStrategy.source,
            unUtilizedAmount = AMOUNT,
        )

        mockReceipt = DataProvider.receipt(
            id = 1L,
            receiptNumber = RECEIPT_NUMBER,
            paymentTransactionId = PAYMENT_TRANSACTION_ID,
            amount = AMOUNT,
            status = ReceiptStatus.GENERATED,
            draftReceipt = mockDraftReceipt
        )

        mockPartnerInfo = DataProvider.partnerInfo(
            partnerId = PARTNER_ID,
            partnerDetailId = PARTNER_DETAIL_ID,
            partnerName = PARTNER_NAME,
            distributorPdi = DataProvider.DISTRIBUTOR_PDI,
            tenant = TENANT
        )

        mockSettlementGroupNumber = SettlementGroupNumber("SGN-001")

        // Default mock behaviors
        every { receiptService.getReceiptByDraftId(any()) } returns null
        every { receiptService.saveReceipt(any()) } answers { 
            val receipt = firstArg<Receipt>()
            // Set an ID to simulate database save
            receipt.copy(id = 1L)
        }
    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    @Nested
    @DisplayName("createReceipt Tests")
    inner class CreateReceiptTests {

        @Test
        @DisplayName("Should create a new receipt when no existing receipt is found")
        fun testCreateReceiptCreatesNewReceipt() {
            // Arrange
            val receiptSlot = slot<Receipt>()
            every { receiptService.saveReceipt(capture(receiptSlot)) } answers {
                val receipt = receiptSlot.captured
                // Set an ID to simulate database save
                receipt.copy(id = 1L)
            }

            // Act
            val result = receiptProcessingStrategy.createReceipt(mockDraftReceipt, mockSettlementGroupNumber)

            // Assert
            assertNotNull(result)
            assertEquals(RECEIPT_NUMBER, result.receiptNumber)
            assertEquals(PAYMENT_TRANSACTION_ID, result.paymentTransactionId)
            assertEquals(AMOUNT, result.amount)

            verify { receiptService.getReceiptByDraftId(1L) }
            verify { receiptService.saveReceipt(any()) }
        }

        @Test
        @DisplayName("Should return existing receipt when one is found")
        fun testCreateReceiptReturnsExistingReceipt() {
            // Arrange
            every { receiptService.getReceiptByDraftId(1L) } returns mockReceipt

            // Act
            val result = receiptProcessingStrategy.createReceipt(mockDraftReceipt, mockSettlementGroupNumber)

            // Assert
            assertNotNull(result)
            assertEquals(mockReceipt, result)

            verify { receiptService.getReceiptByDraftId(1L) }
            verify(exactly = 0) { receiptService.saveReceipt(any()) }
        }

        @Test
        @DisplayName("Should throw exception when draft receipt status is not APPROVED")
        fun testCreateReceiptThrowsExceptionForNonApprovedDraftReceipt() {
            // Arrange
            val nonApprovedDraftReceipt = DataProvider.draftReceipt(
                id = 1L,
                status = ReceiptStatus.DRAFT
            )

            // Act & Assert\
            val exception = assertThrows<IllegalArgumentException> {
                receiptProcessingStrategy.createReceipt(nonApprovedDraftReceipt, mockSettlementGroupNumber)
            }

            assertEquals("Receipt status must be APPROVED", exception.message)
            verify(exactly = 0) { receiptService.getReceiptByDraftId(any()) }
            verify(exactly = 0) { receiptService.saveReceipt(any()) }
        }

        @Test
        @DisplayName("Should create a receipt with settlement group number")
        fun testCreateReceiptWithSettlementGroupNumber() {
            // Arrange
            val receiptSlot = slot<Receipt>()
            every { receiptService.saveReceipt(capture(receiptSlot)) } answers {
                val receipt = receiptSlot.captured
                // Set an ID to simulate database save
                receipt.copy(id = 1L)
            }

            // Create a mock DraftReceiptEntityMapping
            val mockMapping = mockk<DraftReceiptEntityMapping>(relaxed = true)
            val mockMappings = mutableListOf(mockMapping)

            val draftReceiptWithMappings = DataProvider.approvedDraftReceipt(
                id = 1L,
                receiptNumber = RECEIPT_NUMBER,
                paymentTransactionId = PAYMENT_TRANSACTION_ID,
                amount = AMOUNT,
                paymentType = PaymentType.CASH,
                tenant = TENANT,
                partnerId = PARTNER_ID,
                partnerDetailId = PARTNER_DETAIL_ID,
                remarks = "Receipt Created from RIO Payment",
                source = receiptProcessingStrategy.source,
                unUtilizedAmount = AMOUNT,
                settleableMappings = mockMappings
            )

            // Act
            val result = receiptProcessingStrategy.createReceipt(draftReceiptWithMappings, mockSettlementGroupNumber)

            // Assert
            assertNotNull(result)
            assertEquals(RECEIPT_NUMBER, result.receiptNumber)

            verify { receiptService.getReceiptByDraftId(1L) }
            verify { receiptService.saveReceipt(any()) }
            verify { receiptService.addSettlement(any(), mockMapping, mockSettlementGroupNumber) }
        }
    }

    @Nested
    @DisplayName("addSettlements Tests")
    inner class AddSettlementsTests {

        @Test
        @DisplayName("Should add settlements to receipt")
        fun testAddSettlements() {
            // Arrange
            val mockMapping1 = mockk<DraftReceiptEntityMapping>(relaxed = true)
            val mockMapping2 = mockk<DraftReceiptEntityMapping>(relaxed = true)
            val mockMappings = listOf(mockMapping1, mockMapping2)

            // Act
            receiptProcessingStrategy.addSettlements(mockReceipt, mockMappings, mockSettlementGroupNumber)

            // Assert
            verify { receiptService.addSettlement(mockReceipt, mockMapping1, mockSettlementGroupNumber) }
            verify { receiptService.addSettlement(mockReceipt, mockMapping2, mockSettlementGroupNumber) }
        }
    }
}
