package com.pharmeasy.service

import com.pharmeasy.utils.DataProvider
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class ReceiptStrategyTest {

    @Nested
    @DisplayName("getStrategy Tests")
    inner class GetStrategyTests {

        @Test
        @DisplayName("Should return PreApprovedReceipt for digital payment types")
        fun testGetStrategyReturnsPreApprovedReceiptForDigitalPaymentTypes() {
            // Test for DIGITAL_PAYMENT
            val digitalPayment = DataProvider.paymentInfo(paymentMode = "DIGITAL_PAYMENT")
            assertEquals(ReceiptStrategy.PreApprovedReceipt, ReceiptStrategy.getStrategy(digitalPayment))

            // Test for WALLET
            val walletPayment = DataProvider.paymentInfo(paymentMode = "WALLET")
            assertEquals(ReceiptStrategy.PreApprovedReceipt, ReceiptStrategy.getStrategy(walletPayment))

            // Test for CREDIT
            val creditPayment = DataProvider.paymentInfo(paymentMode = "CREDIT")
            assertEquals(ReceiptStrategy.PreApprovedReceipt, ReceiptStrategy.getStrategy(creditPayment))
        }

        @Test
        @DisplayName("Should return PreApprovedReceipt for cash with pre-approval")
        fun testGetStrategyReturnsPreApprovedReceiptForCashWithPreApproval() {
            // Test for cash with RIO_DELIVERY
            val rioDeliveryCashPayment = DataProvider.paymentInfo(
                customerTransactionId = "TXN_123",
                initiatedBy = "RIO_DELIVERY"
            )
            assertEquals(ReceiptStrategy.PreApprovedReceipt, ReceiptStrategy.getStrategy(rioDeliveryCashPayment))

            // Test for cash with SALESMAN_DELIVERY_BOY
            val salesmanDeliveryCashPayment = DataProvider.paymentInfo(
                customerTransactionId = "TXN_123",
                initiatedBy = "SALESMAN_DELIVERY_BOY"
            )
            assertEquals(ReceiptStrategy.PreApprovedReceipt, ReceiptStrategy.getStrategy(salesmanDeliveryCashPayment))
        }

        @Test
        @DisplayName("Should return DraftReceipt for non-digital payment types and non-pre-approved cash")
        fun testGetStrategyReturnsDraftReceiptForOtherCases() {
            // Test for non-digital payment type
            val nonDigitalPayment = DataProvider.paymentInfo(paymentMode = "CHEQUE")
            assertEquals(ReceiptStrategy.DraftReceipt, ReceiptStrategy.getStrategy(nonDigitalPayment))

            // Test for cash without pre-approved source
            val nonPreApprovedCashPayment = DataProvider.paymentInfo(
                customerTransactionId = "TXN_123"
            )
            assertEquals(ReceiptStrategy.DraftReceipt, ReceiptStrategy.getStrategy(nonPreApprovedCashPayment))

            // Test for cash with pre-approved source but bank deposit
            val bankDepositCashPayment = DataProvider.paymentInfo(
                customerTransactionId = "TXN_123",
                initiatedBy = "RIO_DELIVERY",
                isBankDeposit = true
            )
            assertEquals(ReceiptStrategy.DraftReceipt, ReceiptStrategy.getStrategy(bankDepositCashPayment))

            // Test for cash with pre-approved source but not digital receipt
            val nonDigitalReceiptCashPayment = DataProvider.paymentInfo(
                customerTransactionId = "TXN_123",
                paymentMode = "OTHER_MODE",
                initiatedBy = "RIO_DELIVERY"
            )
            assertEquals(ReceiptStrategy.DraftReceipt, ReceiptStrategy.getStrategy(nonDigitalReceiptCashPayment))
        }

        @Test
        @DisplayName("Should handle case insensitivity for payment mode")
        fun testGetStrategyHandlesCaseInsensitivityForPaymentMode() {
            // Test for lowercase payment mode
            val lowercasePaymentMode = DataProvider.paymentInfo(paymentMode = "digital_payment")
            assertEquals(ReceiptStrategy.PreApprovedReceipt, ReceiptStrategy.getStrategy(lowercasePaymentMode))

            // Test for mixed case payment mode
            val mixedCasePaymentMode = DataProvider.paymentInfo(paymentMode = "DiGiTaL_PaYmEnT")
            assertEquals(ReceiptStrategy.PreApprovedReceipt, ReceiptStrategy.getStrategy(mixedCasePaymentMode))
        }

        @Test
        @DisplayName("Should handle case insensitivity for customer transaction id and initiated by")
        fun testGetStrategyHandlesCaseInsensitivityForOtherFields() {
            // Test for lowercase customer transaction id and initiated by
            val lowercaseFields = DataProvider.paymentInfo(
                customerTransactionId = "cash",
                paymentMode = "digital_receipt",
                initiatedBy = "rio_delivery"
            )
            assertEquals(ReceiptStrategy.PreApprovedReceipt, ReceiptStrategy.getStrategy(lowercaseFields))

            // Test for mixed case customer transaction id and initiated by
            val mixedCaseFields = DataProvider.paymentInfo(
                customerTransactionId = "CaSh",
                paymentMode = "DiGiTaL_ReCeIpT",
                initiatedBy = "RiO_DeLiVeRy"
            )
            assertEquals(ReceiptStrategy.PreApprovedReceipt, ReceiptStrategy.getStrategy(mixedCaseFields))
        }

        @Test
        @DisplayName("Should handle null payment mode")
        fun testGetStrategyHandlesNullPaymentMode() {
            // Test for null payment mode
            val nullPaymentMode = DataProvider.paymentInfo(paymentMode = null)
            assertEquals(ReceiptStrategy.DraftReceipt, ReceiptStrategy.getStrategy(nullPaymentMode))
        }

        @Test
        @DisplayName("Should handle null initiated by")
        fun testGetStrategyHandlesNullInitiatedBy() {
            // Test for null initiated by
            val nullInitiatedBy = DataProvider.paymentInfo(
                customerTransactionId = "TXN_123",
                initiatedBy = null
            )
            assertEquals(ReceiptStrategy.DraftReceipt, ReceiptStrategy.getStrategy(nullInitiatedBy))
        }
    }
}
