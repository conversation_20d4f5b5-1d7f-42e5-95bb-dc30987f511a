package com.pharmeasy.service

import com.pharmeasy.data.*
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.proxy.UserProxy
import com.pharmeasy.repo.*
import com.pharmeasy.type.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito.*
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.junit.jupiter.MockitoSettings
import org.mockito.quality.Strictness
import java.time.LocalDate
import java.time.LocalDateTime
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.assertThrows
import org.mockito.ArgumentMatchers.any
import org.mockito.ArgumentMatchers.anyLong
import org.mockito.ArgumentMatchers.anyString
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.check
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.test.util.ReflectionTestUtils
import java.util.Optional
import java.math.BigDecimal

@ExtendWith(MockitoExtension::class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ChequeHandlingServiceTest {

    @Mock private lateinit var chequeHandlingRepo: ChequeHandlingRepo
    @Mock private lateinit var chequeHandlingLog: ChequeHandleLogRepo
    @Mock private lateinit var adjustmentService: AdjustmentService
    @Mock private lateinit var settlementRepo: SettlementRepo
    @Mock private lateinit var documentMasterService: DocumentMasterService
    @Mock private lateinit var checkerService: CheckerService
    @Mock private lateinit var companyService: CompanyService
    @Mock private lateinit var settlementService: SettlementService
    @Mock private lateinit var advancePaymentService: AdvancePaymentService
    @Mock private lateinit var s3FileUtilityService : S3FileUtilityService
    @Mock private lateinit var receiptService: ReceiptService
    @Mock private lateinit var retailerDebitNoteService: RetailerDebitNoteService
    @Mock private lateinit var receiptRepo: ReceiptRepo
    @Mock private lateinit var userProxy: UserProxy
    @Mock private lateinit var retailerDebitNoteSettlementMappingService: RetailerDebitNoteSettlementMappingService
    @Mock private lateinit var invoiceService: InvoiceService
    @Mock private lateinit var invoiceSettlementMappingService: InvoiceSettlementMappingService

    private lateinit var chequeHandlingService: ChequeHandlingService

    private lateinit var sampleChequeHandle: ChequeHandle
    private lateinit var sampleCheckerDetails: CheckerDetails
    private lateinit var sampleSettlement: Settlement
    private lateinit var sampleReceipt: Receipt
    private lateinit var sampleChequeHandleDto: ChequeHandleDto
    private lateinit var sampleChequeHandleLog: ChequeHandleLog
    private lateinit var sampleCompanyTenantMapping: CompanyTenantMapping

    @BeforeEach
    fun setUp() {
        advancePaymentService = mock()
        receiptService = mock()
        retailerDebitNoteService = mock()
        chequeHandlingService = ChequeHandlingService(
            dnToRetailerCBHsn = "test-hsn",
            dnToRetailerCBTax = 18,
            chequeHandlingRepo = chequeHandlingRepo,
            chequeHandlingLog = chequeHandlingLog,
            adjustmentService = adjustmentService,
            documentMasterService = documentMasterService,
            checkerService = checkerService,
            companyService = companyService,
            s3FileUtilityService = s3FileUtilityService,
            userProxy = userProxy,
            retailerDebitNoteSettlementMappingService = retailerDebitNoteSettlementMappingService,
            invoiceSettlementMappingService = invoiceSettlementMappingService
        )
        // Manually inject the field using reflection
        ReflectionTestUtils.setField(chequeHandlingService, "advancePaymentService", advancePaymentService)
        ReflectionTestUtils.setField(chequeHandlingService, "receiptService", receiptService)
        ReflectionTestUtils.setField(chequeHandlingService, "retailerDebitNoteService", retailerDebitNoteService)
        ReflectionTestUtils.setField(chequeHandlingService, "receiptRepo", receiptRepo)
        ReflectionTestUtils.setField(chequeHandlingService, "settlementRepo", settlementRepo)
        ReflectionTestUtils.setField(chequeHandlingService, "settlementService", settlementService)
        ReflectionTestUtils.setField(chequeHandlingService, "invoiceService", invoiceService)

        // Initialize test data
        sampleCheckerDetails = CheckerDetails(
            id = 1L,
            userId = "test_checker",
            userName = "Test Checker",
            companyId = 1L,
            type = CheckerType.CHECKER,
            email = "<EMAIL>",
            role = Role.APPROVER,
            isActive = true,
            priority = 1
        )
        sampleChequeHandleDto = ChequeHandleDto(
            l1 = 1L,
            s1 = "test",
            s2 = "test",
            l2 = 1L,
            s3 = "test",
            d1 = 0.0,
            ld1 = LocalDate.now(),
            ld2 = LocalDateTime.now(),
            s4 = "test",
            ch1 = ChequeHandleType.DEPOSITED,
            s5 = "",
            s6 = "",
            b1 = true,
            l3 = 1L,
            b2 = false,
            d2 = 0.0,
            pt1 = PartnerType.CUSTOMER,
            s7 = ""
        )
        sampleChequeHandle = createSampleChequeHandle()

        sampleSettlement = Settlement(
            id = 1L,
            createdOn = LocalDateTime.now(),
            updatedOn = LocalDateTime.now(),
            createdBy = "test_user",
            supplierId = 1L,
            supplierName = "Test Supplier",
            amount = 1000.00,
            paidAmount = 1000.00,
            remarks = "Test settlement",
            settlementNumber = "SET-001",
            invoices = mutableListOf(),
            creditNotes = mutableListOf(),
            paymentType = PaymentType.CHEQUE,
            paymentReference = "CHQ123",
            paymentDate = LocalDate.now(),
            partnerId = 1L,
            partnerDetailId = 1L,
            type = PartnerType.VENDOR,
            tenant = "test_tenant",
            chequeDate = LocalDate.now(),
            bankId = 1L,
            bankName = "Test Bank",
            isBounced = false,
            reversed = false,
            advancePayment = mutableListOf(),
            chargeInvoice = mutableListOf(),
            charge = false,
            receipt = listOf(),
            paymentSource = AdvancePaymentSource.SYSTEM,
            retailerDebitNotes = mutableListOf(),
            uuid = "test-uuid"
        )

        sampleReceipt = Receipt(
            id = 1L,
            createdBy = "test_user",
            updatedBy = "test_user",
            receiptNumber = "REC-001",
            amount = 1000.00,
            advanceAmount = 0.0,
            source = AdvancePaymentSource.RIO_COLLECTIONS,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            paymentTransactionId = "CHQ123",
            remarks = "",
            status = ReceiptStatus.GENERATED
        )

        sampleChequeHandleLog = (ChequeHandleLog(
            id = 1L,
            draftId = 1L,
            status = ChequeHandleType.RECEIVED,
            createdOn = LocalDateTime.now(),
            createdBy = "test_user"
        ))
        sampleCompanyTenantMapping = CompanyTenantMapping(
            id = 1L,
            companyId = 1L,
            tenant = "test_tenant",
            "test",
            1L,
            1L
        )

    }

    @Test
    fun `addChequeHandleEntry should create new cheque handle entry`() {
        // Given
        val createChequeHandleDto = CreateChequeHandleDto(
            tenant = "test_tenant",
            chequeNumber = "CHQ123",
            settlementId = 1L,
            advanceId = null,
            paymentDate = LocalDate.now()
        )

        // When
        whenever(checkerService.findCheckers(anyString(), any(), any())).thenReturn(mutableListOf(sampleCheckerDetails))
        whenever(chequeHandlingRepo.save(any())).thenReturn(sampleChequeHandle)
        chequeHandlingService.addChequeHandleEntry("test_user", createChequeHandleDto)

        // Then
        verify(chequeHandlingRepo).save(any())
        verify(chequeHandlingLog).save(any())
    }

    @Test
    fun `updateChequeStatus should throw exception when cheque not found`() {
        // Given
        val chequeUpdate = ChequeUpdateDto(
            id = 1L,
            status = ChequeHandleType.DEPOSITED,
            updatedBy = "test_user",
            updatedOn = LocalDateTime.now(),
            reason = "",
            assignTo = "test_checker",
            charge = false,
            chargeAmt = 0.0,
            bounceDate = LocalDateTime.now()
        )

        // When/Then
        assertThrows(RequestException::class.java) {
            chequeHandlingService.updateChequeStatus(chequeUpdate, "test_tenant", "<EMAIL>")
        }
    }

    @Test
    fun `updateChequeStatus should throw exception for invalid status transition`() {
        // Given
        val chequeUpdate = ChequeUpdateDto(
            id = 1L,
            status = ChequeHandleType.CLEARED, // Invalid transition from RECEIVED to CLEARED
            updatedBy = "test_user",
            updatedOn = LocalDateTime.now(),
            reason = "",
            assignTo = "test_checker",
            charge = false,
            chargeAmt = 0.0,
            bounceDate = LocalDateTime.now()
        )

        // When/Then
        assertThrows(RequestException::class.java) {
            chequeHandlingService.updateChequeStatus(chequeUpdate, "test_tenant", "<EMAIL>")
        }
    }

    @Test
    fun `validateChequeStatus should return true for matching status`() {
        // Given
        whenever(chequeHandlingRepo.get(anyLong())).thenReturn(sampleChequeHandle)

        // When
        val result = chequeHandlingService.validateChequeStatus(1L, ChequeHandleType.RECEIVED)

        // Then
        assertTrue(result)
    }

    @Test
    fun `validateChequeStatus should return true for bounce status variations`() {
        // Given
        sampleChequeHandle.status = ChequeHandleType.BOUNCE
        whenever(chequeHandlingRepo.get(anyLong())).thenReturn(sampleChequeHandle)

        // When
        val result = chequeHandlingService.validateChequeStatus(1L, ChequeHandleType.BOUNCE)

        // Then
        assertTrue(result)
    }

    @Test
    fun `validateChequeStatus should return false for non-matching status`() {
        // Given
        whenever(chequeHandlingRepo.get(anyLong())).thenReturn(sampleChequeHandle)

        // When
        val result = chequeHandlingService.validateChequeStatus(1L, ChequeHandleType.CLEARED)

        // Then
        assertFalse(result)
    }

    @Test
    fun `validateChequeStatusUpdate should return true for valid transition`() {
        // Given
        val currentStatus = ChequeHandleType.RECEIVED
        val newStatus = ChequeHandleType.DEPOSITED

        // When
        val result = chequeHandlingService.validateChequeStatusUpdate(currentStatus, newStatus)

        // Then
        assertTrue(result)
    }

    @Test
    fun `validateChequeStatusUpdate should return false for invalid transition`() {
        // Given
        val currentStatus = ChequeHandleType.RECEIVED
        val newStatus = ChequeHandleType.CLEARED

        // When
        val result = chequeHandlingService.validateChequeStatusUpdate(currentStatus, newStatus)

        // Then
        assertFalse(result)
    }

    @Test
    fun `validateNewEntry should return true for new cheque`() {
        // Given
        val testDate = LocalDate.now()
        whenever(chequeHandlingRepo.findDuplicateCheques("CHQ123", 1L, testDate, "test_tenant")).thenReturn(null)

        // When
        val result = chequeHandlingService.validateNewEntry(
            "CHQ123",
            1L,
            testDate,
            "test_tenant"
        )

        // Then
        assertTrue(result)
    }

    @Test
    fun `validateNewEntry should return false for duplicate cheque`() {
        // Given
        val testDate = LocalDate.now()
        whenever(chequeHandlingRepo.findDuplicateCheques("CHQ123", 1L, testDate, "test_tenant")).thenReturn(sampleChequeHandle)


        // When
        val result = chequeHandlingService.validateNewEntry(
            "CHQ123",
            1L,
            testDate,
            "test_tenant"
        )

        // Then
        assertFalse(result)
    }

    @Test
    fun `getOutstandingPdcAmount should return correct amount`() {
        // Given
        val expectedAmount = BigDecimal("1000.00")
        whenever(chequeHandlingRepo.getTotalOutstandingChequeAmount(anyLong(), anyString()))
            .thenReturn(expectedAmount)

        // When
        val result = chequeHandlingService.getOutstandingPdcAmount("test_tenant", 1L)

        // Then
        assertEquals(expectedAmount, result)
    }

    @Test
    fun `getOutstandingPdcAmount should return zero when no amount found`() {
        // Given
        whenever(chequeHandlingRepo.getTotalOutstandingChequeAmount(anyLong(), anyString()))
            .thenReturn(null)

        // When
        val result = chequeHandlingService.getOutstandingPdcAmount("test_tenant", 1L)

        // Then
        assertEquals(BigDecimal.ZERO, result)
    }

    @Test
    fun `getDueInvoiceOutstandingPdcAmount should return correct amount`() {
        // Given
        val expectedAmount = BigDecimal("500.00")
        whenever(chequeHandlingRepo.getTotalDueOutstandingChequeAmount(anyLong(), anyString()))
            .thenReturn(expectedAmount)

        // When
        val result = chequeHandlingService.getDueInvoiceOutstandingPdcAmount("test_tenant", 1L)

        // Then
        assertEquals(expectedAmount, result)
    }

    @Test
    fun `getDueInvoiceOutstandingPdcAmount should return zero when no amount found`() {
        // Given
        whenever(chequeHandlingRepo.getTotalDueOutstandingChequeAmount(anyLong(), anyString()))
            .thenReturn(null)

        // When
        val result = chequeHandlingService.getDueInvoiceOutstandingPdcAmount("test_tenant", 1L)

        // Then
        assertEquals(BigDecimal.ZERO, result)
    }

    @Test
    fun `getChequeStatusTooltip should return empty list for empty input`() {
        // Given
        val emptyCheques = emptyList<ChequeHandleDto>()

        // When
        val result = chequeHandlingService.getChequeStatusTooltip(emptyCheques)

        // Then
        assertTrue(result.isEmpty())
    }

    @Test
    fun `getChequeStatusTooltip should return correct tooltip for cheque with status logs`() {
        // Given
        var chequeDto = sampleChequeHandleDto
        chequeDto.status = ChequeHandleType.RECEIVED
        chequeDto.bounceDate = LocalDateTime.now()
        chequeDto.chequeNum = "CHQ123"
        chequeDto.id = 1L

        val statusLog = ChequeHandleLog(
            id = 1L,
            draftId = 1L,
            status = ChequeHandleType.RECEIVED,
            createdOn = LocalDateTime.now(),
            createdBy = "test_user"
        )
        whenever(chequeHandlingLog.getByDraftIds(anyList())).thenReturn(listOf(statusLog))

        // When
        val result = chequeHandlingService.getChequeStatusTooltip(listOf(chequeDto))

        // Then
        assertEquals(1, result.size)
        assertNotNull(result[0].recievedOn)
    }

    @Test
    fun `getTotalBounceNumber should return correct count`() {
        // Given
        val expectedCount = 5L
        whenever(companyService.findTenants("test_tenant")).thenReturn(mutableListOf("test_tenant"))
        whenever(chequeHandlingRepo.getPastBounceNumberV2(anyList(), anyLong())).thenReturn(expectedCount)

        // When
        val result = chequeHandlingService.getTotalBounceNumber("test_tenant", 1L)

        // Then
        assertEquals(expectedCount, result)
    }

    @Test
    fun `updateChequeStatus should handle cleared status with advance payment`() {
        // Given
        sampleChequeHandle.status = ChequeHandleType.DEPOSITED
        sampleChequeHandle.advancePaymentId = 1L
        whenever(chequeHandlingRepo.findById(any())).thenReturn(Optional.of(sampleChequeHandle))
        whenever(chequeHandlingRepo.getChequeHandlingDataById(anyLong())).thenReturn(sampleChequeHandleDto)
        whenever(chequeHandlingLog.getByDraftIdAndStatus(1L, ChequeHandleType.CLEARED)).thenReturn(emptyList())
        whenever(settlementRepo.findById(anyLong())).thenReturn(Optional.of(sampleSettlement))
        whenever(receiptService.checkExistingReceipt(sampleSettlement)).thenReturn(sampleReceipt)

        val chequeUpdate = ChequeUpdateDto(
            id = 1L,
            status = ChequeHandleType.CLEARED,
            updatedBy = "test_user",
            updatedOn = LocalDateTime.now(),
            reason = "",
            assignTo = "test_checker",
            charge = false,
            chargeAmt = 0.0,
            bounceDate = LocalDateTime.now()
        )

        // When
        val result = chequeHandlingService.updateChequeStatus(chequeUpdate, "test_tenant", "<EMAIL>")

        // Then
        assertEquals(ChequeHandleType.CLEARED, result?.status)
        assertTrue(result?.settled == true)
        verify(advancePaymentService).changeAdvancePaymentStatus(1L, Status.APPROVED)
    }

    @Test
    fun `updateChequeStatus should handle cleared status with RIO collections receipt`() {
        // Given
        sampleChequeHandle.status = ChequeHandleType.DEPOSITED
        sampleReceipt.source = AdvancePaymentSource.RIO_COLLECTIONS
        sampleReceipt.advanceAmount = 100.0
        whenever(chequeHandlingRepo.findById(any())).thenReturn(Optional.of(sampleChequeHandle))
        whenever(chequeHandlingRepo.getChequeHandlingDataById(anyLong())).thenReturn(sampleChequeHandleDto)
        whenever(chequeHandlingLog.getByDraftIdAndStatus(1L, ChequeHandleType.CLEARED)).thenReturn(emptyList())
        whenever(settlementRepo.findById(anyLong())).thenReturn(Optional.of(sampleSettlement))
        whenever(receiptService.checkExistingReceipt(sampleSettlement)).thenReturn(sampleReceipt)

        val chequeUpdate = ChequeUpdateDto(
            id = 1L,
            status = ChequeHandleType.CLEARED,
            updatedBy = "test_user",
            updatedOn = LocalDateTime.now(),
            reason = "",
            assignTo = "test_checker",
            charge = false,
            chargeAmt = 0.0,
            bounceDate = LocalDateTime.now()
        )

        // When
        val result = chequeHandlingService.updateChequeStatus(chequeUpdate, "test_tenant", "<EMAIL>")

        // Then
        assertEquals(ChequeHandleType.CLEARED, result?.status)
        assertTrue(result?.settled == true)
    }

    @Test
    fun `updateChequeStatus should handle cancelled status with settlement`() {
        // Given
        sampleChequeHandle.status = ChequeHandleType.RECEIVED
        sampleChequeHandle.settlementId = 1L
        whenever(chequeHandlingRepo.findById(any())).thenReturn(Optional.of(sampleChequeHandle))
        whenever(settlementRepo.findById(anyLong())).thenReturn(Optional.of(sampleSettlement))
        whenever(settlementRepo.save(sampleSettlement)).thenReturn(sampleSettlement)
        val chequeUpdate = ChequeUpdateDto(
            id = 1L,
            status = ChequeHandleType.CANCELLED,
            updatedBy = "test_user",
            updatedOn = LocalDateTime.now(),
            reason = "",
            assignTo = "test_checker",
            charge = false,
            chargeAmt = 0.0,
            bounceDate = LocalDateTime.now()
        )

        // When
        val result = chequeHandlingService.updateChequeStatus(chequeUpdate, "test_tenant", "<EMAIL>")

        // Then
        assertEquals(ChequeHandleType.CANCELLED, result?.status)
        verify(settlementRepo).save(sampleSettlement)
    }

    @Test
    fun `updateChequeStatus should handle cancelled status with advance payment`() {
        // Given
        sampleChequeHandle.status = ChequeHandleType.RECEIVED
        sampleChequeHandle.advancePaymentId = 1L
        whenever(chequeHandlingRepo.findById(any())).thenReturn(Optional.of(sampleChequeHandle))
        whenever(settlementRepo.findById(anyLong())).thenReturn(Optional.of(sampleSettlement))
        whenever(settlementRepo.save(sampleSettlement)).thenReturn(sampleSettlement)

        val chequeUpdate = ChequeUpdateDto(
            id = 1L,
            status = ChequeHandleType.CANCELLED,
            updatedBy = "test_user",
            updatedOn = LocalDateTime.now(),
            reason = "",
            assignTo = "test_checker",
            charge = false,
            chargeAmt = 0.0,
            bounceDate = LocalDateTime.now()
        )

        // When
        val result = chequeHandlingService.updateChequeStatus(chequeUpdate, "test_tenant", "<EMAIL>")

        // Then
        assertEquals(ChequeHandleType.CANCELLED, result?.status)
    }

    @Test
    fun `updateBouncedStatus should handle company not found`() {
        // Given
        whenever(companyService.getCompanyByTenant(anyString())).thenReturn(null)
        
        // When/Then
        assertThrows(RequestException::class.java) {
            chequeHandlingService.updateBouncedStatus("test_user", true, 1L, "test_tenant", sampleCheckerDetails)
        }
    }

    @Test
    fun `updateBulkChequeStatus should handle cleared status`() {
        // Given
        sampleChequeHandle.status = ChequeHandleType.RECEIVED
        whenever(chequeHandlingRepo.getChequeForStatusChange(anyString(), anyLong(), anyString())).thenReturn(sampleChequeHandle)
        whenever(chequeHandlingRepo.updateChequeStatus(anyLong(), any(), any(), anyString(), anyString())).thenReturn(1)

        val chequeUpdate = ChequeHandleStatusChangeDto(
            chequeNum = "CHQ123",
            pdi = 1L,
            status = ChequeHandleType.CLEARED,
            refDate = LocalDate.now(),
            chargeAmt = 0.0
        )

        // When
        val result = chequeHandlingService.updateBulkChequeStatus(chequeUpdate)

        // Then
        assertEquals("success", result.message)
        verify(chequeHandlingLog).save(any())
    }

    @Test
    fun `validateChequeStatus should handle bounce status`() {
        // Given
        sampleChequeHandle.status = ChequeHandleType.BOUNCE
        whenever(chequeHandlingRepo.get(anyLong())).thenReturn(sampleChequeHandle)

        // When
        val result = chequeHandlingService.validateChequeStatus(1L, ChequeHandleType.BOUNCE)

        // Then
        assertTrue(result)
    }

    @Test
    fun `validateChequeStatus should handle after clear bounce status`() {
        // Given
        sampleChequeHandle.status = ChequeHandleType.AFTER_CLEAR_BOUNCE
        whenever(chequeHandlingRepo.get(anyLong())).thenReturn(sampleChequeHandle)

        // When
        val result = chequeHandlingService.validateChequeStatus(1L, ChequeHandleType.BOUNCE)

        // Then
        assertTrue(result)
    }

    @Test
    fun `getOutstandingPdcAmount should return zero for no amount`() {
        // Given
        whenever(chequeHandlingRepo.getTotalOutstandingChequeAmount(anyLong(), anyString())).thenReturn(null)

        // When
        val result = chequeHandlingService.getOutstandingPdcAmount("test_tenant", 1L)

        // Then
        assertEquals(BigDecimal.ZERO, result)
    }

    @Test
    fun `getDueInvoiceOutstandingPdcAmount should return zero for no amount`() {
        // Given
        whenever(chequeHandlingRepo.getTotalDueOutstandingChequeAmount(anyLong(), anyString())).thenReturn(null)

        // When
        val result = chequeHandlingService.getDueInvoiceOutstandingPdcAmount("test_tenant", 1L)

        // Then
        assertEquals(BigDecimal.ZERO, result)
    }

    @Test
    fun `getChequeStatusTooltip should return empty list for empty cheques`() {
        // When
        val result = chequeHandlingService.getChequeStatusTooltip(emptyList())

        // Then
        assertTrue(result.isEmpty())
    }

    @Test
    fun `getChequeStatusTooltip should return tooltips with all status dates`() {
        // Given
        val chequeDto = ChequeHandleDto(
            l1 = 1L,
            s1 = "test",
            s2 = "test",
            l2 = 1L,
            s3 = "test",
            d1 = 0.0,
            ld1 = LocalDate.now(),
            ld2 = LocalDateTime.now(),
            s4 = "test",
            ch1 = ChequeHandleType.RECEIVED,
            s5 = "",
            s6 = "",
            b1 = true,
            l3 = 1L,
            b2 = false,
            d2 = 0.0,
            pt1 = PartnerType.CUSTOMER,
            s7 = ""
        )
        val receivedLog = ChequeHandleLog(
            id = 1L,
            draftId = 1L,
            status = ChequeHandleType.RECEIVED,
            createdOn = LocalDateTime.now(),
            createdBy = "test_user"
        )
        val depositedLog = ChequeHandleLog(
            id = 2L,
            draftId = 1L,
            status = ChequeHandleType.DEPOSITED,
            createdOn = LocalDateTime.now(),
            createdBy = "test_user"
        )
        val clearedLog = ChequeHandleLog(
            id = 3L,
            draftId = 1L,
            status = ChequeHandleType.CLEARED,
            createdOn = LocalDateTime.now(),
            createdBy = "test_user"
        )
        whenever(chequeHandlingLog.getByDraftIds(listOf(1L))).thenReturn(listOf(receivedLog, depositedLog, clearedLog))

        // When
        val result = chequeHandlingService.getChequeStatusTooltip(listOf(chequeDto))

        // Then
        assertEquals(1, result.size)
        assertNotNull(result[0].recievedOn)
        assertNotNull(result[0].depositedOn)
        assertNotNull(result[0].clearedOn)
    }

    @Test
    fun `getAggregatedChequeData should return data for tenant`() {
        // Given
        val expectedData = AggregatedChequeHandleDto(
            totalRecievedCheques = 10L,
            totalRecievedChequesAmount = BigDecimal("10000.00"),
            totalDepositedCheques = 5L,
            totalDepositedChequesAmount = BigDecimal("5000.00")
        )
        whenever(companyService.findTenants("test_tenant")).thenReturn(mutableListOf("test_tenant"))
        whenever(chequeHandlingRepo.getAggregatedChequeDataV2(mutableListOf("test_tenant"), 1L)).thenReturn(expectedData)

        // When
        val result = chequeHandlingService.getAggregatedChequeData("test_tenant", null, 1L)

        // Then
        assertEquals(expectedData, result)
    }

    @Test
    fun `getChequeUrl should throw exception when tenant not found`() {
        // Given
        whenever(companyService.findTenants("test_tenant")).thenReturn(mutableListOf())

        // When/Then
        assertThrows(RequestException::class.java) {
            chequeHandlingService.getChequeUrl("test_tenant", null, LocalDate.now(), LocalDate.now(), ChequeHandleType.RECEIVED)
        }
    }

    @Test
    fun `should throw RequestException if user is a checker`() {
        whenever(checkerService.findCheckers(org.mockito.kotlin.any(), anyOrNull(), anyOrNull()))
            .thenReturn(mutableListOf(sampleChecker("user1")))

        assertThrows<RequestException> {
            chequeHandlingService.checkerCheck("user1", "tenant1")
        }
    }

    @Test
    fun `should return checker if user is not a checker`() {
        whenever(checkerService.findCheckers(org.mockito.kotlin.any(), anyOrNull(), anyOrNull()))
            .thenReturn(mutableListOf(sampleChecker("user2")))

        val result = chequeHandlingService.checkerCheck("user1", "tenant1")
        assertNotNull(result)
        assertEquals("user2", result?.userId)
    }

    @Test
    fun `should return null if no checkers found`() {
        whenever(checkerService.findCheckers(org.mockito.kotlin.any(), anyOrNull(), anyOrNull()))
            .thenReturn(mutableListOf())

        val result = chequeHandlingService.checkerCheck("user1", "tenant1")
        assertNull(result)
    }

    @Test
    fun `should update cheque checker successfully`() {
        sampleChequeHandle.status= ChequeHandleType.PENDING_APPROVAL
        whenever(checkerService.findCheckers(
            anyString(),
            any(),
            any()
        )).thenReturn(mutableListOf(sampleChecker("user1")))
        whenever(chequeHandlingRepo.get(1L)).thenReturn(sampleChequeHandle)
        whenever(companyService.getCompanyTenantMappingObject(sampleChequeHandle.tenant)).thenReturn(sampleCompanyTenantMapping)
        whenever(checkerService.getCheckerByCompanyId(1L)).thenReturn(mutableListOf(sampleChecker("user1")))
        whenever(chequeHandlingRepo.save(sampleChequeHandle)).thenReturn(sampleChequeHandle)

        val result = chequeHandlingService.updateChequeChecker(1L, 1L, sampleChequeHandle.tenant, "user1")

        assertEquals(success, result)
        verify(chequeHandlingRepo).save(org.mockito.kotlin.any())
    }

    @Test
    fun `should throw if no checker found`() {
        whenever(checkerService.findCheckers(
            anyString(),
            any(),
            any()
        )).thenReturn(mutableListOf())
        assertThrows<RequestException> {
            chequeHandlingService.updateChequeChecker(1L, 2L, "tenant1", "checker1")
        }
    }

    @Test
    fun `should throw if cheque not found`() {
        whenever(checkerService.findCheckers(
            anyString(),any(),any()
        )).thenReturn(mutableListOf())
        whenever(chequeHandlingRepo.get(1L)).thenReturn(null)
        assertThrows<RequestException> {
            chequeHandlingService.updateChequeChecker(1L, 2L, "tenant1", "checker1")
        }
    }

    @Test
    fun `should throw if cheque not in PENDING_APPROVAL`() {
        val cheque = sampleChequeHandle.copy(status = ChequeHandleType.RECEIVED)
        whenever(checkerService.findCheckers(
            anyString(),
            any(),
            any()
        )).thenReturn(mutableListOf(mock()))
        whenever(chequeHandlingRepo.get(1L)).thenReturn(cheque)
        assertThrows<RequestException> {
            chequeHandlingService.updateChequeChecker(1L, 2L, "tenant1", "checker1")
        }
    }

    @Test
    fun `should throw if company not found`() {
        whenever(checkerService.findCheckers(
            anyString(),
            any(),
            any()
        )).thenReturn(mutableListOf(mock()))
        whenever(chequeHandlingRepo.get(1L)).thenReturn(sampleChequeHandle)
        whenever(companyService.getCompanyTenantMappingObject("test_tenant")).thenReturn(null)
        assertThrows<RequestException> {
            chequeHandlingService.updateChequeChecker(1L, 2L, "tenant1", "checker1")
        }
    }

    @Test
    fun `should throw if user is not creator or checker`() {
        whenever(checkerService.findCheckers(
            anyString(),any(),any()
        )).thenReturn(mutableListOf())
        whenever(chequeHandlingRepo.get(1L)).thenReturn(sampleChequeHandle)
        whenever(companyService.getCompanyTenantMappingObject("test_tenant")).thenReturn(sampleCompanyTenantMapping)
        whenever(checkerService.getCheckerByCompanyId(1L)).thenReturn(mutableListOf())
        assertThrows<RequestException> {
            chequeHandlingService.updateChequeChecker(1L, 2L, "tenant1", "notallowed")
        }
    }

    @Test
    fun `should throw RequestException when tenant not found`() {
        whenever(companyService.findTenants("test_tenant")).thenReturn(mutableListOf())

        assertThrows<RequestException> {
            chequeHandlingService.getChequeUrl("tenant1", null, LocalDate.now(), LocalDate.now(), ChequeHandleType.RECEIVED)
        }
    }

    @Test
    fun `should reverse settlement and invoice successfully`() {
        val invoiceList = listOf(mock<BkInvoice>())
        val settlementInvoiceList = listOf(
            InvoiceSettlement(
                id = InvoiceSettlementId(1L, 1L),
                amount = BigDecimal("100.00"),
                paidAmount = BigDecimal("100.00"),
                invoiceStatus = InvoiceStatus.PAID
            )
        )

        whenever(chequeHandlingRepo.findById(1L)).thenReturn(Optional.of(sampleChequeHandle))
        whenever(settlementRepo.findById(1L)).thenReturn(Optional.of(sampleSettlement))
        whenever(invoiceSettlementMappingService.getInvoicesForSettlement(1L)).thenReturn(invoiceList)
        whenever(invoiceSettlementMappingService.getSettlementInvoiceBySettlementId(1L)).thenReturn(settlementInvoiceList)
        whenever(settlementRepo.save(sampleSettlement)).thenReturn(sampleSettlement)

        chequeHandlingService.reverseSettlementAndInvoice("user1", 1L)

        verify(invoiceService).updateInvoiceAmtForBounceCheque(eq(invoiceList), org.mockito.kotlin.any())
        verify(chequeHandlingRepo).save(check {
            assertEquals(ChequeHandleType.CANCELLED, it.status)
        })
    }

    @Test
    fun `getChequeData should return pagination dto with user names`() {
        // Given
        val page = 0
        val size = 10
        val tenant = "test_tenant"
        val ds: String? = null
        val partnerIds: List<Long>? = null
        val chequeNum = "CHQ123"
        val settlementNum = "SETT123"
        val status = ChequeHandleType.DEPOSITED
        val bankName = "Test Bank"
        val from = LocalDate.now().minusDays(7)
        val to = LocalDate.now()
        val partnerDetailId = 1L
        val advancePaymentNumber = "ADV123"
        val distributorId: Long? = null

        val pagination = PageRequest.of(page, size)
        val tenantList: MutableList<String?> = mutableListOf("test_tenant")
        val pageData = PageImpl(listOf(sampleChequeHandleDto), pagination, 1)
        val userDto = User("1L", "test_checker", "Test Checker", "")

        whenever(userProxy.getUserList(listOf("test_checker"))).thenReturn(listOf(userDto))
        whenever(companyService.getCompanyTenantMappingObject(tenant)).thenReturn(sampleCompanyTenantMapping)
        whenever(companyService.findTenants(tenant)).thenReturn(tenantList)
        whenever(chequeHandlingRepo.getChequeHandlingDataWithoutIdsV2(
            chequeNum,
            settlementNum,
            advancePaymentNumber,
            status,
            bankName,
            from,
            to,
            tenantList,
            partnerDetailId,
            pagination
        )).thenReturn(pageData)
        whenever(userProxy.getUserList(listOf("test_checker"))).thenReturn(listOf(userDto))
        whenever(chequeHandlingService.getChequeStatusTooltip(pageData.content)).thenReturn(emptyList())

        // When
        val result = chequeHandlingService.getChequeData(
            page,
            size,
            tenant,
            ds,
            partnerIds,
            chequeNum,
            settlementNum,
            status,
            bankName,
            from,
            to,
            partnerDetailId,
            advancePaymentNumber,
            distributorId
        )

        // Then
        assertEquals(1, result.elements)
        assertFalse(result.hasPrevious == true)
        assertFalse(result.hasNext == true)
    }

    @Test
    fun `updateBulkChequeStatus should throw if status is same as current`() {
        // Given
        val chequeUpdateDto = ChequeHandleStatusChangeDto(
            chequeNum = "CHQ123",
            pdi = 1L,
            receiptId = "REC-001",
            status = ChequeHandleType.RECEIVED
        )
        val chequeHandle = sampleChequeHandle.copy(status = ChequeHandleType.RECEIVED)

        whenever(chequeHandlingRepo.getChequeForStatusChange(anyString(), anyLong(), anyString())).thenReturn(chequeHandle)

        // Then
        assertThrows<RequestException> {
            chequeHandlingService.updateBulkChequeStatus(chequeUpdateDto)
        }
    }

    @Test
    fun `updateBulkChequeStatus should throw if trying to clear non-RECEIVED cheque`() {
        // Given
        val chequeUpdateDto = ChequeHandleStatusChangeDto(
            chequeNum = "CHQ123",
            pdi = 1L,
            receiptId = "REC-001",
            status = ChequeHandleType.CLEARED
        )
        val chequeHandle = sampleChequeHandle.copy(status = ChequeHandleType.BOUNCE)

        whenever(chequeHandlingRepo.getChequeForStatusChange(anyString(), anyLong(), anyString())).thenReturn(chequeHandle)

        // Then
        assertThrows<RequestException> {
            chequeHandlingService.updateBulkChequeStatus(chequeUpdateDto)
        }
    }

    private fun sampleChecker(userId: String = "user1") = CheckerDetails(
        id = 1L,
        companyId = 1L,
        userName = "Test User",
        userId = userId,
        type = CheckerType.CHECKER,
        email = "<EMAIL>",
        role = Role.APPROVER,
        isActive = true,
        priority = 0
    )

    private fun createSampleChequeHandle(): ChequeHandle {
        return ChequeHandle(
            tenant = "test_tenant",
            chequeNum = "CHQ123",
            settlementId = 1L,
            assignTo = "test_checker",
            status = ChequeHandleType.RECEIVED,
            inApprove = false,
            settled = false,
            advancePaymentId = null
        ).apply {
            id = 1L
            createdBy = "test_user"
            updatedBy = "test_user"
            createdOn = LocalDateTime.now()
            updatedOn = LocalDateTime.now()
        }
    }
}
