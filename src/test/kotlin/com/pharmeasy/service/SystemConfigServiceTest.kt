package com.pharmeasy.service

import com.pharmeasy.data.CheckerDetails
import com.pharmeasy.data.CompanyTenantMapping
import com.pharmeasy.data.SystemConfig
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.UpdateConfigDto
import com.pharmeasy.model.debitnote.PartnerInfoDto
import com.pharmeasy.repo.CompanyTenantMappingRepo
import com.pharmeasy.repo.ConfigDataLinksRepo
import com.pharmeasy.repo.SystemConfigRepo
import com.pharmeasy.type.CheckerType
import com.pharmeasy.type.NoteTypes
import com.pharmeasy.type.RecipientType
import com.pharmeasy.type.Role
import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import java.math.BigDecimal
import java.time.LocalDateTime

@ExtendWith(MockKExtension::class)
class SystemConfigServiceTest {

    private lateinit var systemConfigRepo: SystemConfigRepo
    private lateinit var companyService: CompanyService
    private lateinit var companyTenantMappingRepo: CompanyTenantMappingRepo
    private lateinit var checkerService: CheckerService
    private lateinit var s3FileUtilityService: S3FileUtilityService
    private lateinit var vendorFileService: VendorFileService
    private lateinit var configDataLinkRepo: ConfigDataLinksRepo
    private lateinit var cnConfigService: CnConfigService

    private lateinit var systemConfigService: SystemConfigService

    // Sample test data
    private lateinit var sampleSystemConfig: SystemConfig
    private lateinit var sampleCompanyTenantMapping: CompanyTenantMapping
    private lateinit var sampleUpdateConfigDto: UpdateConfigDto
    private lateinit var samplePartnerInfoDto: PartnerInfoDto
    private lateinit var sampleCheckerDetails: CheckerDetails

    @BeforeEach
    fun setUp() {
        // Initialize mocks
        systemConfigRepo = mockk(relaxed = true)
        companyService = mockk(relaxed = true)
        companyTenantMappingRepo = mockk(relaxed = true)
        checkerService = mockk(relaxed = true)
        s3FileUtilityService = mockk(relaxed = true)
        vendorFileService = mockk(relaxed = true)
        configDataLinkRepo = mockk(relaxed = true)
        cnConfigService = mockk(relaxed = true)
        
        systemConfigService = spyk(SystemConfigService())

        // Manually inject dependencies
        val serviceClass = SystemConfigService::class.java
        serviceClass.getDeclaredField("systemConfigRepo").apply { isAccessible = true; set(systemConfigService, systemConfigRepo) }
        serviceClass.getDeclaredField("companyService").apply { isAccessible = true; set(systemConfigService, companyService) }
        serviceClass.getDeclaredField("companyTenantMappingRepo").apply { isAccessible = true; set(systemConfigService, companyTenantMappingRepo) }
        serviceClass.getDeclaredField("checkerService").apply { isAccessible = true; set(systemConfigService, checkerService) }
        serviceClass.getDeclaredField("s3FileUtilityService").apply { isAccessible = true; set(systemConfigService, s3FileUtilityService) }
        serviceClass.getDeclaredField("vendorFileService").apply { isAccessible = true; set(systemConfigService, vendorFileService) }
        serviceClass.getDeclaredField("configDataLinkRepo").apply { isAccessible = true; set(systemConfigService, configDataLinkRepo) }
        serviceClass.getDeclaredField("cnConfigService").apply { isAccessible = true; set(systemConfigService, cnConfigService) }

        // Initialize test data
        sampleSystemConfig = SystemConfig(
            id = 1L,
            createdAt = LocalDateTime.now(),
            createdBy = "SYSTEM",
            partnerId = 123L,
            partnerName = "Test Vendor",
            documentType = NoteTypes.SR_ACCEPTED,
            conversionDay = 10,
            conversionPercentage = BigDecimal("100.00"),
            isAutoCN = true,
            isAutoSettlement = false,
            isApproved = true,
            approvedBy = "SYSTEM",
            approvedAt = LocalDateTime.now(),
            companyId = 456L,
            isAutoEmail = false,
            deletedAt = null,
            isDynamicPercentage = false,
            partnerDetailId = 789L,
            cnConversionLimit = 30000.0,
            cnAutoSettlementLimit = 30000.0
        )

        sampleCompanyTenantMapping = CompanyTenantMapping(
            id = 1L,
            companyId = 456L,
            tenant = "test_tenant",
            tenantName = "test_tenant",
            theaId = 1,
            partnerDetailId = 123L
        )

        sampleUpdateConfigDto = UpdateConfigDto(
            docType = NoteTypes.SR_ACCEPTED,
            conversionDay = 10,
            conversionPercentage = BigDecimal("100.00"),
            autoCN = true,
            isDynamicPercentage = false,
            recipientType = RecipientType.INCLUDE,
            partners = mutableListOf(
                PartnerInfoDto(
                    partnerId = 123L,
                    supplierName = "Test Vendor",
                    partnerDetailId = 789L
                )
            )
        )

        samplePartnerInfoDto = PartnerInfoDto(
            partnerId = 123L,
            supplierName = "Test Vendor",
            partnerDetailId = 789L
        )

        sampleCheckerDetails = CheckerDetails(
            id = 1L,
            userId = "test_user",
            userName = "Test User",
            companyId = 456L,
            type = CheckerType.CHECKER,
            email = "",
            role = Role.APPROVER,
            isActive = true,
            priority = 0
        )
    }
    @Test
    fun `updateConfig should throw exception when checker tries to update`() {
        // Given
        every { checkerService.findCheckers(any(), any(), any()) } returns mutableListOf(sampleCheckerDetails)

        // When/Then
        assertThrows(RequestException::class.java) {
            systemConfigService.updateConfig(
                userId = "test_user",
                userName = "Test User",
                tenant = "test_tenant",
                ds = null,
                updateConfigDto = sampleUpdateConfigDto
            )
        }
    }
    @Test
    fun `getPartnerConfig should return existing config`() {
        // Given
        val page = PageImpl(listOf(sampleSystemConfig), PageRequest.of(0, 1), 1)
        every { systemConfigRepo.getConfigForPartnerById(any(), any(), any(), any(), any()) } returns page

        // When
        val result = systemConfigService.getPartnerConfig(
            partnerId = 123L,
            partnerName = "Test Vendor",
            companyId = 456L,
            docType = NoteTypes.SR_ACCEPTED,
            tenant = "test_tenant",
            partnerDetailId = 789L
        )

        // Then
        assertEquals(sampleSystemConfig, result)
        verify { systemConfigRepo.getConfigForPartnerById(any(), any(), any(), any(), any()) }
    }
} 
