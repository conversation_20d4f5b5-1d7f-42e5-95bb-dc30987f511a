package com.pharmeasy.service

import com.pharmeasy.data.RetailerDebitNote
import com.pharmeasy.data.RetailerDebitNoteSettlementMapping
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.InvoiceStatus
import com.pharmeasy.repo.RetailerDebitNoteRepo
import com.pharmeasy.stream.InvoiceSettlementUpdateHandler
import com.pharmeasy.type.DnType
import com.pharmeasy.utils.DataProvider
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertSame
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.data.repository.findByIdOrNull
import org.springframework.test.util.ReflectionTestUtils
import java.time.LocalDateTime

class RetailerDebitNoteServiceTest {

    private lateinit var retailerDebitNoteService: RetailerDebitNoteService
    private lateinit var retailerDebitNoteSettlementMappingService: RetailerDebitNoteSettlementMappingService
    private lateinit var retailerDebitNoteRepo: RetailerDebitNoteRepo
    private lateinit var invoiceSettlementUpdateHandler: InvoiceSettlementUpdateHandler

    @BeforeEach
    fun setUp() {
        // Create mocks
        retailerDebitNoteSettlementMappingService = mockk(relaxed = true)
        retailerDebitNoteRepo = mockk(relaxed = true)
        invoiceSettlementUpdateHandler = mockk(relaxed = true)

        // Create service with constructor-injected dependency
        retailerDebitNoteService = RetailerDebitNoteService(
            dnToRetailerCBHsn = "test-hsn",
            dnToRetailerCBTemplate = "test-template",
            dnToRetailerCBTax = 18,
            retailIoVersion = "1.0",
            retailIoSource = "test-source",
            retailIoKey = "test-key",
            retailerDebitNoteSettlementMappingService = retailerDebitNoteSettlementMappingService
        )

        // Inject other dependencies using reflection
        ReflectionTestUtils.setField(retailerDebitNoteService, "retailerDebitNoteRepo", retailerDebitNoteRepo)
        ReflectionTestUtils.setField(retailerDebitNoteService, "invoiceSettlementUpdateHandler", invoiceSettlementUpdateHandler)
    }

    @Test
    @DisplayName("Service should be initialized with correct properties")
    fun testServiceInitialization() {
        // Assert
        assertEquals("test-hsn", ReflectionTestUtils.getField(retailerDebitNoteService, "dnToRetailerCBHsn"))
        assertEquals("test-template", ReflectionTestUtils.getField(retailerDebitNoteService, "dnToRetailerCBTemplate"))
        assertEquals(18, ReflectionTestUtils.getField(retailerDebitNoteService, "dnToRetailerCBTax"))
        assertEquals("1.0", ReflectionTestUtils.getField(retailerDebitNoteService, "retailIoVersion"))
        assertEquals("test-source", ReflectionTestUtils.getField(retailerDebitNoteService, "retailIoSource"))
        assertEquals("test-key", ReflectionTestUtils.getField(retailerDebitNoteService, "retailIoKey"))
        assertSame(retailerDebitNoteSettlementMappingService, ReflectionTestUtils.getField(retailerDebitNoteService, "retailerDebitNoteSettlementMappingService"))
    }

    @Nested
    @DisplayName("getDebitNoteById Tests")
    inner class GetDebitNoteByIdTests {

        @Test
        @DisplayName("getDebitNoteById should return debit note when found")
        fun testGetDebitNoteByIdFound() {
            // Arrange
            val id = 1L
            val debitNote = RetailerDebitNote(
                createdBy = "test-user",
                updatedBy = "test-user",
                remarks = "Test debit note",
                status = InvoiceStatus.PENDING,
                type = DnType.ADHOC,
                amount = 1000.0,
                amountReceived = 0.0,
                taxableValue = 900.0,
                taxAmount = 100.0,
                documentNumber = "DN-001",
                partnerDetailId = 1L,
                partnerId = 1L,
                partnerName = "Test Partner",
                tenant = "th001",
                companyId = 1L
            ).apply { this.id = id }

            every { retailerDebitNoteRepo.findByIdOrNull(id) } returns debitNote

            // Act
            val result = retailerDebitNoteService.getDebitNoteById(id)

            // Assert
            assertNotNull(result)
            assertSame(debitNote, result)
            verify { retailerDebitNoteRepo.findByIdOrNull(id) }
        }

        @Test
        @DisplayName("getDebitNoteById should return null when not found")
        fun testGetDebitNoteByIdNotFound() {
            // Arrange
            val id = 1L

            every { retailerDebitNoteRepo.findByIdOrNull(id) } returns null

            // Act
            val result = retailerDebitNoteService.getDebitNoteById(id)

            // Assert
            assertEquals(null, result)
            verify { retailerDebitNoteRepo.findByIdOrNull(id) }
        }
    }

    @Nested
    @DisplayName("updateDNSettlement Tests")
    inner class UpdateDNSettlementTests {

        @Test
        @DisplayName("updateDNSettlement should update debit note and create mapping")
        fun testUpdateDNSettlement() {
            // Arrange
            val debitNoteId = 1L
            val settlementId = 1L

            val debitNote = RetailerDebitNote(
                createdBy = "test-user",
                updatedBy = "test-user",
                remarks = "Test debit note",
                status = InvoiceStatus.PENDING,
                type = DnType.ADHOC,
                amount = 1000.0,
                amountReceived = 0.0,
                taxableValue = 900.0,
                taxAmount = 100.0,
                documentNumber = "DN-001",
                partnerDetailId = 1L,
                partnerId = 1L,
                partnerName = "Test Partner",
                tenant = "th001",
                companyId = 1L
            ).apply { this.id = debitNoteId }

            val settlementDebitNote = debitNote.copy().apply { 
                this.id = debitNoteId
                this.amountReceived = 500.0 
            }

            val settlement = DataProvider.settlement(
                id = settlementId,
                createdBy = "test-user",
                retailerDebitNotes = mutableListOf(settlementDebitNote)
            )

            val settlementMapping = DataProvider.draftReceiptEntityMapping(
                entityTxAmount = 500.0
            )

            val savedDebitNote = debitNote.copy().apply { 
                this.id = debitNoteId
                this.amountReceived = 500.0
                this.status = InvoiceStatus.PARTIAL_PAID
                this.updatedBy = "test-user"
                this.updatedOn = LocalDateTime.now()
            }

            val savedMapping = mockk<RetailerDebitNoteSettlementMapping>()

            val mappingSlot = slot<RetailerDebitNoteSettlementMapping>()

            every { retailerDebitNoteRepo.save(any()) } returns savedDebitNote
            every { retailerDebitNoteSettlementMappingService.save(capture(mappingSlot)) } returns savedMapping

            // Act
            val result = retailerDebitNoteService.updateDNSettlement(debitNote, settlement, settlementMapping)

            // Assert
            assertEquals(savedDebitNote, result.first)
            assertEquals(savedMapping, result.second)
            assertEquals(InvoiceStatus.PARTIAL_PAID, savedDebitNote.status)
            assertEquals(500.0, savedDebitNote.amountReceived)
            assertEquals("test-user", savedDebitNote.updatedBy)
            assertNotNull(savedDebitNote.updatedOn)

            // Verify the mapping was created correctly
            assertEquals(savedDebitNote, mappingSlot.captured.retailerDebitNoteId)
            assertEquals(settlement, mappingSlot.captured.settlementId)
            assertEquals(500.0, mappingSlot.captured.paidAmount)

            verify { retailerDebitNoteRepo.save(any()) }
            verify { retailerDebitNoteSettlementMappingService.save(any()) }
        }

        @Test
        @DisplayName("updateDNSettlement should set status to PAID when remaining amount is less than 1.00")
        fun testUpdateDNSettlementWithSmallRemainingAmount() {
            // Arrange
            val debitNoteId = 1L
            val settlementId = 1L

            val debitNote = RetailerDebitNote(
                createdBy = "test-user",
                updatedBy = "test-user",
                remarks = "Test debit note",
                status = InvoiceStatus.PENDING,
                type = DnType.ADHOC,
                amount = 1000.0,
                amountReceived = 0.0,
                taxableValue = 900.0,
                taxAmount = 100.0,
                documentNumber = "DN-001",
                partnerDetailId = 1L,
                partnerId = 1L,
                partnerName = "Test Partner",
                tenant = "th001",
                companyId = 1L
            ).apply { this.id = debitNoteId }

            val settlementDebitNote = debitNote.copy().apply { 
                this.id = debitNoteId
                this.amountReceived = 999.5 
            }

            val settlement = DataProvider.settlement(
                id = settlementId,
                createdBy = "test-user",
                retailerDebitNotes = mutableListOf(settlementDebitNote)
            )

            val settlementMapping = DataProvider.draftReceiptEntityMapping(
                entityTxAmount = 999.5
            )

            val savedDebitNote = debitNote.copy().apply { 
                this.id = debitNoteId
                this.amountReceived = 999.5
                this.status = InvoiceStatus.PAID
                this.updatedBy = "test-user"
                this.updatedOn = LocalDateTime.now()
            }

            val savedMapping = mockk<RetailerDebitNoteSettlementMapping>()

            every { retailerDebitNoteRepo.save(any()) } returns savedDebitNote
            every { retailerDebitNoteSettlementMappingService.save(any()) } returns savedMapping

            // Act
            val result = retailerDebitNoteService.updateDNSettlement(debitNote, settlement, settlementMapping)

            // Assert
            assertEquals(savedDebitNote, result.first)
            assertEquals(savedMapping, result.second)
            assertEquals(InvoiceStatus.PAID, savedDebitNote.status)

            verify { retailerDebitNoteRepo.save(any()) }
            verify { retailerDebitNoteSettlementMappingService.save(any()) }
        }

        @Test
        @DisplayName("updateDNSettlement should throw exception when debit note not found in settlement")
        fun testUpdateDNSettlementDebitNoteNotFound() {
            // Arrange
            val debitNoteId = 1L
            val settlementId = 1L

            val debitNote = RetailerDebitNote(
                createdBy = "test-user",
                updatedBy = "test-user",
                remarks = "Test debit note",
                status = InvoiceStatus.PENDING,
                type = DnType.ADHOC,
                amount = 1000.0,
                amountReceived = 0.0,
                taxableValue = 900.0,
                taxAmount = 100.0,
                documentNumber = "DN-001",
                partnerDetailId = 1L,
                partnerId = 1L,
                partnerName = "Test Partner",
                tenant = "th001",
                companyId = 1L
            ).apply { this.id = debitNoteId }

            val settlement = DataProvider.settlement(
                id = settlementId,
                retailerDebitNotes = mutableListOf()
            )

            val settlementMapping = DataProvider.draftReceiptEntityMapping()

            // Act & Assert
            val exception = assertThrows<RequestException> {
                retailerDebitNoteService.updateDNSettlement(debitNote, settlement, settlementMapping)
            }

            assertEquals("debit note not found for id $debitNoteId", exception.message)
        }

        @Test
        @DisplayName("updateDNSettlement should throw exception when debit note is already paid")
        fun testUpdateDNSettlementDebitNoteAlreadyPaid() {
            // Arrange
            val debitNoteId = 1L
            val settlementId = 1L

            val debitNote = RetailerDebitNote(
                createdBy = "test-user",
                updatedBy = "test-user",
                remarks = "Test debit note",
                status = InvoiceStatus.PAID,
                type = DnType.ADHOC,
                amount = 1000.0,
                amountReceived = 1000.0,
                taxableValue = 900.0,
                taxAmount = 100.0,
                documentNumber = "DN-001",
                partnerDetailId = 1L,
                partnerId = 1L,
                partnerName = "Test Partner",
                tenant = "th001",
                companyId = 1L
            ).apply { this.id = debitNoteId }

            val settlementDebitNote = debitNote.copy().apply { this.id = debitNoteId }

            val settlement = DataProvider.settlement(
                id = settlementId,
                retailerDebitNotes = mutableListOf(settlementDebitNote)
            )

            val settlementMapping = DataProvider.draftReceiptEntityMapping()

            // Act & Assert
            val exception = assertThrows<RequestException> {
                retailerDebitNoteService.updateDNSettlement(debitNote, settlement, settlementMapping)
            }

            assertEquals("Trying to change the invoice in a terminal state - ${InvoiceStatus.PAID}", exception.message)
        }
    }
}
