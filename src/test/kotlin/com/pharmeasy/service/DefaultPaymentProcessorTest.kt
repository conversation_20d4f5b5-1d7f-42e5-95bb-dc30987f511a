package com.pharmeasy.service

import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.type.PaymentType
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test

class DefaultPaymentProcessorTest {

    private lateinit var defaultPaymentProcessor: DefaultPaymentProcessor
    private lateinit var draftReceiptService: DraftReceiptService
    private val partnerDetailId = 1L
    private val tenant = "th001"
    private val transactionId = "TX123456"

    @BeforeEach
    fun setUp() {
        draftReceiptService = mockk(relaxed = true)
        defaultPaymentProcessor = DefaultPaymentProcessor(draftReceiptService)
    }

    @Test
    @DisplayName("DefaultPaymentProcessor should use parent class behavior")
    fun testDefaultPaymentProcessorUsesParentBehavior() {
        // Arrange
        val paymentInfo = PaymentInfo(
            customerTransactionId = transactionId,
            transactionDate = System.currentTimeMillis(),
            transactionAmount = 1000.0,
            paymentType = PaymentType.CASH,
            paymentMode = "CASH",
            initiatedBy = "USER"
        )
        
        val partnerInfo = PartnerInfo(
            partnerId = 1L,
            partnerDetailId = partnerDetailId,
            partnerName = "Test Partner",
            tenant = tenant,
            distributorPdi = 2L
        )
        
        every { draftReceiptService.findDuplicatesByPaymentTransactionId(any(), any(), any()) } returns emptyList()
        
        // Act
        val result = defaultPaymentProcessor.validatePayment(paymentInfo, partnerInfo)
        
        // Assert
        assertNull(result)
        verify { draftReceiptService.findDuplicatesByPaymentTransactionId(any(), any(), any()) }
    }
}
