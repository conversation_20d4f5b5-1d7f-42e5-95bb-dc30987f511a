package com.pharmeasy.service

import com.pharmeasy.data.DraftReceipt
import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.service.abstracts.PaymentProcessor
import com.pharmeasy.service.strategy.PaymentProcessorStrategyFactory
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.ReceiptStatus
import com.pharmeasy.utils.DataProvider
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class DraftReceiptProcessingStrategyImplTest {

    private lateinit var draftReceiptProcessingStrategy: DraftReceiptProcessingStrategyImpl
    private lateinit var companyService: CompanyService
    private lateinit var documentService: DocumentMasterService
    private lateinit var draftReceiptService: DraftReceiptService
    private lateinit var paymentProcessorStrategyFactory: PaymentProcessorStrategyFactory
    private lateinit var settleableProcessorFactory: SettleableProcessorFactory

    companion object {
        const val RECEIPT_NUMBER = "REC-001"
        const val TENANT = DataProvider.TENANT
        const val PARTNER_ID = DataProvider.PARTNER_ID
        const val PARTNER_DETAIL_ID = DataProvider.PDI
        const val PAYMENT_TRANSACTION_ID = DataProvider.TX_ID
        const val AMOUNT = 1000.0
        const val USER = "test-user"
        const val PARTNER_NAME = DataProvider.PARTNER_NAME
    }

    private lateinit var mockDraftReceipt: DraftReceipt
    private lateinit var mockPaymentInfo: PaymentInfo
    private lateinit var mockPartnerInfo: PartnerInfo

    @BeforeEach
    fun setUp() {
        // Initialize all mocks
        companyService = mockk(relaxed = true)
        documentService = mockk(relaxed = true)
        draftReceiptService = mockk(relaxed = true)
        paymentProcessorStrategyFactory = mockk(relaxed = true)
        settleableProcessorFactory = mockk(relaxed = true)

        // Create the strategy with mocked dependencies
        draftReceiptProcessingStrategy = DraftReceiptProcessingStrategyImpl(
            companyService,
            documentService,
            draftReceiptService,
            paymentProcessorStrategyFactory,
            settleableProcessorFactory
        )

        mockDraftReceipt = DataProvider.draftReceipt(
            receiptNumber = RECEIPT_NUMBER,
            paymentTransactionId = PAYMENT_TRANSACTION_ID,
            amount = AMOUNT,
            paymentType = PaymentType.CASH,
            tenant = TENANT,
            partnerId = PARTNER_ID,
            partnerDetailId = PARTNER_DETAIL_ID,
            remarks = "Receipt Created from RIO Payment",
            status = ReceiptStatus.DRAFT
        )

        mockPaymentInfo = DataProvider.cashPaymentInfo(
            customerTransactionId = PAYMENT_TRANSACTION_ID,
            transactionAmount = AMOUNT
        )

        mockPartnerInfo = DataProvider.partnerInfo(
            partnerId = PARTNER_ID,
            partnerDetailId = PARTNER_DETAIL_ID,
            partnerName = PARTNER_NAME,
            distributorPdi = DataProvider.DISTRIBUTOR_PDI,
            tenant = TENANT
        )

        // Default mock behaviors
        every { draftReceiptService.getReceiptByPaymentTransactionId(any()) } returns null
        every { draftReceiptService.saveDraftReceipt(any()) } answers { 
            val draft = firstArg<DraftReceipt>()
            // Set an ID to simulate database save
            draft.id = 1L
            draft
        }
        every { draftReceiptService.updateSettleableMappings(any(), any(), any()) } returns Unit

        // Mock document service to return RECEIPT_NUMBER
        every { documentService.generateDocumentNumber(any(), any()) } returns RECEIPT_NUMBER

        // Mock for PaymentProcessorStrategyFactory
        val paymentProcessor = mockk<PaymentProcessor>(relaxed = true)
        every { paymentProcessorStrategyFactory.getInstance(any()) } returns paymentProcessor
    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    @Nested
    @DisplayName("createDraftReceipt Tests")
    inner class CreateDraftReceiptTests {

        @Test
        @DisplayName("Should create a new draft receipt when no existing receipt is found")
        fun testCreateDraftReceiptCreatesNewReceipt() {
            // Ensure validatePayment returns null (no error)
            val paymentProcessor = mockk<PaymentProcessor>(relaxed = true)
            every { paymentProcessorStrategyFactory.getInstance(any()) } returns paymentProcessor
            every { paymentProcessor.validatePayment(any(), any()) } returns null

            val draftReceiptSlot = slot<DraftReceipt>()
            every { draftReceiptService.saveDraftReceipt(capture(draftReceiptSlot)) } answers {
                val draft = draftReceiptSlot.captured
                // Set an ID to simulate database save
                draft.id = 1L
                draft
            }

            val result = draftReceiptProcessingStrategy.createDraftReceipt(mockPaymentInfo, mockPartnerInfo)

            assertNotNull(result)
            assertEquals(RECEIPT_NUMBER, result.receiptNumber)
            assertEquals(PAYMENT_TRANSACTION_ID, result.paymentTransactionId)
            assertEquals(AMOUNT, result.amount)
            assertEquals("Receipt Created from RIO Payment", result.remarks)

            verify { draftReceiptService.getReceiptByPaymentTransactionId(PAYMENT_TRANSACTION_ID) }
            verify { draftReceiptService.saveDraftReceipt(any()) }
            verify { draftReceiptService.updateSettleableMappings(draftReceiptSlot.captured, mockPaymentInfo, mockPartnerInfo) }
        }

        @Test
        @DisplayName("Should return existing draft receipt when one is found")
        fun testCreateDraftReceiptReturnsExistingReceipt() {
            every { draftReceiptService.getReceiptByPaymentTransactionId(PAYMENT_TRANSACTION_ID) } returns mockDraftReceipt

            val result = draftReceiptProcessingStrategy.createDraftReceipt(mockPaymentInfo, mockPartnerInfo)

            assertNotNull(result)
            assertEquals(mockDraftReceipt, result)

            verify { draftReceiptService.getReceiptByPaymentTransactionId(PAYMENT_TRANSACTION_ID) }
            verify(exactly = 1) { draftReceiptService.saveDraftReceipt(any()) }
            verify { draftReceiptService.updateSettleableMappings(mockDraftReceipt, mockPaymentInfo, mockPartnerInfo) }
        }

        @Test
        @DisplayName("Should create a pre-approved receipt when preApprove is true")
        fun testCreateDraftReceiptWithPreApprove() {
            val draftReceiptSlot = slot<DraftReceipt>()
            every { draftReceiptService.saveDraftReceipt(capture(draftReceiptSlot)) } answers {
                val draft = draftReceiptSlot.captured
                // Set an ID to simulate database save
                draft.id = 1L
                draft
            }

            val result = draftReceiptProcessingStrategy.createDraftReceipt(mockPaymentInfo, mockPartnerInfo, true)

            assertNotNull(result)
            assertEquals(RECEIPT_NUMBER, result.receiptNumber)
            assertEquals(ReceiptStatus.APPROVED, result.status)

            verify { draftReceiptService.getReceiptByPaymentTransactionId(PAYMENT_TRANSACTION_ID) }
            verify { draftReceiptService.saveDraftReceipt(any()) }
            verify { draftReceiptService.updateSettleableMappings(draftReceiptSlot.captured, mockPaymentInfo, mockPartnerInfo) }
        }

        @Test
        @DisplayName("Should create a draft receipt for all invoices/DebitNotes scenario")
        fun testCreateDraftReceiptForAllInvoicesAndDebitNotes() {
            val invoiceSettleable = DataProvider.invoiceSettleableInfo(amount = 400.0)
            val debitNoteSettleable = DataProvider.debitNoteSettleableInfo(amount = 600.0)
            val paymentInfo = DataProvider.cashPaymentInfo(
                settleables = listOf(invoiceSettleable, debitNoteSettleable)
            )

            val draftReceiptSlot = slot<DraftReceipt>()
            every { draftReceiptService.saveDraftReceipt(capture(draftReceiptSlot)) } answers {
                val draft = draftReceiptSlot.captured
                // Set an ID to simulate database save
                draft.id = 1L
                draft
            }

            val result = draftReceiptProcessingStrategy.createDraftReceipt(paymentInfo, mockPartnerInfo)

            assertNotNull(result)
            assertEquals(RECEIPT_NUMBER, result.receiptNumber)

            verify { draftReceiptService.getReceiptByPaymentTransactionId(PAYMENT_TRANSACTION_ID) }
            verify { draftReceiptService.saveDraftReceipt(any()) }
            verify { draftReceiptService.updateSettleableMappings(draftReceiptSlot.captured, paymentInfo, mockPartnerInfo) }
        }

        @Test
        @DisplayName("Should create a draft receipt for only Advance Payment scenario")
        fun testCreateDraftReceiptForAdvancePayment() {
            val paymentInfo = DataProvider.advancePaymentInfo()

            val draftReceiptSlot = slot<DraftReceipt>()
            every { draftReceiptService.saveDraftReceipt(capture(draftReceiptSlot)) } answers {
                val draft = draftReceiptSlot.captured
                // Set an ID to simulate database save
                draft.id = 1L
                draft
            }

            val result = draftReceiptProcessingStrategy.createDraftReceipt(paymentInfo, mockPartnerInfo)

            assertNotNull(result)
            assertEquals(RECEIPT_NUMBER, result.receiptNumber)

            verify { draftReceiptService.getReceiptByPaymentTransactionId(PAYMENT_TRANSACTION_ID) }
            verify { draftReceiptService.saveDraftReceipt(any()) }
            verify { draftReceiptService.updateSettleableMappings(draftReceiptSlot.captured, paymentInfo, mockPartnerInfo) }
        }

        @Test
        @DisplayName("Should create a draft receipt for Invoice + Advance Payment scenario")
        fun testCreateDraftReceiptForInvoiceAndAdvancePayment() {
            val invoiceSettleable = DataProvider.invoiceSettleableInfo(amount = 400.0)
            val paymentInfo = DataProvider.cashPaymentInfo(
                transactionAmount = 1000.0,
                settleables = listOf(invoiceSettleable)
            )

            val draftReceiptSlot = slot<DraftReceipt>()
            every { draftReceiptService.saveDraftReceipt(capture(draftReceiptSlot)) } answers {
                val draft = draftReceiptSlot.captured
                // Set an ID to simulate database save
                draft.id = 1L
                draft
            }

            val result = draftReceiptProcessingStrategy.createDraftReceipt(paymentInfo, mockPartnerInfo)

            assertNotNull(result)
            assertEquals(RECEIPT_NUMBER, result.receiptNumber)

            verify { draftReceiptService.getReceiptByPaymentTransactionId(PAYMENT_TRANSACTION_ID) }
            verify { draftReceiptService.saveDraftReceipt(any()) }
            verify { draftReceiptService.updateSettleableMappings(draftReceiptSlot.captured, paymentInfo, mockPartnerInfo) }
        }

        @Test
        @DisplayName("Should create a draft receipt for Cheque payment scenario")
        fun testCreateDraftReceiptForChequePayment() {
            val paymentInfo = DataProvider.chequePaymentInfo()

            val draftReceiptSlot = slot<DraftReceipt>()
            every { draftReceiptService.saveDraftReceipt(capture(draftReceiptSlot)) } answers {
                val draft = draftReceiptSlot.captured
                // Set an ID to simulate database save
                draft.id = 1L
                draft
            }

            val result = draftReceiptProcessingStrategy.createDraftReceipt(paymentInfo, mockPartnerInfo)

            assertNotNull(result)
            assertEquals(RECEIPT_NUMBER, result.receiptNumber)

            verify { draftReceiptService.getReceiptByPaymentTransactionId(PAYMENT_TRANSACTION_ID) }
            verify { draftReceiptService.saveDraftReceipt(any()) }
            verify { draftReceiptService.updateSettleableMappings(draftReceiptSlot.captured, paymentInfo, mockPartnerInfo) }
        }

        @Test
        @DisplayName("Should create a draft receipt for Neft payment scenario")
        fun testCreateDraftReceiptForNeftPayment() {
            val paymentInfo = DataProvider.neftPaymentInfo()

            val draftReceiptSlot = slot<DraftReceipt>()
            every { draftReceiptService.saveDraftReceipt(capture(draftReceiptSlot)) } answers {
                val draft = draftReceiptSlot.captured
                // Set an ID to simulate database save
                draft.id = 1L
                draft
            }

            val result = draftReceiptProcessingStrategy.createDraftReceipt(paymentInfo, mockPartnerInfo)

            assertNotNull(result)
            assertEquals(RECEIPT_NUMBER, result.receiptNumber)

            verify { draftReceiptService.getReceiptByPaymentTransactionId(PAYMENT_TRANSACTION_ID) }
            verify { draftReceiptService.saveDraftReceipt(any()) }
            verify { draftReceiptService.updateSettleableMappings(draftReceiptSlot.captured, paymentInfo, mockPartnerInfo) }
        }
    }

    @Nested
    @DisplayName("addSettlements Tests")
    inner class AddSettlementsTests {

        @Test
        @DisplayName("Should update settleable mappings")
        fun testAddSettlements() {
            draftReceiptProcessingStrategy.addSettlements(mockDraftReceipt, mockPaymentInfo, mockPartnerInfo)

            verify { draftReceiptService.updateSettleableMappings(mockDraftReceipt, mockPaymentInfo, mockPartnerInfo) }
        }
    }

    @Nested
    @DisplayName("Error Remark Tests")
    inner class ErrorRemarkTests {

        @Test
        @DisplayName("Should create a rejected receipt when payment validation fails")
        fun testCreateDraftReceiptWithPaymentValidationError() {
            val errorMessage = "Invalid payment details"
            val paymentProcessor = mockk<PaymentProcessor>(relaxed = true)
            every { paymentProcessorStrategyFactory.getInstance(any()) } returns paymentProcessor
            every { paymentProcessor.validatePayment(any(), any()) } returns errorMessage

            val draftReceiptSlot = slot<DraftReceipt>()
            every { draftReceiptService.saveDraftReceipt(capture(draftReceiptSlot)) } answers {
                val draft = draftReceiptSlot.captured
                // Set an ID to simulate database save
                draft.id = 1L
                draft
            }

            val result = draftReceiptProcessingStrategy.createDraftReceipt(mockPaymentInfo, mockPartnerInfo)

            assertNotNull(result)
            assertEquals(RECEIPT_NUMBER, result.receiptNumber)

            // Verify the draft receipt was saved with REJECTED status and the error message
            verify { draftReceiptService.saveDraftReceipt(match { 
                it.status == ReceiptStatus.REJECTED && it.remarks == errorMessage 
            }) }

            verify { draftReceiptService.getReceiptByPaymentTransactionId(PAYMENT_TRANSACTION_ID) }
            verify { draftReceiptService.updateSettleableMappings(draftReceiptSlot.captured, mockPaymentInfo, mockPartnerInfo) }
        }

        @Test
        @DisplayName("Should create a rejected receipt for cheque payment with validation error")
        fun testCreateDraftReceiptForChequePaymentWithValidationError() {
            val errorMessage = "Invalid cheque details"
            val paymentInfo = DataProvider.chequePaymentInfo()
            val paymentProcessor = mockk<PaymentProcessor>(relaxed = true)

            every { paymentProcessorStrategyFactory.getInstance(PaymentType.CHEQUE) } returns paymentProcessor
            every { paymentProcessor.validatePayment(any(), any()) } returns errorMessage

            val draftReceiptSlot = slot<DraftReceipt>()
            every { draftReceiptService.saveDraftReceipt(capture(draftReceiptSlot)) } answers {
                val draft = draftReceiptSlot.captured
                // Set an ID to simulate database save
                draft.id = 1L
                draft
            }

            val result = draftReceiptProcessingStrategy.createDraftReceipt(paymentInfo, mockPartnerInfo)

            assertNotNull(result)
            assertEquals(RECEIPT_NUMBER, result.receiptNumber)

            // Verify the draft receipt was saved with REJECTED status and the error message
            verify { draftReceiptService.saveDraftReceipt(match { 
                it.status == ReceiptStatus.REJECTED && it.remarks == errorMessage 
            }) }

            verify { draftReceiptService.getReceiptByPaymentTransactionId(PAYMENT_TRANSACTION_ID) }
            verify { draftReceiptService.updateSettleableMappings(draftReceiptSlot.captured, paymentInfo, mockPartnerInfo) }
        }

        @Test
        @DisplayName("Should create a rejected receipt for NEFT payment with validation error")
        fun testCreateDraftReceiptForNeftPaymentWithValidationError() {
            val errorMessage = "Invalid NEFT details"
            val paymentInfo = DataProvider.neftPaymentInfo()
            val paymentProcessor = mockk<PaymentProcessor>(relaxed = true)

            every { paymentProcessorStrategyFactory.getInstance(PaymentType.NEFT) } returns paymentProcessor
            every { paymentProcessor.validatePayment(any(), any()) } returns errorMessage

            val draftReceiptSlot = slot<DraftReceipt>()
            every { draftReceiptService.saveDraftReceipt(capture(draftReceiptSlot)) } answers {
                val draft = draftReceiptSlot.captured
                // Set an ID to simulate database save
                draft.id = 1L
                draft
            }

            val result = draftReceiptProcessingStrategy.createDraftReceipt(paymentInfo, mockPartnerInfo)

            assertNotNull(result)
            assertEquals(RECEIPT_NUMBER, result.receiptNumber)

            // Verify the draft receipt was saved with REJECTED status and the error message
            verify { draftReceiptService.saveDraftReceipt(match { 
                it.status == ReceiptStatus.REJECTED && it.remarks == errorMessage 
            }) }

            verify { draftReceiptService.getReceiptByPaymentTransactionId(PAYMENT_TRANSACTION_ID) }
            verify { draftReceiptService.updateSettleableMappings(draftReceiptSlot.captured, paymentInfo, mockPartnerInfo) }
        }
    }
}
