package com.pharmeasy.service.abstracts

import com.pharmeasy.service.DebitNoteSettleableProcessor
import com.pharmeasy.service.InvoiceSettleableProcessor
import com.pharmeasy.service.SettleableProcessorFactory
import com.pharmeasy.type.SettleableType
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertSame
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.test.util.ReflectionTestUtils

class SettleableProcessorFactoryTest {

    private lateinit var settleableProcessorFactory: SettleableProcessorFactory
    private lateinit var invoiceSettleableProcessor: InvoiceSettleableProcessor
    private lateinit var debitNoteSettleableProcessor: DebitNoteSettleableProcessor

    @BeforeEach
    fun setUp() {
        // Create mock processors
        invoiceSettleableProcessor = mockk<InvoiceSettleableProcessor>()
        debitNoteSettleableProcessor = mockk<DebitNoteSettleableProcessor>()
        
        // Create the factory
        settleableProcessorFactory = SettleableProcessorFactory()
        
        // Inject the mock processors using reflection
        ReflectionTestUtils.setField(settleableProcessorFactory, "invoiceSettleableProcessor", invoiceSettleableProcessor)
        ReflectionTestUtils.setField(settleableProcessorFactory, "debitNoteSettleableProcessor", debitNoteSettleableProcessor)
    }

    @Test
    @DisplayName("getSettleableProcessor should return InvoiceSettleableProcessor for INVOICE type")
    fun testGetSettleableProcessorForInvoiceType() {
        // Act
        val processor = settleableProcessorFactory.getSettleableProcessor(SettleableType.INVOICE)
        
        // Assert
        assertSame(invoiceSettleableProcessor, processor)
    }

    @Test
    @DisplayName("getSettleableProcessor should return DebitNoteSettleableProcessor for DEBIT_NOTE type")
    fun testGetSettleableProcessorForDebitNoteType() {
        // Act
        val processor = settleableProcessorFactory.getSettleableProcessor(SettleableType.DEBIT_NOTE)
        
        // Assert
        assertSame(debitNoteSettleableProcessor, processor)
    }
}
