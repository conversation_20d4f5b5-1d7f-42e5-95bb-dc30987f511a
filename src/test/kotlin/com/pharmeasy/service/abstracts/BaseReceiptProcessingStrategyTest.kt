package com.pharmeasy.service.abstracts

import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.model.ops.SettleableInfo
import com.pharmeasy.service.CompanyService
import com.pharmeasy.service.DocumentMasterService
import com.pharmeasy.service.SettleableProcessorFactory
import com.pharmeasy.service.strategy.PaymentProcessorStrategyFactory
import com.pharmeasy.type.CreationType
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.SettleableType
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class BaseReceiptProcessingStrategyTest {

    private lateinit var baseReceiptProcessingStrategy: TestBaseReceiptProcessingStrategy
    private lateinit var companyService: CompanyService
    private lateinit var documentService: DocumentMasterService
    private lateinit var paymentProcessorStrategyFactory: PaymentProcessorStrategyFactory
    private lateinit var settleableProcessorFactory: SettleableProcessorFactory
    private lateinit var settleableProcessor: BaseSettleableProcessor
    private lateinit var paymentProcessor: PaymentProcessor

    @BeforeEach
    fun setUp() {
        companyService = mockk(relaxed = true)
        documentService = mockk(relaxed = true)
        paymentProcessorStrategyFactory = mockk(relaxed = true)
        settleableProcessorFactory = mockk(relaxed = true)
        settleableProcessor = mockk(relaxed = true)
        paymentProcessor = mockk(relaxed = true)

        baseReceiptProcessingStrategy = TestBaseReceiptProcessingStrategy(
            companyService,
            documentService,
            paymentProcessorStrategyFactory,
            settleableProcessorFactory
        )

        every { paymentProcessorStrategyFactory.getInstance(any()) } returns paymentProcessor
    }

    @Test
    @DisplayName("validateReceiptRequest should throw exception when settleable details not found")
    fun testValidateReceiptRequestThrowsExceptionWhenSettleableDetailsNotFound() {
        // Arrange
        val payment = PaymentInfo(
            customerTransactionId = "TX123",
            transactionDate = System.currentTimeMillis(),
            transactionAmount = 1000.0,
            paymentType = PaymentType.CASH,
            paymentMode = "CASH",
            initiatedBy = "USER",
            settleables = listOf(
                SettleableInfo(
                    number = "INV001",
                    type = CreationType.INVOICE,
                    amount = 1000.0,
                    category = "INVOICE"
                )
            )
        )
        
        val partnerInfo = PartnerInfo(
            partnerId = 1L,
            partnerDetailId = 1L,
            partnerName = "Test Partner",
            tenant = "th001",
            distributorPdi = 2L
        )
        
        val settleableType = SettleableType.INVOICE
        
        every { settleableProcessorFactory.getSettleableProcessor(settleableType) } returns settleableProcessor
        every { settleableProcessor.getSettleableDetails("INV001", "th001") } returns null
        
        // Act & Assert
        val exception = assertThrows<IllegalArgumentException> {
            baseReceiptProcessingStrategy.validateReceiptRequest(payment, partnerInfo)
        }
        
        // Verify the exception message contains the expected text
        assertEquals(
            "Invalid $settleableType number: INV001 for tenant th001",
            exception.message
        )
        
        verify { settleableProcessorFactory.getSettleableProcessor(settleableType) }
        verify { settleableProcessor.getSettleableDetails("INV001", "th001") }
    }

    @Test
    @DisplayName("generateReceiptNumber should throw exception when company not found")
    fun testGenerateReceiptNumberThrowsExceptionWhenCompanyNotFound() {
        // Arrange
        val tenant = "nonexistent-tenant"
        every { companyService.getCompanyByTenant(tenant) } returns null
        
        // Act & Assert
        val exception = assertThrows<RequestException> {
            baseReceiptProcessingStrategy.generateReceiptNumber(tenant)
        }
        
        // Verify the exception message
        assertEquals("Company not found for tenant $tenant", exception.message)
        
        verify { companyService.getCompanyByTenant(tenant) }
    }

    // Test implementation of BaseReceiptProcessingStrategy for testing protected methods
    private class TestBaseReceiptProcessingStrategy(
        companyService: CompanyService,
        documentService: DocumentMasterService,
        paymentProcessorStrategyFactory: PaymentProcessorStrategyFactory,
        settleableProcessorFactory: SettleableProcessorFactory
    ) : BaseReceiptProcessingStrategy(
        companyService,
        documentService,
        paymentProcessorStrategyFactory,
        settleableProcessorFactory
    ) {
        // Expose protected methods for testing
        public override fun validateReceiptRequest(payment: PaymentInfo, partnerInfo: PartnerInfo): String? {
            return super.validateReceiptRequest(payment, partnerInfo)
        }
        
        public override fun generateReceiptNumber(tenant: String): String {
            return super.generateReceiptNumber(tenant)
        }
    }
}
