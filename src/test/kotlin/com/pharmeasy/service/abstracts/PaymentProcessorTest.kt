package com.pharmeasy.service.abstracts

import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.model.ops.RIOPaymentDTO
import com.pharmeasy.service.DraftReceiptService
import com.pharmeasy.type.PaymentType
import com.pharmeasy.utils.DataProvider
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class PaymentProcessorTest {

    private lateinit var paymentProcessor: TestPaymentProcessor
    private lateinit var draftReceiptService: DraftReceiptService

    private val transactionId = "TX123456"
    private val partnerDetailId = 1L
    private val tenant = "th001"

    @BeforeEach
    fun setUp() {
        draftReceiptService = mockk(relaxed = true)
        paymentProcessor = TestPaymentProcessor(draftReceiptService)
    }

    @Test
    @DisplayName("validatePayment should throw exception when transaction ID is blank")
    fun testValidatePaymentWithBlankTransactionId() {
        // Arrange
        val paymentInfo = PaymentInfo(
            customerTransactionId = "",
            transactionDate = System.currentTimeMillis(),
            transactionAmount = 1000.0,
            paymentType = PaymentType.CASH,
            paymentMode = "CASH",
            initiatedBy = "USER"
        )
        
        val partnerInfo = PartnerInfo(
            partnerId = 1L,
            partnerDetailId = partnerDetailId,
            partnerName = "Test Partner",
            tenant = tenant,
            distributorPdi = 2L
        )
        
        // Act & Assert
        val exception = assertThrows<IllegalArgumentException> {
            paymentProcessor.validatePayment(paymentInfo, partnerInfo)
        }
        
        // Verify the exception message
        assertEquals("Transaction ID is required", exception.message)
    }

    @Test
    @DisplayName("validatePayment should check for duplicate payments")
    fun testValidatePaymentChecksDuplicates() {
        // Arrange
        val paymentInfo = PaymentInfo(
            customerTransactionId = transactionId,
            transactionDate = System.currentTimeMillis(),
            transactionAmount = 1000.0,
            paymentType = PaymentType.CASH,
            paymentMode = "CASH",
            initiatedBy = "USER"
        )
        
        val partnerInfo = PartnerInfo(
            partnerId = 1L,
            partnerDetailId = partnerDetailId,
            partnerName = "Test Partner",
            tenant = tenant,
            distributorPdi = 2L
        )
        
        every { draftReceiptService.findDuplicatesByPaymentTransactionId(transactionId, partnerDetailId, tenant) } returns emptyList()
        
        // Act
        val result = paymentProcessor.validatePayment(paymentInfo, partnerInfo)
        
        // Assert
        assertNull(result)
        verify { draftReceiptService.findDuplicatesByPaymentTransactionId(transactionId, partnerDetailId, tenant) }
    }

    @Test
    @DisplayName("validatePayment should return error when duplicate transaction ID exists")
    fun testValidatePaymentWithDuplicateTransactionId() {
        // Arrange
        val paymentInfo = PaymentInfo(
            customerTransactionId = transactionId,
            transactionDate = System.currentTimeMillis(),
            transactionAmount = 1000.0,
            paymentType = PaymentType.CASH,
            paymentMode = "CASH",
            initiatedBy = "USER"
        )
        
        val partnerInfo = PartnerInfo(
            partnerId = 1L,
            partnerDetailId = partnerDetailId,
            partnerName = "Test Partner",
            tenant = tenant,
            distributorPdi = 2L
        )
        
        val existingReceipt = DataProvider.draftReceipt(
            id = 1L,
            receiptNumber = "REC-001",
            paymentTransactionId = transactionId,
            amount = 1000.0
        )
        
        every { draftReceiptService.findDuplicatesByPaymentTransactionId(transactionId, partnerDetailId, tenant) } returns listOf(existingReceipt)
        
        // Act
        val result = paymentProcessor.validatePayment(paymentInfo, partnerInfo)
        
        // Assert
        assertEquals("Transaction ID $transactionId already exists for the given partner in receipt: REC-001", result)
        verify { draftReceiptService.findDuplicatesByPaymentTransactionId(transactionId, partnerDetailId, tenant) }
    }

    // Concrete implementation of PaymentProcessor for testing
    private class TestPaymentProcessor(
        draftReceiptService: DraftReceiptService
    ) : PaymentProcessor(draftReceiptService) {
        // No need to override methods as we want to test the base class implementation
    }
}
