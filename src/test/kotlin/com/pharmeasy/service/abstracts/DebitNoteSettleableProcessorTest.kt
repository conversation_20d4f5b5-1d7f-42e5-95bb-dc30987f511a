package com.pharmeasy.service.abstracts

import com.pharmeasy.data.DraftReceiptEntityMapping
import com.pharmeasy.data.Receipt
import com.pharmeasy.data.RetailerDebitNote
import com.pharmeasy.data.Settlement
import com.pharmeasy.dto.SettleableDetails
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.InvoiceStatus
import com.pharmeasy.model.Supplier
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.service.DebitNoteSettleableProcessor
import com.pharmeasy.service.RetailerDebitNoteService
import com.pharmeasy.stream.InvoiceSettlementUpdateHandler
import com.pharmeasy.type.DnType
import com.pharmeasy.type.PartnerType
import com.pharmeasy.type.SettleableType
import com.pharmeasy.utils.DataProvider
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class DebitNoteSettleableProcessorTest {

    private lateinit var debitNoteSettleableProcessor: DebitNoteSettleableProcessor
    private lateinit var debitNoteService: RetailerDebitNoteService
    private lateinit var supplierProxy: SupplierProxy
    private lateinit var invoiceSettlementUpdateHandler: InvoiceSettlementUpdateHandler

    companion object {
        const val DEBIT_NOTE_NUMBER = DataProvider.DN_NUMBER
        const val TENANT = DataProvider.TENANT
        const val PARTNER_ID = DataProvider.PARTNER_ID
        const val PARTNER_DETAIL_ID = DataProvider.PDI
        const val PAYMENT_TRANSACTION_ID = DataProvider.TX_ID
        const val AMOUNT = 1000.0
        const val USER = "test-user"
        const val PARTNER_NAME = DataProvider.PARTNER_NAME
    }

    private lateinit var mockReceipt: Receipt
    private lateinit var mockDebitNote: RetailerDebitNote
    private lateinit var mockSettleableDetails: SettleableDetails
    private lateinit var mockSettlementMapping: DraftReceiptEntityMapping
    private lateinit var mockSettlement: Settlement
    private lateinit var mockSupplier: Supplier

    @BeforeEach
    fun setUp() {
        // Initialize all mocks
        debitNoteService = mockk(relaxed = true)
        supplierProxy = mockk(relaxed = true)
        invoiceSettlementUpdateHandler = mockk(relaxed = true)

        // Create the processor with mocked dependencies
        debitNoteSettleableProcessor = DebitNoteSettleableProcessor(
            debitNoteService,
            supplierProxy,
            invoiceSettlementUpdateHandler
        )

        // Create objects using DataProvider where possible
        mockReceipt = DataProvider.receipt(
            id = 1L,
            receiptNumber = "REC-001",
            paymentTransactionId = PAYMENT_TRANSACTION_ID,
            amount = AMOUNT,
            updatedBy = USER,
            tenant = TENANT,
            partnerId = PARTNER_ID,
            partnerDetailId = PARTNER_DETAIL_ID
        )

        mockDebitNote = mockk(relaxed = true) {
            every { id } returns 1L
            every { documentNumber } returns DEBIT_NOTE_NUMBER
            every { amount } returns AMOUNT
            every { amountReceived } returns 0.0
            every { status } returns InvoiceStatus.PENDING
            every { type } returns DnType.ADHOC
            every { partnerId } returns PARTNER_ID
            every { partnerDetailId } returns PARTNER_DETAIL_ID
            every { partnerName } returns PARTNER_NAME
            every { tenant } returns TENANT
            every { copy() } returns this
        }

        // Use DataProvider for SettleableDetails
        mockSettleableDetails = DataProvider.settleableDetails(
            id = mockDebitNote.id!!,
            number = DEBIT_NOTE_NUMBER,
            amount = AMOUNT,
            pendingAmount = AMOUNT,
            status = InvoiceStatus.PENDING,
            type = SettleableType.DEBIT_NOTE
        )

        // Use DataProvider for DraftReceiptEntityMapping
        mockSettlementMapping = DataProvider.draftReceiptEntityMapping(
            receipt = null,
            entityType = SettleableType.DEBIT_NOTE,
            entityId = mockDebitNote.id!!,
            entityTxAmount = AMOUNT,
            active = true
        )

        mockSettlement = mockk(relaxed = true) {
            every { id } returns 1L
            every { retailerDebitNotes } returns mutableListOf(mockDebitNote)
            every { tenant } returns TENANT
            every { createdBy } returns USER
        }

        mockSupplier = DataProvider.supplier(
            partnerId = PARTNER_ID,
            partnerName = PARTNER_NAME
        )

        // Default mock behaviors
        every { debitNoteService.getRetailerDebitNoteByDebitNoteNumber(DEBIT_NOTE_NUMBER) } returns mockDebitNote
        every { debitNoteService.getDebitNoteById(1L) } returns mockDebitNote
        every { supplierProxy.supplier(listOf(PARTNER_ID)) } returns listOf(mockSupplier)
        every { debitNoteService.updateDNSettlement(any(), any(), any()) } returns Pair(mockDebitNote, mockk(relaxed = true))
    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    @Nested
    @DisplayName("getSettleableDetails Tests")
    inner class GetSettleableDetailsTests {

        @Test
        @DisplayName("Should return settleable details by debit note number and tenant")
        fun testGetSettleableDetailsByNumberAndTenant() {
            val result = debitNoteSettleableProcessor.getSettleableDetails(DEBIT_NOTE_NUMBER, TENANT)

            assertNotNull(result)
            assertEquals(mockDebitNote.id, result?.id)
            assertEquals(DEBIT_NOTE_NUMBER, result?.number)
            assertEquals(AMOUNT, result?.amount)
            assertEquals(AMOUNT, result?.pendingAmount)
            assertEquals(SettleableType.DEBIT_NOTE, result?.type)
            verify { debitNoteService.getRetailerDebitNoteByDebitNoteNumber(DEBIT_NOTE_NUMBER) }
        }

        @Test
        @DisplayName("Should return null when debit note is not found by number and tenant")
        fun testGetSettleableDetailsByNumberAndTenantReturnsNull() {
            every { debitNoteService.getRetailerDebitNoteByDebitNoteNumber(DEBIT_NOTE_NUMBER) } returns null

            val result = debitNoteSettleableProcessor.getSettleableDetails(DEBIT_NOTE_NUMBER, TENANT)

            assertNull(result)
            verify { debitNoteService.getRetailerDebitNoteByDebitNoteNumber(DEBIT_NOTE_NUMBER) }
        }

        @Test
        @DisplayName("Should return settleable details by debit note id")
        fun testGetSettleableDetailsById() {
            val result = debitNoteSettleableProcessor.getSettleableDetails(1L)

            assertNotNull(result)
            assertEquals(mockDebitNote.id, result?.id)
            assertEquals(DEBIT_NOTE_NUMBER, result?.number)
            assertEquals(AMOUNT, result?.amount)
            assertEquals(AMOUNT, result?.pendingAmount)
            assertEquals(SettleableType.DEBIT_NOTE, result?.type)
            verify { debitNoteService.getDebitNoteById(1L) }
        }

        @Test
        @DisplayName("Should return null when debit note is not found by id")
        fun testGetSettleableDetailsByIdReturnsNull() {
            every { debitNoteService.getDebitNoteById(1L) } returns null

            val result = debitNoteSettleableProcessor.getSettleableDetails(1L)

            assertNull(result)
            verify { debitNoteService.getDebitNoteById(1L) }
        }
    }

    @Nested
    @DisplayName("constructSettlement Tests")
    inner class ConstructSettlementTests {

        @Test
        @DisplayName("Should construct settlement from receipt, settleable details, and settlement mapping")
        fun testConstructSettlement() {
            val result = debitNoteSettleableProcessor.constructSettlement(mockReceipt, mockSettleableDetails, mockSettlementMapping)

            assertNotNull(result)
            assertEquals(USER, result.createdBy)
            assertEquals(PARTNER_ID, result.supplierId)
            assertEquals(PARTNER_NAME, result.supplierName)
            assertEquals(AMOUNT, result.amount)
            assertEquals(AMOUNT, result.paidAmount)
            assertEquals("Settled via RIO Payment", result.remarks)
            assertEquals(0, result.invoices.size)
            assertEquals(mockReceipt.paymentType, result.paymentType)
            assertEquals(PAYMENT_TRANSACTION_ID, result.paymentReference)
            assertEquals(PARTNER_ID, result.partnerId)
            assertEquals(PARTNER_DETAIL_ID, result.partnerDetailId)
            assertEquals(PartnerType.CUSTOMER, result.type)
            assertEquals(TENANT, result.tenant)
            assertEquals(mockReceipt.source, result.paymentSource)
            assertEquals(1, result.retailerDebitNotes.size)
            verify { supplierProxy.supplier(listOf(PARTNER_ID)) }
            verify { debitNoteService.getRetailerDebitNoteByDebitNoteNumber(DEBIT_NOTE_NUMBER) }
        }

        @Test
        @DisplayName("Should throw exception when debit note is not found")
        fun testConstructSettlementThrowsExceptionWhenDebitNoteNotFound() {
            every { debitNoteService.getRetailerDebitNoteByDebitNoteNumber(DEBIT_NOTE_NUMBER) } returns null

            val exception = assertThrows<RequestException> {
                debitNoteSettleableProcessor.constructSettlement(mockReceipt, mockSettleableDetails, mockSettlementMapping)
            }
            assertEquals("Invoice not found for invoice number: $DEBIT_NOTE_NUMBER", exception.message)
            verify { supplierProxy.supplier(listOf(PARTNER_ID)) }
            verify { debitNoteService.getRetailerDebitNoteByDebitNoteNumber(DEBIT_NOTE_NUMBER) }
        }

        @Test
        @DisplayName("Should throw exception when debit note is already paid")
        fun testConstructSettlementThrowsExceptionWhenDebitNoteAlreadyPaid() {
            val paidDebitNote = mockk<RetailerDebitNote>(relaxed = true) {
                every { id } returns 1L
                every { documentNumber } returns DEBIT_NOTE_NUMBER
                every { amount } returns AMOUNT
                every { amountReceived } returns 0.0
                every { status } returns InvoiceStatus.PAID
                every { partnerId } returns PARTNER_ID
                every { partnerDetailId } returns PARTNER_DETAIL_ID
            }
            every { debitNoteService.getRetailerDebitNoteByDebitNoteNumber(DEBIT_NOTE_NUMBER) } returns paidDebitNote

            val exception = assertThrows<IllegalArgumentException> {
                debitNoteSettleableProcessor.constructSettlement(mockReceipt, mockSettleableDetails, mockSettlementMapping)
            }
            assertEquals("Invoice $DEBIT_NOTE_NUMBER is already paid", exception.message)
            verify { supplierProxy.supplier(listOf(PARTNER_ID)) }
            verify { debitNoteService.getRetailerDebitNoteByDebitNoteNumber(DEBIT_NOTE_NUMBER) }
        }

        @Test
        @DisplayName("Should throw exception when receipt amount is greater than debit note outstanding")
        fun testConstructSettlementThrowsExceptionWhenReceiptAmountGreaterThanOutstanding() {
            val partiallyPaidDebitNote = mockk<RetailerDebitNote>(relaxed = true) {
                every { id } returns 1L
                every { documentNumber } returns DEBIT_NOTE_NUMBER
                every { amount } returns AMOUNT
                every { amountReceived } returns 500.0
                every { status } returns InvoiceStatus.PENDING
                every { partnerId } returns PARTNER_ID
                every { partnerDetailId } returns PARTNER_DETAIL_ID
            }
            every { debitNoteService.getRetailerDebitNoteByDebitNoteNumber(DEBIT_NOTE_NUMBER) } returns partiallyPaidDebitNote

            // Create a new DraftReceiptEntityMapping with higher entityTxAmount
            val largeAmountMapping = DataProvider.draftReceiptEntityMapping(
                receipt = null,
                entityType = SettleableType.DEBIT_NOTE,
                entityId = mockDebitNote.id!!,
                entityTxAmount = 600.0,
                active = true
            )

            val exception = assertThrows<IllegalArgumentException> {
                debitNoteSettleableProcessor.constructSettlement(mockReceipt, mockSettleableDetails, largeAmountMapping)
            }
            assertEquals("Receipt amount 600.0 is greater than invoice outstanding 500.0", exception.message)
            verify { supplierProxy.supplier(listOf(PARTNER_ID)) }
            verify { debitNoteService.getRetailerDebitNoteByDebitNoteNumber(DEBIT_NOTE_NUMBER) }
        }
    }

    @Nested
    @DisplayName("updateSettlementMapping Tests")
    inner class UpdateSettlementMappingTests {

        @Test
        @DisplayName("Should update settlement mapping")
        fun testUpdateSettlementMapping() {
            debitNoteSettleableProcessor.updateSettlementMapping(mockSettlement, mockSettleableDetails, mockSettlementMapping)

            verify { debitNoteService.getRetailerDebitNoteByDebitNoteNumber(any()) }
            verify { debitNoteService.updateDNSettlement(any(), any(), any()) }
            verify { invoiceSettlementUpdateHandler.createSettlementConsumer(any(), any()) }
        }

        @Test
        @DisplayName("Should handle null debit note when updating settlement mapping")
        fun testUpdateSettlementMappingHandlesNullDebitNote() {
            every { debitNoteService.getRetailerDebitNoteByDebitNoteNumber(any()) } returns null

            debitNoteSettleableProcessor.updateSettlementMapping(mockSettlement, mockSettleableDetails, mockSettlementMapping)

            verify { debitNoteService.getRetailerDebitNoteByDebitNoteNumber(any()) }
            verify(exactly = 0) { debitNoteService.updateDNSettlement(any(), any(), any()) }
            verify(exactly = 0) { invoiceSettlementUpdateHandler.createSettlementConsumer(any(), any()) }
        }
    }
}
