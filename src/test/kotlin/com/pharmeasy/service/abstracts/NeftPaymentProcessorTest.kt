package com.pharmeasy.service.abstracts

import com.pharmeasy.model.ops.BankDetails
import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.service.DraftReceiptService
import com.pharmeasy.service.NeftPaymentProcessor
import com.pharmeasy.type.PaymentType
import com.pharmeasy.utils.DataProvider
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class NeftPaymentProcessorTest {

    private lateinit var neftPaymentProcessor: NeftPaymentProcessor
    private lateinit var draftReceiptService: DraftReceiptService

    private val neftId = "NEFT123456"
    private val partnerDetailId = 1L
    private val tenant = "th001"
    private val transactionId = "TX123456"

    @BeforeEach
    fun setUp() {
        draftReceiptService = mockk(relaxed = true)
        neftPaymentProcessor = NeftPaymentProcessor(draftReceiptService)
    }

    @Test
    @DisplayName("validatePayment should return error when NEFT ID is missing")
    fun testValidatePaymentWithMissingNeftId() {
        // Arrange
        val paymentInfo = PaymentInfo(
            customerTransactionId = transactionId,
            transactionDate = System.currentTimeMillis(),
            transactionAmount = 1000.0,
            paymentType = PaymentType.NEFT,
            paymentMode = "NEFT",
            initiatedBy = "USER",
            bankDetails = BankDetails(
                neftId = null,
                bankName = "Test Bank"
            )
        )
        
        val partnerInfo = PartnerInfo(
            partnerId = 1L,
            partnerDetailId = partnerDetailId,
            partnerName = "Test Partner",
            tenant = tenant,
            distributorPdi = 2L
        )
        
        every { draftReceiptService.findDuplicatesByPaymentTransactionId(any(), any(), any()) } returns emptyList()
        
        // Act
        val result = assertThrows<IllegalArgumentException> {
            neftPaymentProcessor.validatePayment(paymentInfo, partnerInfo)
        }
        
        // Assert
        assertEquals("NEFT ID is required for NEFT payments", result.message)
    }

    @Test
    @DisplayName("validatePayment should return error when NEFT ID is blank")
    fun testValidatePaymentWithBlankNeftId() {
        // Arrange
        val paymentInfo = PaymentInfo(
            customerTransactionId = transactionId,
            transactionDate = System.currentTimeMillis(),
            transactionAmount = 1000.0,
            paymentType = PaymentType.NEFT,
            paymentMode = "NEFT",
            initiatedBy = "USER",
            bankDetails = BankDetails(
                neftId = "",
                bankName = "Test Bank"
            )
        )
        
        val partnerInfo = PartnerInfo(
            partnerId = 1L,
            partnerDetailId = partnerDetailId,
            partnerName = "Test Partner",
            tenant = tenant,
            distributorPdi = 2L
        )
        
        every { draftReceiptService.findDuplicatesByPaymentTransactionId(any(), any(), any()) } returns emptyList()
        
        // Act
        val result = assertThrows<IllegalArgumentException> {
            neftPaymentProcessor.validatePayment(paymentInfo, partnerInfo)
        }
        
        // Assert
        assertEquals("NEFT ID is required for NEFT payments", result.message)
    }

    @Test
    @DisplayName("validatePayment should return null when NEFT ID is valid and no duplicates")
    fun testValidatePaymentWithValidNeftId() {
        // Arrange
        val paymentInfo = PaymentInfo(
            customerTransactionId = transactionId,
            transactionDate = System.currentTimeMillis(),
            transactionAmount = 1000.0,
            paymentType = PaymentType.NEFT,
            paymentMode = "NEFT",
            initiatedBy = "USER",
            bankDetails = BankDetails(
                neftId = neftId,
                bankName = "Test Bank"
            )
        )
        
        val partnerInfo = PartnerInfo(
            partnerId = 1L,
            partnerDetailId = partnerDetailId,
            partnerName = "Test Partner",
            tenant = tenant,
            distributorPdi = 2L
        )
        
        every { draftReceiptService.findDuplicatesByPaymentTransactionId(any(), any(), any()) } returns emptyList()
        every { draftReceiptService.findDuplicateNeftPayments(neftId, partnerDetailId, tenant, any()) } returns emptyList()
        
        // Act
        val result = neftPaymentProcessor.validatePayment(paymentInfo, partnerInfo)
        
        // Assert
        assertNull(result)
        verify { draftReceiptService.findDuplicateNeftPayments(neftId, partnerDetailId, tenant, any()) }
    }

    @Test
    @DisplayName("validatePayment should return error when duplicate NEFT ID exists")
    fun testValidatePaymentWithDuplicateNeftId() {
        // Arrange
        val paymentInfo = PaymentInfo(
            customerTransactionId = transactionId,
            transactionDate = System.currentTimeMillis(),
            transactionAmount = 1000.0,
            paymentType = PaymentType.NEFT,
            paymentMode = "NEFT",
            initiatedBy = "USER",
            bankDetails = BankDetails(
                neftId = neftId,
                bankName = "Test Bank"
            )
        )
        
        val partnerInfo = PartnerInfo(
            partnerId = 1L,
            partnerDetailId = partnerDetailId,
            partnerName = "Test Partner",
            tenant = tenant,
            distributorPdi = 2L
        )
        
        val existingReceipt = DataProvider.draftReceipt(
            id = 1L,
            receiptNumber = "REC-001",
            paymentTransactionId = "TX-EXISTING",
            amount = 1000.0,
            paymentType = PaymentType.NEFT,
            txReferenceNumber = neftId
        )
        
        every { draftReceiptService.findDuplicatesByPaymentTransactionId(any(), any(), any()) } returns emptyList()
        every { draftReceiptService.findDuplicateNeftPayments(neftId, partnerDetailId, tenant, any()) } returns listOf(existingReceipt)

        // Act
        val result = neftPaymentProcessor.validatePayment(paymentInfo, partnerInfo)
        
        // Assert
        assertEquals("NEFT ID $neftId already exists for the the partner in receipt: REC-001", result)
        verify { draftReceiptService.findDuplicateNeftPayments(neftId, partnerDetailId, tenant, any()) }
    }
}
