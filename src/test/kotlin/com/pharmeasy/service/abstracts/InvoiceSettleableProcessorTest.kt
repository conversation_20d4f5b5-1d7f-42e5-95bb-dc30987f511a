package com.pharmeasy.service.abstracts

import com.pharmeasy.data.BkInvoice
import com.pharmeasy.data.DraftReceiptEntityMapping
import com.pharmeasy.data.InvoiceSettlement
import com.pharmeasy.data.Receipt
import com.pharmeasy.data.Settlement
import com.pharmeasy.dto.SettleableDetails
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.InvoiceStatus
import com.pharmeasy.model.Supplier
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.service.InvoiceService
import com.pharmeasy.service.InvoiceSettleableProcessor
import com.pharmeasy.service.SlipService
import com.pharmeasy.stream.BlockedVendorSettlementPusher
import com.pharmeasy.stream.InvoiceSettlementUpdateHandler
import com.pharmeasy.stream.SettlementInvoiceListGateway
import com.pharmeasy.type.CreationType
import com.pharmeasy.type.PartnerType
import com.pharmeasy.type.SettleableType
import com.pharmeasy.type.SlipStatus
import com.pharmeasy.utils.DataProvider
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal

class InvoiceSettleableProcessorTest {

    private lateinit var invoiceSettleableProcessor: InvoiceSettleableProcessor
    private lateinit var invoiceService: InvoiceService
    private lateinit var supplierProxy: SupplierProxy
    private lateinit var slipService: SlipService
    private lateinit var invoiceSettlementUpdateHandler: InvoiceSettlementUpdateHandler
    private lateinit var blockedVendorSettlementPusher: BlockedVendorSettlementPusher
    private lateinit var settlementInvoiceListGateway: SettlementInvoiceListGateway

    companion object {
        const val INVOICE_NUMBER = DataProvider.INVOICE_NUMBER
        const val TENANT = DataProvider.TENANT
        const val PARTNER_ID = DataProvider.PARTNER_ID
        const val PARTNER_DETAIL_ID = DataProvider.PDI
        const val PAYMENT_TRANSACTION_ID = DataProvider.TX_ID
        const val AMOUNT = 1000.0
        const val USER = "test-user"
        const val PARTNER_NAME = DataProvider.PARTNER_NAME
    }

    private lateinit var mockReceipt: Receipt
    private lateinit var mockInvoice: BkInvoice
    private lateinit var mockSettleableDetails: SettleableDetails
    private lateinit var mockSettlementMapping: DraftReceiptEntityMapping
    private lateinit var mockSettlement: Settlement
    private lateinit var mockSupplier: Supplier

    @BeforeEach
    fun setUp() {
        // Initialize all mocks
        invoiceService = mockk(relaxed = true)
        supplierProxy = mockk(relaxed = true)
        slipService = mockk(relaxed = true)
        invoiceSettlementUpdateHandler = mockk(relaxed = true)
        blockedVendorSettlementPusher = mockk(relaxed = true)
        settlementInvoiceListGateway = mockk(relaxed = true)

        // Create the processor with mocked dependencies
        invoiceSettleableProcessor = InvoiceSettleableProcessor(
            invoiceService,
            supplierProxy,
            slipService,
            invoiceSettlementUpdateHandler,
            blockedVendorSettlementPusher,
            settlementInvoiceListGateway
        )

        // Create mock objects
        mockReceipt = DataProvider.receipt(
            id = 1L,
            receiptNumber = "REC-001",
            paymentTransactionId = PAYMENT_TRANSACTION_ID,
            amount = AMOUNT,
            updatedBy = USER,
            tenant = TENANT,
            partnerId = PARTNER_ID,
            partnerDetailId = PARTNER_DETAIL_ID
        )

        mockInvoice = DataProvider.bkInvoice(
            id = 1L,
            invoiceNum = INVOICE_NUMBER,
            amount = AMOUNT,
            paidAmount = 0.0,
            tenant = TENANT,
            partnerId = PARTNER_ID,
            partnerDetailId = PARTNER_DETAIL_ID
        )

        mockSettleableDetails = SettleableDetails(
            mockInvoice.id,
            INVOICE_NUMBER,
            AMOUNT,
            AMOUNT,
            InvoiceStatus.PENDING,
            SettleableType.INVOICE
        )

        mockSettlementMapping = mockk(relaxed = true) {
            every { entityId } returns mockInvoice.id
            every { entityType } returns SettleableType.INVOICE
            every { entityTxAmount } returns AMOUNT
        }

        mockSettlement = mockk(relaxed = true) {
            every { id } returns 1L
            every { invoices } returns mutableListOf(mockInvoice)
            every { tenant } returns TENANT
            every { createdBy } returns USER
        }

        mockSupplier = DataProvider.supplier(
            partnerId = PARTNER_ID,
            partnerName = PARTNER_NAME
        )

        // Default mock behaviors
        every { invoiceService.findCustomerInvoiceByInvoiceNumAndTenant(INVOICE_NUMBER, TENANT) } returns mockInvoice
        every { invoiceService.getBkInvoiceById(1L) } returns mockInvoice
        every { supplierProxy.supplier(listOf(PARTNER_ID)) } returns listOf(mockSupplier)
    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    @Nested
    @DisplayName("getSettleableDetails Tests")
    inner class GetSettleableDetailsTests {

        @Test
        @DisplayName("Should return settleable details by invoice number and tenant")
        fun testGetSettleableDetailsByNumberAndTenant() {
            // Act
            val result = invoiceSettleableProcessor.getSettleableDetails(INVOICE_NUMBER, TENANT)

            // Assert
            assertNotNull(result)
            assertEquals(mockInvoice.id, result?.id)
            assertEquals(INVOICE_NUMBER, result?.number)
            assertEquals(AMOUNT, result?.amount)
            assertEquals(AMOUNT, result?.pendingAmount)
            assertEquals(SettleableType.INVOICE, result?.type)
            verify { invoiceService.findCustomerInvoiceByInvoiceNumAndTenant(INVOICE_NUMBER, TENANT) }
        }

        @Test
        @DisplayName("Should return null when invoice is not found by number and tenant")
        fun testGetSettleableDetailsByNumberAndTenantReturnsNull() {
            // Arrange
            every { invoiceService.findCustomerInvoiceByInvoiceNumAndTenant(INVOICE_NUMBER, TENANT) } returns null

            // Act
            val result = invoiceSettleableProcessor.getSettleableDetails(INVOICE_NUMBER, TENANT)

            // Assert
            assertNull(result)
            verify { invoiceService.findCustomerInvoiceByInvoiceNumAndTenant(INVOICE_NUMBER, TENANT) }
        }

        @Test
        @DisplayName("Should return settleable details by invoice id")
        fun testGetSettleableDetailsById() {
            // Act
            val result = invoiceSettleableProcessor.getSettleableDetails(1L)

            // Assert
            assertNotNull(result)
            assertEquals(mockInvoice.id, result?.id)
            assertEquals(INVOICE_NUMBER, result?.number)
            assertEquals(AMOUNT, result?.amount)
            assertEquals(AMOUNT, result?.pendingAmount)
            assertEquals(SettleableType.INVOICE, result?.type)
            verify { invoiceService.getBkInvoiceById(1L) }
        }

        @Test
        @DisplayName("Should return null when invoice is not found by id")
        fun testGetSettleableDetailsByIdReturnsNull() {
            // Arrange
            every { invoiceService.getBkInvoiceById(1L) } returns null

            // Act
            val result = invoiceSettleableProcessor.getSettleableDetails(1L)

            // Assert
            assertNull(result)
            verify { invoiceService.getBkInvoiceById(1L) }
        }
    }

    @Nested
    @DisplayName("constructSettlement Tests")
    inner class ConstructSettlementTests {

        @Test
        @DisplayName("Should construct settlement from receipt, settleable details, and settlement mapping")
        fun testConstructSettlement() {
            // Act
            val result = invoiceSettleableProcessor.constructSettlement(
                mockReceipt,
                mockSettleableDetails,
                mockSettlementMapping
            )

            // Assert
            assertNotNull(result)
            assertEquals(USER, result.createdBy)
            assertEquals(PARTNER_ID, result.supplierId)
            assertEquals(PARTNER_NAME, result.supplierName)
            assertEquals(AMOUNT, result.amount)
            assertEquals(AMOUNT, result.paidAmount)
            assertEquals("Settled via RIO Payment", result.remarks)
            assertEquals(1, result.invoices.size)
            assertEquals(INVOICE_NUMBER, result.invoices[0].invoiceNum)
            assertEquals(mockReceipt.paymentType, result.paymentType)
            assertEquals(PAYMENT_TRANSACTION_ID, result.paymentReference)
            assertEquals(PARTNER_ID, result.partnerId)
            assertEquals(PARTNER_DETAIL_ID, result.partnerDetailId)
            assertEquals(PartnerType.CUSTOMER, result.type)
            assertEquals(TENANT, result.tenant)
            assertEquals(mockReceipt.source, result.paymentSource)
            verify { supplierProxy.supplier(listOf(PARTNER_ID)) }
            verify { invoiceService.findCustomerInvoiceByInvoiceNumAndTenant(INVOICE_NUMBER, TENANT) }
        }

        @Test
        @DisplayName("Should throw exception when invoice is not found")
        fun testConstructSettlementThrowsExceptionWhenInvoiceNotFound() {
            // Arrange
            every { invoiceService.findCustomerInvoiceByInvoiceNumAndTenant(INVOICE_NUMBER, TENANT) } returns null

            // Act & Assert
            val exception = assertThrows<RequestException> {
                invoiceSettleableProcessor.constructSettlement(
                    mockReceipt,
                    mockSettleableDetails,
                    mockSettlementMapping
                )
            }
            assertEquals("Invoice not found for invoice number: $INVOICE_NUMBER", exception.message)
            verify { supplierProxy.supplier(listOf(PARTNER_ID)) }
            verify { invoiceService.findCustomerInvoiceByInvoiceNumAndTenant(INVOICE_NUMBER, TENANT) }
        }

        @Test
        @DisplayName("Should throw exception when invoice is already paid")
        fun testConstructSettlementThrowsExceptionWhenInvoiceAlreadyPaid() {
            // Arrange
            val paidInvoice = mockInvoice.copy(status = InvoiceStatus.PAID)
            every {
                invoiceService.findCustomerInvoiceByInvoiceNumAndTenant(
                    INVOICE_NUMBER,
                    TENANT
                )
            } returns paidInvoice

            // Act & Assert
            val exception = assertThrows<IllegalArgumentException> {
                invoiceSettleableProcessor.constructSettlement(
                    mockReceipt,
                    mockSettleableDetails,
                    mockSettlementMapping
                )
            }
            assertEquals("Invoice $INVOICE_NUMBER is already paid", exception.message)
            verify { supplierProxy.supplier(listOf(PARTNER_ID)) }
            verify { invoiceService.findCustomerInvoiceByInvoiceNumAndTenant(INVOICE_NUMBER, TENANT) }
        }

        @Test
        @DisplayName("Should throw exception when receipt amount is greater than invoice outstanding")
        fun testConstructSettlementThrowsExceptionWhenReceiptAmountGreaterThanOutstanding() {
            // Arrange
            val partiallyPaidInvoice = mockInvoice.copy(paidAmount = 500.0)
            every {
                invoiceService.findCustomerInvoiceByInvoiceNumAndTenant(
                    INVOICE_NUMBER,
                    TENANT
                )
            } returns partiallyPaidInvoice
            every { mockSettlementMapping.entityTxAmount } returns 600.0

            // Act & Assert
            val exception = assertThrows<IllegalArgumentException> {
                invoiceSettleableProcessor.constructSettlement(
                    mockReceipt,
                    mockSettleableDetails,
                    mockSettlementMapping
                )
            }
            assertEquals("Receipt amount 600.0 is greater than invoice outstanding 500.0", exception.message)
            verify { supplierProxy.supplier(listOf(PARTNER_ID)) }
            verify { invoiceService.findCustomerInvoiceByInvoiceNumAndTenant(INVOICE_NUMBER, TENANT) }
        }
    }

    @Nested
    @DisplayName("updateSettlementMapping Tests")
    inner class UpdateSettlementMappingTests {

        @Test
        @DisplayName("Should update settlement mapping")
        fun testUpdateSettlementMapping() {
            // Arrange
            val invoiceList = listOf(mockInvoice.copy())
            val invoiceSettlement = mockk<InvoiceSettlement>(relaxed = true) {
                every { paidAmount } returns BigDecimal.valueOf(AMOUNT)
            }
            every { invoiceService.findInvoicesByNumberAndTenant(any()) } returns invoiceList
            every { invoiceService.updateInvoiceForSettlement(any(), any(), any()) } returns Pair(
                mockInvoice.copy(
                    status = InvoiceStatus.PAID
                ), invoiceSettlement
            )

            // Act
            invoiceSettleableProcessor.updateSettlementMapping(
                mockSettlement,
                mockSettleableDetails,
                mockSettlementMapping
            )

            // Assert
            verify { invoiceService.findInvoicesByNumberAndTenant(any()) }
            verify { invoiceService.updateInvoiceForSettlement(any(), mockSettlementMapping, mockSettlement) }
            verify { settlementInvoiceListGateway.sendInvoiceListProducerEvent(any()) }
            verify { invoiceSettlementUpdateHandler.createSettlementConsumer(mockInvoice.id, CreationType.INVOICE) }
            verify { blockedVendorSettlementPusher.blockedVendorSettlementSinkProducer(any()) }
            verify { slipService.statusChangeSlip(any(), SlipStatus.CLOSED, any()) }
        }

        @Test
        @DisplayName("Should handle RequestException when closing slips")
        fun testUpdateSettlementMappingHandlesRequestException() {
            // Arrange
            val invoiceList = listOf(mockInvoice.copy())
            val invoiceSettlement = mockk<InvoiceSettlement>(relaxed = true) {
                every { paidAmount } returns BigDecimal.valueOf(AMOUNT)
            }
            every { invoiceService.findInvoicesByNumberAndTenant(any()) } returns invoiceList
            every { invoiceService.updateInvoiceForSettlement(any(), any(), any()) } returns Pair(
                mockInvoice.copy(
                    status = InvoiceStatus.PAID
                ), invoiceSettlement
            )
            every { slipService.statusChangeSlip(any(), any(), any()) } throws RequestException("Failed to close slips")

            // Act
            invoiceSettleableProcessor.updateSettlementMapping(
                mockSettlement,
                mockSettleableDetails,
                mockSettlementMapping
            )

            // Assert
            verify { invoiceService.findInvoicesByNumberAndTenant(any()) }
            verify { invoiceService.updateInvoiceForSettlement(any(), mockSettlementMapping, mockSettlement) }
            verify { settlementInvoiceListGateway.sendInvoiceListProducerEvent(any()) }
            verify { invoiceSettlementUpdateHandler.createSettlementConsumer(mockInvoice.id, CreationType.INVOICE) }
            verify { blockedVendorSettlementPusher.blockedVendorSettlementSinkProducer(any()) }
            verify { slipService.statusChangeSlip(any(), SlipStatus.CLOSED, any()) }
        }

        @Test
        @DisplayName("Should handle generic Exception when closing slips")
        fun testUpdateSettlementMappingHandlesGenericException() {
            // Arrange
            val invoiceList = listOf(mockInvoice.copy())
            val invoiceSettlement = mockk<InvoiceSettlement>(relaxed = true) {
                every { paidAmount } returns BigDecimal.valueOf(AMOUNT)
            }
            every { invoiceService.findInvoicesByNumberAndTenant(any()) } returns invoiceList
            every { invoiceService.updateInvoiceForSettlement(any(), any(), any()) } returns Pair(
                mockInvoice.copy(
                    status = InvoiceStatus.PAID
                ), invoiceSettlement
            )
            every { slipService.statusChangeSlip(any(), any(), any()) } throws RuntimeException("Unexpected error")

            // Act
            invoiceSettleableProcessor.updateSettlementMapping(
                mockSettlement,
                mockSettleableDetails,
                mockSettlementMapping
            )

            // Assert
            verify { invoiceService.findInvoicesByNumberAndTenant(any()) }
            verify { invoiceService.updateInvoiceForSettlement(any(), mockSettlementMapping, mockSettlement) }
            verify { settlementInvoiceListGateway.sendInvoiceListProducerEvent(any()) }
            verify { invoiceSettlementUpdateHandler.createSettlementConsumer(mockInvoice.id, CreationType.INVOICE) }
            verify { blockedVendorSettlementPusher.blockedVendorSettlementSinkProducer(any()) }
            verify { slipService.statusChangeSlip(any(), SlipStatus.CLOSED, any()) }
        }
    }
}
