package com.pharmeasy.service

import com.pharmeasy.data.RetailerDebitNoteSettlementMapping
import com.pharmeasy.repo.RetailerDebitNoteSettlementMappingRepo
import com.pharmeasy.utils.DataProvider
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertSame
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.test.util.ReflectionTestUtils

class RetailerDebitNoteSettlementMappingServiceTest {

    private lateinit var retailerDebitNoteSettlementMappingService: RetailerDebitNoteSettlementMappingService
    private lateinit var retailerDebitNoteSettlementMappingRepo: RetailerDebitNoteSettlementMappingRepo

    @BeforeEach
    fun setUp() {
        retailerDebitNoteSettlementMappingRepo = mockk(relaxed = true)
        retailerDebitNoteSettlementMappingService = RetailerDebitNoteSettlementMappingService()

        // Inject the mocked repository using reflection
        ReflectionTestUtils.setField(
            retailerDebitNoteSettlementMappingService, 
            "retailerDebitNoteSettlementMappingRepo", 
            retailerDebitNoteSettlementMappingRepo
        )
    }

    @Test
    @DisplayName("saveAll should save and return list of mappings")
    fun testSaveAll() {
        // Arrange
        val retailerDebitNote = DataProvider.retailerDebitNote(id = 1L)
        val settlement = DataProvider.settlement(id = 1L)

        val mapping1 = RetailerDebitNoteSettlementMapping(
            retailerDebitNoteId = retailerDebitNote,
            settlementId = settlement,
            paidAmount = 500.0
        )

        val mapping2 = RetailerDebitNoteSettlementMapping(
            retailerDebitNoteId = retailerDebitNote,
            settlementId = settlement,
            paidAmount = 300.0
        )

        val mappings = listOf(mapping1, mapping2)

        every { retailerDebitNoteSettlementMappingRepo.saveAll(mappings) } returns mappings

        // Act
        val result = retailerDebitNoteSettlementMappingService.saveAll(mappings)

        // Assert
        assertEquals(2, result.size)
        assertEquals(mappings, result)
        verify { retailerDebitNoteSettlementMappingRepo.saveAll(mappings) }
    }

    @Test
    @DisplayName("save should save and return a single mapping")
    fun testSave() {
        // Arrange
        val retailerDebitNote = DataProvider.retailerDebitNote(id = 1L)
        val settlement = DataProvider.settlement(id = 1L)

        val mapping = RetailerDebitNoteSettlementMapping(
            retailerDebitNoteId = retailerDebitNote,
            settlementId = settlement,
            paidAmount = 500.0
        )

        every { retailerDebitNoteSettlementMappingRepo.save(mapping) } returns mapping

        // Act
        val result = retailerDebitNoteSettlementMappingService.save(mapping)

        // Assert
        assertSame(mapping, result)
        verify { retailerDebitNoteSettlementMappingRepo.save(mapping) }
    }

    @Test
    @DisplayName("getRetailerDebitNoteSettlementMapping should return mapping by retailer debit note ID and settlement ID")
    fun testGetRetailerDebitNoteSettlementMapping() {
        // Arrange
        val retailerDebitNoteId = 1L
        val settlementId = 2L

        val retailerDebitNote = DataProvider.retailerDebitNote(id = retailerDebitNoteId)
        val settlement = DataProvider.settlement(id = settlementId)

        val mapping = RetailerDebitNoteSettlementMapping(
            retailerDebitNoteId = retailerDebitNote,
            settlementId = settlement,
            paidAmount = 500.0
        )

        every { 
            retailerDebitNoteSettlementMappingRepo.findByRetailerDNIdAndSettlementId(
                retailerDebitNoteId, 
                settlementId
            ) 
        } returns mapping

        // Act
        val result = retailerDebitNoteSettlementMappingService.getRetailerDebitNoteSettlementMapping(
            retailerDebitNoteId, 
            settlementId
        )

        // Assert
        assertSame(mapping, result)
        verify { 
            retailerDebitNoteSettlementMappingRepo.findByRetailerDNIdAndSettlementId(
                retailerDebitNoteId, 
                settlementId
            ) 
        }
    }
}
