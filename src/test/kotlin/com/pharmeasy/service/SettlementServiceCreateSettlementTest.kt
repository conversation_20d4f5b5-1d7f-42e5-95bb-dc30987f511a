package com.pharmeasy.service

import com.pharmeasy.data.Company
import com.pharmeasy.data.DraftReceiptEntityMapping
import com.pharmeasy.data.Receipt
import com.pharmeasy.data.Settlement
import com.pharmeasy.dto.SettleableDetails
import com.pharmeasy.exception.DetailedRequestException
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.ops.SettlementGroupNumber
import com.pharmeasy.repo.CompanyRepo
import com.pharmeasy.repo.SettlementRepo
import com.pharmeasy.service.abstracts.BaseSettleableProcessor
import com.pharmeasy.type.DocumentType
import com.pharmeasy.type.PartnerType
import com.pharmeasy.type.SettleableType
import com.pharmeasy.utils.DataProvider
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.test.util.ReflectionTestUtils
import java.math.BigDecimal

class SettlementServiceCreateSettlementTest {

    private lateinit var settlementService: SettlementService
    private lateinit var settleableProcessorFactory: SettleableProcessorFactory
    private lateinit var companyRepo: CompanyRepo
    private lateinit var settlementRepo: SettlementRepo
    private lateinit var documentMasterService: DocumentMasterService

    companion object {
        const val RECEIPT_NUMBER = "REC-001"
        const val TENANT = DataProvider.TENANT
        const val PARTNER_ID = DataProvider.PARTNER_ID
        const val PARTNER_DETAIL_ID = DataProvider.PDI
        const val PAYMENT_TRANSACTION_ID = DataProvider.TX_ID
        const val AMOUNT = 1000.0
        const val USER = "test-user"
        const val PARTNER_NAME = DataProvider.PARTNER_NAME
        const val SETTLEMENT_NUMBER = "SET-001"
        const val COMPANY_CODE = DataProvider.COMPANY_CODE
    }

    private lateinit var mockReceipt: Receipt
    private lateinit var mockSettlementMapping: DraftReceiptEntityMapping
    private lateinit var mockSettlementGroupNumber: SettlementGroupNumber
    private lateinit var mockSettleableProcessor: BaseSettleableProcessor
    private lateinit var mockSettleableDetails: SettleableDetails
    private lateinit var mockSettlement: Settlement
    private lateinit var mockCompany: Company

    @BeforeEach
    fun setUp() {
        // Initialize all mocks
        settleableProcessorFactory = mockk(relaxed = true)
        companyRepo = mockk(relaxed = true)
        settlementRepo = mockk(relaxed = true)
        documentMasterService = mockk(relaxed = true)
        mockSettleableProcessor = mockk(relaxed = true)

        // Create the service with mocked dependencies
        settlementService = SettlementService(1000.0, BigDecimal.valueOf(0.5), settleableProcessorFactory)

        // Use ReflectionTestUtils to set private fields
        ReflectionTestUtils.setField(settlementService, "companyRepo", companyRepo)
        ReflectionTestUtils.setField(settlementService, "settlementRepo", settlementRepo)
        ReflectionTestUtils.setField(settlementService, "documentMasterService", documentMasterService)

        // Create mock objects
        mockReceipt = DataProvider.receipt(
            id = 1L,
            receiptNumber = RECEIPT_NUMBER,
            paymentTransactionId = PAYMENT_TRANSACTION_ID,
            amount = AMOUNT,
            updatedBy = USER,
            tenant = TENANT,
            partnerId = PARTNER_ID,
            partnerDetailId = PARTNER_DETAIL_ID
        )

        mockSettlementMapping = mockk(relaxed = true) {
            every { entityId } returns 1L
            every { entityType } returns SettleableType.INVOICE
            every { entityTxAmount } returns AMOUNT
        }

        mockSettlementGroupNumber = spyk(SettlementGroupNumber("SGN-001"))

        mockSettleableDetails = mockk(relaxed = true) {
            every { id } returns 1L
            every { number } returns DataProvider.INVOICE_NUMBER
            every { amount } returns AMOUNT
            every { pendingAmount } returns AMOUNT
            every { type } returns SettleableType.INVOICE
        }

        mockSettlement = mockk(relaxed = true) {
            every { id } returns 1L
            every { tenant } returns TENANT
            every { createdBy } returns USER
            every { type } returns PartnerType.CUSTOMER
        }

        mockCompany = DataProvider.company(
            companyCode = COMPANY_CODE
        )

        // Default mock behaviors
        every { settleableProcessorFactory.getSettleableProcessor(SettleableType.INVOICE) } returns mockSettleableProcessor
        every { mockSettleableProcessor.getSettleableDetails(1L) } returns mockSettleableDetails
        every { mockSettleableProcessor.constructSettlement(mockReceipt, mockSettleableDetails, mockSettlementMapping) } returns mockSettlement
        every { companyRepo.getCompanyByTenant(TENANT) } returns mockCompany
        every { documentMasterService.getDocumentNumber(USER, COMPANY_CODE, DocumentType.CX_RECEIPT) } returns SETTLEMENT_NUMBER
        every { settlementRepo.save(any()) } answers { 
            val settlement = firstArg<Settlement>()
            settlement.also { it.id = 1L }
        }
    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    @Nested
    @DisplayName("createSettlementFromReceipt Tests")
    inner class CreateSettlementFromReceiptTests {

        @Test
        @DisplayName("Should create settlement from receipt")
        fun testCreateSettlementFromReceipt() {
            // Act
            val result = settlementService.createSettlementFromReceipt(mockReceipt, mockSettlementMapping, mockSettlementGroupNumber)

            // Assert
            assertNotNull(result)
            verify { settleableProcessorFactory.getSettleableProcessor(SettleableType.INVOICE) }
            verify { mockSettleableProcessor.getSettleableDetails(1L) }
            verify { mockSettleableProcessor.constructSettlement(mockReceipt, mockSettleableDetails, mockSettlementMapping) }
            verify { companyRepo.getCompanyByTenant(TENANT) }
            verify { mockSettlementGroupNumber.next() }
            verify { settlementRepo.save(mockSettlement) }
            verify { mockSettleableProcessor.updateSettlementMapping(mockSettlement, mockSettleableDetails, mockSettlementMapping) }
        }

        @Test
        @DisplayName("Should throw exception when settleable details not found")
        fun testCreateSettlementFromReceiptThrowsExceptionWhenSettleableDetailsNotFound() {
            // Arrange
            every { mockSettleableProcessor.getSettleableDetails(1L) } returns null

            // Act & Assert
            val exception = assertThrows<RequestException> {
                settlementService.createSettlementFromReceipt(mockReceipt, mockSettlementMapping, mockSettlementGroupNumber)
            }
            assertEquals("No invoice/DN found for id 1", exception.message)
            verify { settleableProcessorFactory.getSettleableProcessor(SettleableType.INVOICE) }
            verify { mockSettleableProcessor.getSettleableDetails(1L) }
            verify(exactly = 0) { mockSettleableProcessor.constructSettlement(any(), any(), any()) }
        }

        @Test
        @DisplayName("Should throw exception when company not found")
        fun testCreateSettlementFromReceiptThrowsExceptionWhenCompanyNotFound() {
            // Arrange
            every { companyRepo.getCompanyByTenant(TENANT) } returns null

            // Act & Assert
            val exception = assertThrows<RequestException> {
                settlementService.createSettlementFromReceipt(mockReceipt, mockSettlementMapping, mockSettlementGroupNumber)
            }
            assertEquals("Company mapping not found for $TENANT", exception.message)
            verify { settleableProcessorFactory.getSettleableProcessor(SettleableType.INVOICE) }
            verify { mockSettleableProcessor.getSettleableDetails(1L) }
            verify { mockSettleableProcessor.constructSettlement(mockReceipt, mockSettleableDetails, mockSettlementMapping) }
            verify { companyRepo.getCompanyByTenant(TENANT) }
        }

        @Test
        @DisplayName("Should throw exception when duplicate settlement")
        fun testCreateSettlementFromReceiptThrowsExceptionWhenDuplicateSettlement() {
            // Arrange
            every { settlementRepo.save(any()) } throws DataIntegrityViolationException("Duplicate Settlement")

            // Act & Assert
            val exception = assertThrows<DetailedRequestException> {
                settlementService.createSettlementFromReceipt(mockReceipt, mockSettlementMapping, mockSettlementGroupNumber)
            }
            assertTrue(exception.params.firstOrNull()?.toString()?.contains("Duplicate Settlement") == true)
            verify { settleableProcessorFactory.getSettleableProcessor(SettleableType.INVOICE) }
            verify { mockSettleableProcessor.getSettleableDetails(1L) }
            verify { mockSettleableProcessor.constructSettlement(mockReceipt, mockSettleableDetails, mockSettlementMapping) }
            verify { companyRepo.getCompanyByTenant(TENANT) }
            verify { mockSettlementGroupNumber.next() }
            verify { settlementRepo.save(mockSettlement) }
        }
    }
}
