package com.pharmeasy.service

import com.pharmeasy.data.Company
import com.pharmeasy.data.DraftReceipt
import com.pharmeasy.data.Receipt
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.ops.CreditDetails
import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentBatchRequest
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.model.ops.SettlementGroupNumber
import com.pharmeasy.proxy.WarehouseProxy
import com.pharmeasy.type.AdvancePaymentSource
import com.pharmeasy.type.DocumentType
import com.pharmeasy.type.PaymentType
import com.pharmeasy.utils.DataProvider
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class ReceiptOrchestratorServiceTest {

    private lateinit var receiptOrchestratorService: ReceiptOrchestratorService
    private lateinit var receiptProcessingService: ReceiptProcessingStrategyImpl
    private lateinit var draftReceiptProcessingService: DraftReceiptProcessingStrategyImpl
    private lateinit var tradeCreditPaymentService: TradeCreditPaymentService
    private lateinit var partnerService: PartnerService
    private lateinit var cashBankLedgerService: CashBankLedgerService
    private lateinit var companyService: CompanyService
    private lateinit var documentService: DocumentMasterService
    private lateinit var draftReceiptService: DraftReceiptService
    private lateinit var receiptService: ReceiptService
    private lateinit var warehouseProxy: WarehouseProxy

    // Static constants for test data
    companion object {
        const val COMPANY_CODE = DataProvider.COMPANY_CODE
        const val TENANT = DataProvider.TENANT
        const val PARTNER_ID = DataProvider.PARTNER_ID
        const val PARTNER_DETAIL_ID = DataProvider.PDI
        const val DISTRIBUTOR_PDI = DataProvider.DISTRIBUTOR_PDI
        const val RECEIPT_NUMBER = "RCPT-001"
        const val PAYMENT_TRANSACTION_ID = DataProvider.TX_ID
        const val AMOUNT = 1000.0
        const val USER = "test-user"
        const val PARTNER_NAME = DataProvider.PARTNER_NAME
    }

    private lateinit var mockCompany: Company
    private lateinit var mockDraftReceipt: DraftReceipt
    private lateinit var mockReceipt: Receipt

    @BeforeEach
    fun setUp() {
        receiptProcessingService = mockk(relaxed = true)
        draftReceiptProcessingService = mockk(relaxed = true)
        tradeCreditPaymentService = mockk(relaxed = true)
        partnerService = mockk(relaxed = true)
        cashBankLedgerService = mockk(relaxed = true)
        companyService = mockk(relaxed = true)
        documentService = mockk(relaxed = true)
        draftReceiptService = mockk(relaxed = true)
        receiptService = mockk(relaxed = true)
        warehouseProxy = mockk(relaxed = true)

        receiptOrchestratorService = spyk(
            ReceiptOrchestratorService(
                receiptProcessingService,
                draftReceiptProcessingService,
                tradeCreditPaymentService,
                partnerService,
                cashBankLedgerService,
                companyService,
                documentService,
                draftReceiptService,
                receiptService,
                warehouseProxy
            )
        )

        mockCompany = DataProvider.company(companyCode = COMPANY_CODE)

        mockDraftReceipt = mockk {
            every { receiptNumber } returns RECEIPT_NUMBER
            every { paymentTransactionId } returns PAYMENT_TRANSACTION_ID
            every { amount } returns AMOUNT
            every { paymentType } returns PaymentType.CASH
            every { tenant } returns TENANT
            every { partnerId } returns PARTNER_ID
            every { partnerDetailId } returns PARTNER_DETAIL_ID
            every { updatedBy } returns USER
        }

        mockReceipt = mockk {
            every { receiptNumber } returns RECEIPT_NUMBER
            every { paymentTransactionId } returns PAYMENT_TRANSACTION_ID
            every { amount } returns AMOUNT
            every { paymentType } returns PaymentType.CASH
            every { tenant } returns TENANT
            every { partnerId } returns PARTNER_ID
            every { partnerDetailId } returns PARTNER_DETAIL_ID
            every { updatedBy } returns USER
            every { remarks } returns "Test receipt"
            every { bankName } returns null
            every { retailerTxnDate } returns null
            every { retailerName } returns null
        }

        // Default mock behaviors
        every { companyService.getCompanyByTenant(any()) } returns mockCompany
        every { documentService.generateDocumentNumber(any(), any()) } returns "SGN-001"
        every { `draftReceiptProcessingService`.createDraftReceipt(any(), any(), any()) } returns mockDraftReceipt
        every { receiptProcessingService.createReceipt(any(), any()) } returns mockReceipt
    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    @Nested
    @DisplayName("processPayment Tests")
    inner class ProcessPaymentTests {

        @Test
        @DisplayName("Should process payment with DraftReceipt strategy successfully")
        fun testProcessPaymentWithDraftReceiptStrategy() {
            // Arrange
            val paymentInfo = PaymentInfo(
                customerTransactionId = PAYMENT_TRANSACTION_ID,
                transactionDate = System.currentTimeMillis(),
                transactionAmount = AMOUNT,
                paymentType = PaymentType.CASH,
                paymentMode = "CASH",
                initiatedBy = "USER"
            )

            val partnerInfo = PartnerInfo(
                partnerId = PARTNER_ID,
                partnerDetailId = PARTNER_DETAIL_ID,
                partnerName = PARTNER_NAME,
                tenant = TENANT,
                distributorPdi = DISTRIBUTOR_PDI
            )

            val paymentBatch = PaymentBatchRequest(
                partnerInfo = partnerInfo,
                payments = listOf(paymentInfo),
                user = USER
            )

            // Act
            receiptOrchestratorService.processPayment(paymentBatch)

            // Assert
            verify { companyService.getCompanyByTenant(TENANT) }
            verify { documentService.generateDocumentNumber(COMPANY_CODE, DocumentType.CX_RECEIPT) }
            verify { `draftReceiptProcessingService`.createDraftReceipt(paymentInfo, partnerInfo, false) }
            verify(exactly = 0) { receiptProcessingService.createReceipt(any(), any()) }
            verify(exactly = 0) { partnerService.checkAndAddLedgerEntry(any(), any()) }
            verify(exactly = 0) { cashBankLedgerService.checkAndAddCashBankLedgerEntry(any(), any()) }
        }

        @Test
        @DisplayName("Should process payment with PreApprovedReceipt strategy successfully")
        fun testProcessPaymentWithPreApprovedReceiptStrategy() {
            // Arrange
            val paymentInfo = PaymentInfo(
                customerTransactionId = "CASH",
                transactionDate = System.currentTimeMillis(),
                transactionAmount = AMOUNT,
                paymentType = PaymentType.CASH,
                paymentMode = "DIGITAL_RECEIPT",
                initiatedBy = "RIO_DELIVERY"
            )

            val partnerInfo = PartnerInfo(
                partnerId = PARTNER_ID,
                partnerDetailId = PARTNER_DETAIL_ID,
                partnerName = PARTNER_NAME,
                tenant = TENANT,
                distributorPdi = DISTRIBUTOR_PDI
            )

            val paymentBatch = PaymentBatchRequest(
                partnerInfo = partnerInfo,
                payments = listOf(paymentInfo),
                user = USER,
                settlementGroupNumber = SettlementGroupNumber("SGN-001")
            )

            // Act
            receiptOrchestratorService.processPayment(paymentBatch)

            // Assert
            verify { companyService.getCompanyByTenant(TENANT) }
            verify { documentService.generateDocumentNumber(COMPANY_CODE, DocumentType.CX_RECEIPT) }
            verify { `draftReceiptProcessingService`.createDraftReceipt(paymentInfo, partnerInfo, true) }
            verify { receiptProcessingService.createReceipt(mockDraftReceipt, paymentBatch.settlementGroupNumber!!) }
        }

        @Test
        @DisplayName("Should handle trade credit payment successfully")
        fun testProcessPaymentWithTradeCreditPayment() {
            // Arrange
            val paymentInfo = PaymentInfo(
                customerTransactionId = "TXN_1234",
                transactionDate = System.currentTimeMillis(),
                transactionAmount = AMOUNT,
                paymentType = PaymentType.TRADE_CREDIT,
                paymentMode = "DIGITAL_PAYMENT",
                initiatedBy = "RIO_DELIVERY",
                creditDetails = CreditDetails(
                    transactionId = "CREDIT-001",
                    dueDate = System.currentTimeMillis(),
                    partner = null
                )
            )

            val partnerInfo = PartnerInfo(
                partnerId = PARTNER_ID,
                partnerDetailId = PARTNER_DETAIL_ID,
                partnerName = PARTNER_NAME,
                tenant = TENANT,
                distributorPdi = DISTRIBUTOR_PDI
            )

            val paymentBatch = PaymentBatchRequest(
                partnerInfo = partnerInfo,
                payments = listOf(paymentInfo),
                user = USER
            )

            // Act
            receiptOrchestratorService.processPayment(paymentBatch)

            // Assert
            verify { companyService.getCompanyByTenant(TENANT) }
            verify { documentService.generateDocumentNumber(COMPANY_CODE, DocumentType.CX_RECEIPT) }
            verify { `draftReceiptProcessingService`.createDraftReceipt(paymentInfo, partnerInfo, true) }
            verify { receiptProcessingService.createReceipt(mockDraftReceipt, paymentBatch.settlementGroupNumber!!) }
            verify { tradeCreditPaymentService.saveTradeCreditPayment(any()) }
        }

        @Test
        @DisplayName("Should throw exception when company not found")
        fun testProcessPaymentThrowsExceptionWhenCompanyNotFound() {
            // Arrange
            val paymentInfo = PaymentInfo(
                customerTransactionId = PAYMENT_TRANSACTION_ID,
                transactionDate = System.currentTimeMillis(),
                transactionAmount = AMOUNT,
                paymentType = PaymentType.CASH,
                paymentMode = "CASH",
                initiatedBy = "USER"
            )

            val partnerInfo = PartnerInfo(
                partnerId = PARTNER_ID,
                partnerDetailId = PARTNER_DETAIL_ID,
                partnerName = PARTNER_NAME,
                tenant = TENANT,
                distributorPdi = DISTRIBUTOR_PDI
            )

            val paymentBatch = PaymentBatchRequest(
                partnerInfo = partnerInfo,
                payments = listOf(paymentInfo),
                user = USER
            )

            every { companyService.getCompanyByTenant(any()) } returns null

            // Act & Assert
            val exception = assertThrows(RequestException::class.java) {
                receiptOrchestratorService.processPayment(paymentBatch)
            }

            assertEquals("Company not found for tenant $TENANT", exception.message)
        }

        @Test
        @DisplayName("Should set source to RIO_COLLECTIONS for DraftReceipt when source is null")
        fun testProcessPaymentSetsDefaultSourceForDraftReceipt() {
            // Arrange
            val paymentInfo = PaymentInfo(
                customerTransactionId = PAYMENT_TRANSACTION_ID,
                transactionDate = System.currentTimeMillis(),
                transactionAmount = AMOUNT,
                paymentType = PaymentType.CASH,
                paymentMode = "CASH",
                initiatedBy = "USER"
            )

            val partnerInfo = PartnerInfo(
                partnerId = PARTNER_ID,
                partnerDetailId = PARTNER_DETAIL_ID,
                partnerName = PARTNER_NAME,
                tenant = TENANT,
                distributorPdi = DISTRIBUTOR_PDI
            )

            val paymentBatch = PaymentBatchRequest(
                partnerInfo = partnerInfo,
                payments = listOf(paymentInfo),
                user = USER
            )

            val paymentSlot = slot<PaymentInfo>()
            every { `draftReceiptProcessingService`.createDraftReceipt(capture(paymentSlot), any(), any()) } returns mockDraftReceipt

            // Act
            receiptOrchestratorService.processPayment(paymentBatch)

            // Assert
            assertEquals(AdvancePaymentSource.RIO_COLLECTIONS, paymentSlot.captured.paymentSource)
        }

        @Test
        @DisplayName("Should set source to RIO_PAY for PreApprovedReceipt when source is null")
        fun testProcessPaymentSetsDefaultSourceForPreApprovedReceipt() {
            // Arrange
            val paymentInfo = PaymentInfo(
                customerTransactionId = "CASH",
                transactionDate = System.currentTimeMillis(),
                transactionAmount = AMOUNT,
                paymentType = PaymentType.CASH,
                paymentMode = "DIGITAL_RECEIPT",
                initiatedBy = "RIO_DELIVERY"
            )

            val partnerInfo = PartnerInfo(
                partnerId = PARTNER_ID,
                partnerDetailId = PARTNER_DETAIL_ID,
                partnerName = PARTNER_NAME,
                tenant = TENANT,
                distributorPdi = DISTRIBUTOR_PDI
            )

            val paymentBatch = PaymentBatchRequest(
                partnerInfo = partnerInfo,
                payments = listOf(paymentInfo),
                user = USER
            )

            val paymentSlot = slot<PaymentInfo>()
            every { `draftReceiptProcessingService`.createDraftReceipt(capture(paymentSlot), any(), any()) } returns mockDraftReceipt

            // Act
            receiptOrchestratorService.processPayment(paymentBatch)

            // Assert
            assertEquals(AdvancePaymentSource.RIO_PAY, paymentSlot.captured.paymentSource)
        }

        @Test
        @DisplayName("Should use provided source for DraftReceipt")
        fun testProcessPaymentUsesProvidedSourceForDraftReceipt() {
            // Arrange
            val paymentInfo = PaymentInfo(
                customerTransactionId = PAYMENT_TRANSACTION_ID,
                transactionDate = System.currentTimeMillis(),
                transactionAmount = AMOUNT,
                paymentType = PaymentType.CASH,
                paymentMode = "CASH",
                initiatedBy = "USER"
            )

            val partnerInfo = PartnerInfo(
                partnerId = PARTNER_ID,
                partnerDetailId = PARTNER_DETAIL_ID,
                partnerName = PARTNER_NAME,
                tenant = TENANT,
                distributorPdi = DISTRIBUTOR_PDI
            )

            val paymentBatch = PaymentBatchRequest(
                partnerInfo = partnerInfo,
                payments = listOf(paymentInfo),
                user = USER
            )

            val paymentSlot = slot<PaymentInfo>()
            every { `draftReceiptProcessingService`.createDraftReceipt(capture(paymentSlot), any(), any()) } returns mockDraftReceipt

            // Act
            receiptOrchestratorService.processPayment(paymentBatch, AdvancePaymentSource.SYSTEM)

            // Assert
            assertEquals(AdvancePaymentSource.SYSTEM, paymentSlot.captured.paymentSource)
        }

        @Test
        @DisplayName("Should use provided source for DraftReceipt")
        fun testProcessPaymentUsesProvidedSourceForPreApprovedReceipt() {
            // Arrange
            val paymentInfo = PaymentInfo(
                customerTransactionId = PAYMENT_TRANSACTION_ID,
                transactionDate = System.currentTimeMillis(),
                transactionAmount = AMOUNT,
                paymentType = PaymentType.CASH,
                paymentMode = "CASH",
                initiatedBy = "RIO_DELIVERY"
            )

            val partnerInfo = PartnerInfo(
                partnerId = PARTNER_ID,
                partnerDetailId = PARTNER_DETAIL_ID,
                partnerName = PARTNER_NAME,
                tenant = TENANT,
                distributorPdi = DISTRIBUTOR_PDI
            )

            val paymentBatch = PaymentBatchRequest(
                partnerInfo = partnerInfo,
                payments = listOf(paymentInfo),
                user = USER
            )

            val paymentSlot = slot<PaymentInfo>()
            every { `draftReceiptProcessingService`.createDraftReceipt(capture(paymentSlot), any(), any()) } returns mockDraftReceipt

            // Act
            receiptOrchestratorService.processPayment(paymentBatch, AdvancePaymentSource.SYSTEM)

            // Assert
            assertEquals(AdvancePaymentSource.SYSTEM, paymentSlot.captured.paymentSource)
        }
    }
}
