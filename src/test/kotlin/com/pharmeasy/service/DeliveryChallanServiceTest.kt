package com.pharmeasy.service

import com.pharmeasy.data.*
import com.pharmeasy.model.*
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.proxy.WarehouseProxy
import com.pharmeasy.repo.DeliveryChallanLogEntryRepo
import com.pharmeasy.repo.DeliveryChallanMappingRepo
import com.pharmeasy.repo.DeliveryChallanRepo
import com.pharmeasy.repo.DeliveryChallanTaxLogRepo
import com.pharmeasy.repo.read.*
import com.pharmeasy.stream.DcCallBackEventPusher
import com.pharmeasy.type.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import io.mockk.*
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

class DeliveryChallanServiceTest {

    private val warehouseProxy: WarehouseProxy = mockk()
    private val deliveryChallanRepo: DeliveryChallanRepo = mockk()
    private val deliveryChallanReadRepo: DeliveryChallanReadRepo = mockk()
    private val supplierProxy: SupplierProxy = mockk()
    private val fileUploadService: FileUploadService = mockk()
    private val companyService: CompanyService = mockk()
    private val deliveryChallanMappingReadRepo: DeliveryChallanMappingReadRepo = mockk()
    private val deliveryChallanLogEntryReadRepo: DeliveryChallanLogEntryReadRepo = mockk()
    private val deliveryChallanTaxLogReadRepo: DeliveryChallanTaxLogReadRepo = mockk()
    private val deliveryChallanLogEntryRepo: DeliveryChallanLogEntryRepo = mockk()
    private val deliveryChallanMappingRepo: DeliveryChallanMappingRepo = mockk()
    private val deliveryChallanTaxLogRepo: DeliveryChallanTaxLogRepo = mockk()
    private val partnerService: PartnerService = mockk()
    private val dcCallBackEventPusher: DcCallBackEventPusher = mockk()

    private lateinit var deliveryChallanService: DeliveryChallanService

    // Sample test data
    private lateinit var sampleDeliveryChallan: DeliveryChallan
    private lateinit var sampleDeliveryChallanDto: DeliveryChallanDto
    private lateinit var sampleDeliveryChallanLogEntry: DeliveryChallanLogEntry
    private lateinit var sampleDeliveryChallanMapping: DeliveryChallanMapping
    private lateinit var sampleDeliveryChallanTaxLog: DeliveryChallanTaxLog
    private lateinit var sampleSetOffData: SetoffTaxData

    @BeforeEach
    fun setUp() {
        deliveryChallanService = spyk(DeliveryChallanService(warehouseProxy))

        // Manually inject dependencies
        val serviceClass = DeliveryChallanService::class.java
        serviceClass.getDeclaredField("deliveryChallanRepo").apply { isAccessible = true; set(deliveryChallanService, deliveryChallanRepo) }
        serviceClass.getDeclaredField("deliveryChallanReadRepo").apply { isAccessible = true; set(deliveryChallanService, deliveryChallanReadRepo) }
        serviceClass.getDeclaredField("supplierProxy").apply { isAccessible = true; set(deliveryChallanService, supplierProxy) }
        serviceClass.getDeclaredField("fileUploadService").apply { isAccessible = true; set(deliveryChallanService, fileUploadService) }
        serviceClass.getDeclaredField("companyService").apply { isAccessible = true; set(deliveryChallanService, companyService) }
        serviceClass.getDeclaredField("deliveryChallanMappingReadRepo").apply { isAccessible = true; set(deliveryChallanService, deliveryChallanMappingReadRepo) }
        serviceClass.getDeclaredField("deliveryChallanLogEntryReadRepo").apply { isAccessible = true; set(deliveryChallanService, deliveryChallanLogEntryReadRepo) }
        serviceClass.getDeclaredField("deliveryChallanTaxLogReadRepo").apply { isAccessible = true; set(deliveryChallanService, deliveryChallanTaxLogReadRepo) }
        serviceClass.getDeclaredField("deliveryChallanLogEntryRepo").apply { isAccessible = true; set(deliveryChallanService, deliveryChallanLogEntryRepo) }
        serviceClass.getDeclaredField("deliveryChallanMappingRepo").apply { isAccessible = true; set(deliveryChallanService, deliveryChallanMappingRepo) }
        serviceClass.getDeclaredField("deliveryChallanTaxLogRepo").apply { isAccessible = true; set(deliveryChallanService, deliveryChallanTaxLogRepo) }
        serviceClass.getDeclaredField("partnerService").apply { isAccessible = true; set(deliveryChallanService, partnerService) }
        serviceClass.getDeclaredField("dcCallBackEventPusher").apply { isAccessible = true; set(deliveryChallanService, dcCallBackEventPusher) }

        // Initialize test data
        sampleDeliveryChallan = DeliveryChallan(
            id = 1L,
            createdOn = LocalDateTime.now(),
            updatedOn = LocalDateTime.now(),
            createdBy = "SYSTEM",
            updatedBy = null,
            dcNumber = "DC123",
            amount = 1000.0,
            pendingAmount = 1000.0,
            status = DeliveryChallanStatusType.OPEN,
            tenant = "test_tenant",
            partnerDetailId = 123L,
            partnerId = 456L,
            partnerName = "Test Vendor",
            documentDate = LocalDate.now()
        )

        sampleDeliveryChallanDto = DeliveryChallanDto(
            tenant = "test_tenant",
            dcNumber = "DC123",
            dcValue = BigDecimal("1000.00"),
            pdi = 123L,
            items = mutableListOf(
                ItemDto(
                    debitNoteSettlementType = DcSettlementType.AUTO,
                    itemType = DcItemType.SALEABLE,
                    itemValue = 1000.0,
                    gst = 180.0
                )
            ),
            documentDate = LocalDate.now()
        )

        sampleDeliveryChallanLogEntry = DeliveryChallanLogEntry(
            id = 1L,
            createdOn = LocalDateTime.now(),
            createdBy = "SYSTEM",
            amount = 1000.0,
            referenceNumber = "REF123",
            referenceDate = LocalDate.now(),
            referenceAmount = 1000.0,
            referenceNumber2 = null,
            referenceAmount2 = null,
            status = DeliveryChallanLogEntryStatusType.CN_SETOFF,
            remark = "Test remark"
        )

        sampleDeliveryChallanMapping = DeliveryChallanMapping(
            id = 1L,
            createdOn = LocalDateTime.now(),
            deliveryChallanId = 1L,
            deliveryChallanLogId = 1L,
            dcAmountUsed = 1000.0,
            status = DeliveryChallanStatusType.OPEN
        )

        sampleDeliveryChallanTaxLog = DeliveryChallanTaxLog(
            id = 1L,
            deliveryChallanLogId = 1L,
            createdOn = LocalDateTime.now(),
            tax = 18.0,
            taxValue = 180.0,
            taxableValue = 1000.0,
            totalValue = 1180.0,
            hsn = "HSN123"
        )

        sampleSetOffData = SetoffTaxData(
            taxLevel = 18.0,
            taxableAmount = 1000.0,
            taxAmount = 180.0,
            grossAmount = 1180.0
        )
    }

    @Test
    fun `saveDeliveryChallan should not save when delivery challan already exists`() {
        // Given
        every { deliveryChallanReadRepo.getByDcNumberAndTenantAndPartnerDetailId(
            sampleDeliveryChallanDto.dcNumber,
            sampleDeliveryChallanDto.tenant,
            sampleDeliveryChallanDto.pdi
        ) } returns listOf(sampleDeliveryChallan)

        // When
        deliveryChallanService.saveDeliveryChallan(sampleDeliveryChallanDto)

        // Then
        verify(exactly = 0) { deliveryChallanRepo.save(any()) }
    }

    @Test
    fun `getDeliveryChallanBySupplierId should return paginated results`() {
        // Given
        val deliveryChallanDataDto = DeliveryChallanDataDto(
            id = 1L,
            dcNumber = "DC123",
            createdOn = LocalDateTime.now(),
            amount = 1000.0,
            paidAmount = 0.0,
            status = DeliveryChallanStatusType.OPEN,
            documentDate = LocalDate.now()
        )
        val page = PageImpl(listOf(deliveryChallanDataDto), PageRequest.of(0, 10), 1)
        every { companyService.findTenants("test_tenant") } returns mutableListOf("test_tenant")
        every { deliveryChallanReadRepo.getSupplierDeliveryChallanData(
            any(), any(), any(), any(), any(), any(), any(), any(), any()
        ) } returns page

        // When
        val result = deliveryChallanService.getDeliveryChallanBySupplierId(
            123L, "test_tenant", 0, 10, "DC123", LocalDate.now(), LocalDate.now(), 456L, "Test Vendor", DeliveryChallanStatusType.OPEN
        )

        // Then
        assertEquals(1, result.elements)
        assertEquals(1, (result.data as List<*>).size)
    }


    @Test
    fun `getDeliveryChallanHistory should return history data`() {
        // Given
        every { deliveryChallanReadRepo.getOne(1L) } returns sampleDeliveryChallan
        every { deliveryChallanMappingReadRepo.getByDeliveryChallanId(1L) } returns listOf(sampleDeliveryChallanMapping)
        every { deliveryChallanLogEntryReadRepo.getOne(1L) } returns sampleDeliveryChallanLogEntry
        every { deliveryChallanTaxLogReadRepo.getByDeliveryChallanLogId(1L) } returns mutableListOf(sampleSetOffData)
        every { fileUploadService.getDownloadUrl(any(), any()) } returns FileMetaUrlDto(1, mutableListOf())

        // When
        val result = deliveryChallanService.getDeliveryChallanHistory(1L)

        // Then
        assertEquals("Test Vendor", result.partnerName)
        assertEquals(1000.0, result.dcAmount)
        assertEquals(0.0, result.setoffAmount)
        assertEquals(1, result.data.size)
    }

    @Test
    fun `getDeliveryChallanHistoryByDc should return history data`() {
        // Given
        every { deliveryChallanReadRepo.getByDcNumberAndTenantAndPartnerDetailId(
            "DC123", "test_tenant", 123L
        ) } returns listOf(sampleDeliveryChallan)
        every { deliveryChallanMappingReadRepo.getByDeliveryChallanId(1L) } returns listOf(sampleDeliveryChallanMapping)
        every { deliveryChallanLogEntryReadRepo.getOne(1L) } returns sampleDeliveryChallanLogEntry
        every { deliveryChallanTaxLogReadRepo.getByDeliveryChallanLogId(1L) } returns mutableListOf(sampleSetOffData)
        every { fileUploadService.getDownloadUrl(any(), any()) } returns FileMetaUrlDto(1, mutableListOf())
        every { warehouseProxy.getUserByUserIds(any()) } returns listOf(User("SYSTEM", "System User", "SYSTEM", ""))
        every { deliveryChallanReadRepo.getOne(1L) } returns sampleDeliveryChallan

        // When
        val result = deliveryChallanService.getDeliveryChallanHistoryByDc("DC123", "test_tenant", 123L)

        // Then
        assertEquals("Test Vendor", result.partnerName)
        assertEquals(1000.0, result.dcAmount)
        assertEquals(0.0, result.setoffAmount)
        assertEquals(1, result.data.size)
    }

    @Test
    fun `getDocumentsByDc should return document URLs`() {
        // Given
        every { deliveryChallanReadRepo.getByDcNumberAndTenantAndPartnerDetailId(
            "DC123", "test_tenant", 123L
        ) } returns listOf(sampleDeliveryChallan)
        every { deliveryChallanMappingReadRepo.getByDeliveryChallanId(1L) } returns listOf(sampleDeliveryChallanMapping)
        every { fileUploadService.getDownloadUrl(any(), any()) } returns FileMetaUrlDto(1L, mutableListOf())

        // When
        val result = deliveryChallanService.getDocumentsByDc("DC123", "test_tenant", 123L)

        // Then
        assertEquals(1, result.size)
    }
} 
