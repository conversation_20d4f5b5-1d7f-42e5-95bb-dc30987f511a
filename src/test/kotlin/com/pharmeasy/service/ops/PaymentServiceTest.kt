package com.pharmeasy.service.ops

import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.Supplier
import com.pharmeasy.model.ops.RIOPaymentDTO
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.service.CompanyService
import com.pharmeasy.service.InvoiceService
import com.pharmeasy.service.ReceiptOrchestratorService
import com.pharmeasy.service.TradeCreditPaymentService
import com.pharmeasy.util.EventPublisherUtil
import com.pharmeasy.utils.DataProvider
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertDoesNotThrow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class PaymentServiceTest {

    private lateinit var paymentService: PaymentService
    private lateinit var receiptOrchestratorService: ReceiptOrchestratorService
    private lateinit var invoiceService: InvoiceService
    private lateinit var supplierProxy: SupplierProxy
    private lateinit var eventPublisherUtil: EventPublisherUtil
    private lateinit var tradeCreditPaymentService: TradeCreditPaymentService
    private lateinit var companyService: CompanyService

    private lateinit var mockSupplier: Supplier

    @BeforeEach
    fun setUp() {
        // Initialize all mocks
        receiptOrchestratorService = mockk(relaxed = true)
        invoiceService = mockk(relaxed = true)
        supplierProxy = mockk()
        eventPublisherUtil = mockk(relaxed = true)
        tradeCreditPaymentService = mockk(relaxed = true)
        companyService = mockk()

        // Create the service with mocked dependencies
        paymentService = spyk(
            PaymentService(
                receiptOrchestratorService,
                invoiceService,
                supplierProxy,
                eventPublisherUtil,
                tradeCreditPaymentService,
                companyService
            )
        )

        mockSupplier = Supplier(
            partnerId = 123L,
            partnerName = "Test Supplier",
            partnerDetailList = mutableListOf(),
            firmTypes = mutableListOf(),
            retailerCommercial = null,
            distributorCommercial = null
        )

        // Default mock behaviors
        every { supplierProxy.supplier(null, any()) } returns listOf(DataProvider.supplier())
        every { paymentService.getTenantByDistributorId(any(), any()) } returns "test-tenant"
    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    @Nested
    @DisplayName("processRioPaymentV2 Tests")
    inner class ProcessRioPaymentV2Tests {

        @Test
        @DisplayName("Should process valid payment successfully")
        fun testProcessRioPaymentV2WithValidSupplierAndTenant() {
            // Arrange
            fun test(rioPaymentDTO: RIOPaymentDTO) {
                val rioPaymentDTOList = listOf(rioPaymentDTO)

                assertDoesNotThrow {
                    paymentService.processRioPaymentV2(rioPaymentDTOList)
                }

                // Verify interactions
                verify { supplierProxy.supplier(null, DataProvider.PDI) }
                verify { receiptOrchestratorService.processPayment(any()) }
                verify {
                    eventPublisherUtil.sendPaymentTransactionUpdates(withArg {
                        assertTrue {
                            it.data.size == 1 && it.data[0].referenceId == rioPaymentDTO.retailerTxnId
                        }
                    })
                }
                verify(exactly = 0) { paymentService.handleTradeCreditRepayment(any(), any()) }
            }
            test(DataProvider.rioPaymentDto())
            test(DataProvider.chequePaymentDto())
            test(DataProvider.chequePaymentDto().apply { chequeDate = null })
            test(DataProvider.neftPaymentDto())
            test(DataProvider.advanceRioPaymentDto())
            test(DataProvider.advanceRioPaymentDto().apply { invoiceNumber = "" })

        }


        @Test
        @DisplayName("Should partition trade credit repayments correctly")
        fun testProcessRioPaymentV2PartitionsTradeCreditRepayments() {
            // Arrange
            val tradeCreditRePayment = DataProvider.tradeCreditRepaymentDTO().apply { retailerTxnAmount = 800.0 }
            val regularPayment = DataProvider.rioPaymentDto(retailerTxnAmount = 200.0)
            val rioPaymentDTOList = listOf(tradeCreditRePayment, regularPayment)

            // Act
            assertDoesNotThrow {
                paymentService.processRioPaymentV2(rioPaymentDTOList)
            }

            // Verify trade credit repayment was handled
            // Verify interactions
            verify { supplierProxy.supplier(null, DataProvider.PDI) }
            verify { receiptOrchestratorService.processPayment(any()) }
            verify {
                eventPublisherUtil.sendPaymentTransactionUpdates(withArg {
                    assertTrue {
                        it.data.size == 2
                            && it.data.map { it.referenceId }.toSet()
                            .containsAll(rioPaymentDTOList.map { it.retailerTxnId!! })
                    }
                })
            }
            verify(exactly = 1) {
                paymentService.handleTradeCreditRepayment(withArg {
                    it.category == "CREDIT_REPAYMENT"
                }, any())
            }
        }

        @Test
        @DisplayName("Should handle only trade credit repayments")
        fun testProcessRioPaymentV2OnlyTradeCreditRepayments() {
            // Arrange
            val tradeCreditPayment = DataProvider.tradeCreditRepaymentDTO()
            val rioPaymentDTOList = listOf(tradeCreditPayment)

            // Act
            assertDoesNotThrow {
                paymentService.processRioPaymentV2(rioPaymentDTOList)
            }

            // Verify trade credit repayments were handled but no regular payment processing
            verify(exactly = 1) {
                paymentService.handleTradeCreditRepayment(withArg {
                    it.category == "CREDIT_REPAYMENT"
                }, any())
            }
            verify(exactly = 0) { receiptOrchestratorService.processPayment(any()) }
        }

        @Test
        @DisplayName("Should skip processing when supplier not found")
        fun testProcessRioPaymentV2ThrowsExceptionWhenSupplierNotFound() {
            // Arrange
            val rioPaymentDTO = DataProvider.rioPaymentDto()
            val rioPaymentDTOList = listOf(rioPaymentDTO)

            every { supplierProxy.supplier(null, any()) } returns emptyList()

            // Act & Assert
            val exception = assertThrows(RequestException::class.java) {
                paymentService.processRioPaymentV2(rioPaymentDTOList)
            }

            assertEquals("Partner not found", exception.message)
        }

        @Test
        @DisplayName("Should throw exception when tenant not found")
        fun testProcessRioPaymentV2ThrowsExceptionWhenTenantNotFound() {
            val rioPaymentDTO = DataProvider.rioPaymentDto()
            val rioPaymentDTOList = listOf(rioPaymentDTO)

            every { paymentService.getTenantByDistributorId(any(), any()) } returns null

            val exception = assertThrows(RequestException::class.java) {
                paymentService.processRioPaymentV2(rioPaymentDTOList)
            }

            assertTrue(exception.message!!.contains("Tenant not found"))
        }

        @Test
        @DisplayName("Should throw exception for empty payment list")
        fun testProcessRioPaymentV2HandlesEmptyPaymentList() {
            val rioPaymentDTOList = emptyList<RIOPaymentDTO>()

            assertDoesNotThrow {
                paymentService.processRioPaymentV2(rioPaymentDTOList)
            }

            verify(exactly = 0) { supplierProxy.supplier(null, any()) }
        }
    }

    @Nested
    @DisplayName("Validation Tests")
    inner class ValidationTests {

        @Test
        @DisplayName("Should throw exception when retailerTxnId is null")
        fun testValidateRequestThrowsExceptionForNullRetailerTxnId() {
            // Arrange
            val rioPaymentDTO = DataProvider.rioPaymentDto().copy(retailerTxnId = null)
            val rioPaymentDTOList = listOf(rioPaymentDTO)

            // Act & Assert
            val exception = assertThrows(IllegalArgumentException::class.java) {
                paymentService.processRioPaymentV2(rioPaymentDTOList)
            }

            assertEquals("Transaction Id is required", exception.message)
        }

        @Test
        @DisplayName("Should throw exception when partnerDetailId is null")
        fun testValidateRequestThrowsExceptionForNullPartnerDetailId() {
            // Arrange
            val rioPaymentDTO = DataProvider.rioPaymentDto().copy(retailerFrontEndPartyCode = null)
            val rioPaymentDTOList = listOf(rioPaymentDTO)

            // Act & Assert
            val exception = assertThrows(IllegalArgumentException::class.java) {
                paymentService.processRioPaymentV2(rioPaymentDTOList)
            }

            assertEquals("Partner Detail Id is required", exception.message)
        }

        @Test
        @DisplayName("Should throw exception when distributorId is null")
        fun testValidateRequestThrowsExceptionForNullDistributorId() {
            // Arrange
            val rioPaymentDTO = DataProvider.rioPaymentDto().copy(distributorId = null)
            val rioPaymentDTOList = listOf(rioPaymentDTO)

            // Act & Assert
            val exception = assertThrows(IllegalArgumentException::class.java) {
                paymentService.processRioPaymentV2(rioPaymentDTOList)
            }

            assertEquals("Distributor ID is required", exception.message)
        }

        @Test
        @DisplayName("Should throw exception when retailerTxnDate is null")
        fun testValidateRequestThrowsExceptionForNullRetailerTxnDate() {
            // Arrange
            val rioPaymentDTO = DataProvider.rioPaymentDto().copy(retailerTxnDate = null)
            val rioPaymentDTOList = listOf(rioPaymentDTO)

            // Act & Assert
            val exception = assertThrows(IllegalArgumentException::class.java) {
                paymentService.processRioPaymentV2(rioPaymentDTOList)
            }

            assertEquals("Retailer transaction date is required", exception.message)
        }

        @Test
        @DisplayName("Should throw exception when retailerTotalTxnAmount is null")
        fun testValidateRequestThrowsExceptionNullTotalAmount() {
            // Arrange
            val rioPaymentDTO = DataProvider.rioPaymentDto().copy(
                retailerTotalTxnAmount = null
            )
            val rioPaymentDTOList = listOf(rioPaymentDTO)

            // Act & Assert
            val exception = assertThrows(IllegalArgumentException::class.java) {
                paymentService.processRioPaymentV2(rioPaymentDTOList)
            }

            assertEquals("Retailer transaction amount is required", exception.message)
        }

        @Test
        @DisplayName("Should throw exception when retailerTxnAmount exceeds retailerTotalTxnAmount")
        fun testValidateRequestThrowsExceptionForInvalidAmounts() {
            // Arrange
            val rioPaymentDTO = DataProvider.rioPaymentDto().copy(
                retailerTxnAmount = 200.0,
                retailerTotalTxnAmount = 100.0
            )
            val rioPaymentDTOList = listOf(rioPaymentDTO)

            // Act & Assert
            val exception = assertThrows(IllegalArgumentException::class.java) {
                paymentService.processRioPaymentV2(rioPaymentDTOList)
            }

            assertEquals(
                "Retailer transaction amount must be less than or equal to the total transaction amount",
                exception.message
            )
        }

        @Test
        @DisplayName("Should throw exception when invoice has null retailerTxnAmount")
        fun testValidateRequestThrowsExceptionForInvoiceWithNullAmount() {
            // Arrange
            val rioPaymentDTO = DataProvider.rioPaymentDto().copy(
                retailerTxnAmount = null
            )
            val rioPaymentDTOList = listOf(rioPaymentDTO)

            // Act & Assert
            val exception = assertThrows(IllegalArgumentException::class.java) {
                paymentService.processRioPaymentV2(rioPaymentDTOList)
            }

            assertEquals("Transaction amount is required for invoice/DN", exception.message)
        }

        @Test
        @DisplayName("Should throw exception when invoice has negative retailerTxnAmount")
        fun testValidateRequestThrowsExceptionForNegativeAmount() {
            // Arrange
            val rioPaymentDTO = DataProvider.rioPaymentDto().copy(
                invoiceNumber = "INV001",
                retailerTxnAmount = -50.0
            )
            val rioPaymentDTOList = listOf(rioPaymentDTO)

            // Act & Assert
            val exception = assertThrows(IllegalArgumentException::class.java) {
                paymentService.processRioPaymentV2(rioPaymentDTOList)
            }

            assertEquals("Transaction amount must be positive", exception.message)
        }
    }
}
