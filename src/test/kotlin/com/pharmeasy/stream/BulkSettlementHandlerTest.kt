package com.pharmeasy.stream

import com.pharmeasy.data.BulkSettlementFileValidationDto
import com.pharmeasy.data.ops.BulkSettlementFileProcess
import com.pharmeasy.model.ProcessSettlementTaskDto
import com.pharmeasy.service.SettlementFileValidator
import com.pharmeasy.service.SettlementTaskProcessor
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.SelectionType
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*

class BulkSettlementHandlerTest {

    @MockK
    private lateinit var settlementFileValidator: SettlementFileValidator
    @MockK
    private lateinit var settlementTaskProcessor: SettlementTaskProcessor

    private lateinit var bulkSettlementHandler: BulkSettlementHandler

    private lateinit var sampleValidationDto: BulkSettlementFileValidationDto
    private lateinit var sampleProcessDto: BulkSettlementFileProcess

    @BeforeEach
    fun setUp() {
        // Initialize MockK annotations
        io.mockk.MockKAnnotations.init(this)

        // Instantiate and inject mocks manually
        bulkSettlementHandler = BulkSettlementHandler().apply {
            // Use setter injection to avoid reflection
            val validatorField = BulkSettlementHandler::class.java.getDeclaredField("settlementFileValidator")
            validatorField.isAccessible = true
            validatorField.set(this, settlementFileValidator)

            val processorField = BulkSettlementHandler::class.java.getDeclaredField("settlementTaskProcessor")
            processorField.isAccessible = true
            processorField.set(this, settlementTaskProcessor)
        }

        sampleValidationDto = BulkSettlementFileValidationDto(
            key = "test-uri",
            taskId = 1L,
            ds = null
        )

        sampleProcessDto = BulkSettlementFileProcess(
            processSettlementTaskDto = ProcessSettlementTaskDto(
                settlementMode = PaymentType.CASH,
                taskId = 1L,
                taskItemIdList = listOf(100L, 200L),
                selectionType = SelectionType.ALL
            ),
            user = "test_user",
            ds = "ds1",
            tenant = "test_tenant",
            userHash = "test_hash"
        )
    }

    @Test
    fun `bulkSettlementFileValidationConsumer should handle validation successfully`() {
        // Given
        every { settlementFileValidator.validateFile("test-uri", 1L, null) } just runs

        // When
        bulkSettlementHandler.bulkSettlementFileValidationConsumer(sampleValidationDto)

        // Then
        verify(exactly = 1) {
            settlementFileValidator.validateFile(
                sampleValidationDto.key,
                sampleValidationDto.taskId,
                sampleValidationDto.ds
            )
        }
    }

    @Test
    fun `bulkSettlementFileProcessConsumer should handle process successfully`() {
        // Given
        every { settlementTaskProcessor.processTask(any(), any(), any(), any(), any()) } just runs

        // When
        bulkSettlementHandler.bulkSettlementFileProcessConsumer(sampleProcessDto)

        // Then
        verify(exactly = 1) {
            settlementTaskProcessor.processTask(
                sampleProcessDto.processSettlementTaskDto,
                sampleProcessDto.user,
                sampleProcessDto.ds,
                sampleProcessDto.tenant,
                sampleProcessDto.userHash
            )
        }
    }
}
